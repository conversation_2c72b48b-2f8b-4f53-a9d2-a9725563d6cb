<!--
 * @Description: 录音测试页面
 * @Author: AI Assistant
 * @Date: 2025-07-11
-->
<template>
	<view class="record-test-container">
		<!-- 页面标题 -->
		<view class="page-title">录音测试页面</view>

		<!-- 录音状态显示 -->
		<view class="record-status" v-if="recordData.tempFilePath">
			<text class="status-text">录音完成！时长：{{ recordData.showTime }}</text>
			<view class="audio-info">
				<text>文件路径：{{ recordData.tempFilePath }}</text>
			</view>
		</view>

		<!-- 保存状态显示 -->
		<view class="save-status" v-if="saveStatus">
			<text class="save-text" :class="saveStatus.type">{{ saveStatus.message }}</text>
		</view>

		<!-- 悬浮录音按钮 - 右下角 -->
		<view class="floating-record-btn">
			<xiaopao-record
				:mode="1"
				startTxt="长按录音"
				recordTxt="松手保存，上移取消"
				@cbResult="handleRecordResult"
				@cbClearResult="handleClearResult"
				@cbPermissionTips="handlePermissionTips"
			></xiaopao-record>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-buttons" v-if="recordData.tempFilePath">
			<button class="save-btn" @click="saveRecordFile" :disabled="saving">
				{{ saving ? '保存中...' : '保存录音文件' }}
			</button>
			<button class="clear-btn" @click="clearRecord">清除录音</button>
		</view>

		<!-- 使用说明 -->
		<view class="instructions">
			<view class="instruction-title">使用说明：</view>
			<view class="instruction-item">1. 点击右下角蓝色悬浮按钮</view>
			<view class="instruction-item">2. 长按开始录音（至少0.5秒）</view>
			<view class="instruction-item">3. 松手结束录音</view>
			<view class="instruction-item">4. 向上滑动可取消录音</view>
			<view class="instruction-item">5. 录音完成后可点击保存按钮保存文件</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "RecordTest",
	data() {
		return {
			// 录音数据
			recordData: {
				tempFilePath: '',
				showTime: '',
				duration: 0,
				tempFile: null
			},
			// 保存状态
			saveStatus: null,
			// 是否正在保存
			saving: false
		};
	},
	methods: {
		/**
		 * 处理录音结果回调
		 * @param {Object} result 录音结果对象
		 */
		handleRecordResult(result) {
			console.log('录音结果：', result);
			this.recordData = { ...result };

			// 显示录音成功提示
			uni.showToast({
				title: `录音完成 ${result.showTime}`,
				icon: 'success',
				duration: 2000
			});

			// 清除之前的保存状态
			this.saveStatus = null;
		},

		/**
		 * 处理清除录音结果回调
		 * @param {Object} result 清除结果对象
		 */
		handleClearResult(result) {
			console.log('清除录音结果：', result);
			this.recordData = { ...result };
			this.saveStatus = null;
		},

		/**
		 * 处理权限提示回调
		 */
		handlePermissionTips() {
			console.log('权限提示回调');
			uni.showModal({
				title: '权限提示',
				content: '录音功能需要麦克风权限，请在系统设置中开启',
				showCancel: false
			});
		},

		/**
		 * 保存录音文件到本地
		 */
		async saveRecordFile() {
			if (!this.recordData.tempFilePath) {
				uni.showToast({
					title: '没有录音文件',
					icon: 'none'
				});
				return;
			}

			this.saving = true;
			this.saveStatus = {
				type: 'loading',
				message: '正在保存录音文件...'
			};

			try {
				// 生成文件名（使用时间戳）
				const timestamp = new Date().getTime();
				const fileName = `record_${timestamp}.mp3`;

				// 保存文件到本地
				const savedFilePath = await this.saveFileToLocal(this.recordData.tempFilePath, fileName);

				this.saveStatus = {
					type: 'success',
					message: `文件已保存：${savedFilePath}`
				};

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});

			} catch (error) {
				console.error('保存文件失败：', error);
				this.saveStatus = {
					type: 'error',
					message: `保存失败：${error.message || '未知错误'}`
				};

				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			} finally {
				this.saving = false;
			}
		},

		/**
		 * 保存文件到本地存储
		 * @param {String} tempFilePath 临时文件路径
		 * @param {String} fileName 保存的文件名
		 * @returns {Promise<String>} 保存后的文件路径
		 */
		saveFileToLocal(tempFilePath, fileName) {
			return new Promise((resolve, reject) => {
				// #ifdef APP-PLUS
				// App端保存到应用沙盒目录
				const fs = uni.getFileSystemManager();
				const savedPath = `${uni.env.USER_DATA_PATH}/${fileName}`;

				fs.copyFile({
					srcPath: tempFilePath,
					destPath: savedPath,
					success: () => {
						resolve(savedPath);
					},
					fail: (error) => {
						reject(error);
					}
				});
				// #endif

				// #ifdef MP-WEIXIN
				// 微信小程序保存到本地
				uni.saveFile({
					tempFilePath: tempFilePath,
					success: (res) => {
						resolve(res.savedFilePath);
					},
					fail: (error) => {
						reject(error);
					}
				});
				// #endif

				// #ifdef H5
				// H5端不支持录音，直接返回错误
				reject(new Error('H5端不支持录音功能'));
				// #endif
			});
		},

		/**
		 * 清除录音数据
		 */
		clearRecord() {
			this.recordData = {
				tempFilePath: '',
				showTime: '',
				duration: 0,
				tempFile: null
			};
			this.saveStatus = null;

			uni.showToast({
				title: '已清除录音',
				icon: 'success'
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.record-test-container {
	padding: 40rpx;
	min-height: 100vh;
	background-color: #f8f9fa;
	position: relative;
}

.page-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 60rpx;
}

.record-status {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	.status-text {
		font-size: 32rpx;
		color: #2979ff;
		font-weight: bold;
		display: block;
		margin-bottom: 20rpx;
	}

	.audio-info {
		font-size: 24rpx;
		color: #666;
		word-break: break-all;
	}
}

.save-status {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	.save-text {
		font-size: 28rpx;
		display: block;

		&.loading {
			color: #ff9800;
		}

		&.success {
			color: #4caf50;
		}

		&.error {
			color: #f44336;
		}
	}
}

.floating-record-btn {
	position: fixed;
	right: 40rpx;
	bottom: 120rpx;
	z-index: 999;
	background-color: #2979ff;
	border-radius: 50%;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.3);
}

.action-buttons {
	display: flex;
	gap: 30rpx;
	margin-bottom: 60rpx;

	button {
		flex: 1;
		height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		border: none;
	}

	.save-btn {
		background-color: #4caf50;
		color: white;

		&:disabled {
			background-color: #ccc;
		}
	}

	.clear-btn {
		background-color: #ff5722;
		color: white;
	}
}

.instructions {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	.instruction-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.instruction-item {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 12rpx;
		padding-left: 20rpx;
		position: relative;

		&::before {
			content: '•';
			color: #2979ff;
			position: absolute;
			left: 0;
		}
	}
}
</style>
