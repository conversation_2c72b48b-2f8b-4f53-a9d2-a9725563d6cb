<!--
 * @Description: 小助理首页 - 科技蓝风格设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
-->
<template>
  <view class="home-container" :style="{ backgroundImage: `url(${bgIndex})` }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <!-- 模式切换按钮组 -->
        <view class="mode-switch">
          <view
            class="switch-item"
            :class="{ active: currentMode === 'chat' }"
            @click="switchMode('chat')"
          >
            <text class="switch-icon">💬</text>
            <text>会话模式</text>
          </view>
          <view
            class="switch-item"
            :class="{ active: currentMode === 'system' }"
            @click="switchMode('system')"
          >
            <text class="switch-icon">🏠</text>
            <text>系统模式</text>
          </view>
        </view>
      </view>
      <!-- 导航栏下方投影分割线 -->
      <view class="navbar-shadow"></view>
    </view>

    <!-- 顶部欢迎区域 -->
    <view class="header-section">
      <view class="welcome-card">
        <view class="welcome-content">
          <view class="greeting-text">
            <text class="greeting-main">{{ getGreeting() }}</text>
            <text class="greeting-sub">让AI成为您的智能助手</text>
          </view>
          <view class="avatar-section" @click="navigateToUser">
            <u-avatar
              @click="navigateToUser"
              :src="userAvatar"
              size="60"
              color="#ffffff"
              text="AI"
              bg-color="rgba(255, 255, 255, 0.2)"
              :random-bg-color="false"
            ></u-avatar>
          </view>
        </view>

        <!-- 快速统计 -->
        <view class="stats-row">
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.tasks }}</text>
            <text class="stat-label">待办事项</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.accounts }}</text>
            <text class="stat-label">记账笔数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ memoryCount }}</text>
            <text class="stat-label">记忆数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ todayStats.accounts }}</text>
            <text class="stat-label">知识库</text>
          </view>
        </view>
      </view>
    </view>



    <!-- 主要功能入口 -->
    <view class="main-actions">
      <view class="actions-list">
        <!-- 待办事项 - 增强版 -->
        <view class="action-item task-item enhanced-task">
          <view class="task-main-content" @click="navigateToTask">
            <view class="item-icon">
              <text class="icon-emoji">📋</text>
            </view>
            <view class="item-content">
              <text class="item-title">待办</text>
            </view>
            <!-- 待办统计信息 -->
            <view class="task-stats">
              <view class="stat-item">
                <text class="stat-number">{{ taskStats.pending }}</text>
                <text class="stat-label">未完成</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{ taskStats.completed }}</text>
                <text class="stat-label">已完成</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{ taskStats.priority }}</text>
                <text class="stat-label">高紧急</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{ taskStats.urgent }}</text>
                <text class="stat-label">临期</text>
              </view>
            </view>
          </view>

          <!-- 快速操作按钮组 -->
          <view class="quick-actions">
            <view class="quick-btn quick-add" @click="quickAddTask">
              <text class="quick-icon">➕</text>
              <text class="quick-text">快速创建</text>
            </view>
            <view class="quick-btn quick-voice" @click="quickVoiceTask">
              <text class="quick-icon">🎤</text>
              <text class="quick-text">语音创建</text>
            </view>
            <view class="quick-btn quick-ai" @click="quickAiTask">
              <text class="quick-icon">⚡</text>
              <text class="quick-text">AI识别</text>
            </view>
          </view>
        </view>

        <!-- 记账工具 - 增强版 -->
        <view class="action-item accounting-item enhanced-task">
          <view class="task-main-content" @click="navigateToAccounting">
            <view class="item-icon">
              <text class="icon-emoji">💰</text>
            </view>
            <view class="item-content">
              <text class="item-title">记账</text>
            </view>
            <!-- 记账统计信息 -->
            <view class="task-stats accounting-stats">
              <view class="stat-item expense-stat">
                <view class="stat-number">
                  <text class="currency-symbol">￥</text>{{ formatAmount(accountingStats.monthExpense) }}
                </view>
                <text class="stat-label">本月支出</text>
              </view>
              <view class="stat-item income-stat">
                <view class="stat-number">
                  <text class="currency-symbol">￥</text>{{ formatAmount(accountingStats.monthIncome) }}
                </view>
                <text class="stat-label">本月收入</text>
              </view>
            </view>
          </view>

          <!-- 快速操作按钮组 -->
          <view class="quick-actions">
            <view class="quick-btn quick-add" @click="quickAddAccount">
              <text class="quick-icon">➕</text>
              <text class="quick-text">快速创建</text>
            </view>
            <view class="quick-btn quick-voice" @click="quickVoiceAccount">
              <text class="quick-icon">🎤</text>
              <text class="quick-text">语音创建</text>
            </view>
            <view class="quick-btn quick-ai" @click="quickAiAccount">
              <text class="quick-icon">⚡</text>
              <text class="quick-text">AI识别</text>
            </view>
          </view>
        </view>

        <!-- 忆往昔 -->
        <view class="action-item memory-item" @click="navigateToMemory">
          <view class="item-icon">
            <text class="icon-emoji">💭</text>
          </view>
          <view class="item-content">
            <text class="item-title">忆往昔</text>
            <text class="item-desc">记录想法创意，保存美好瞬间</text>
          </view>
          <view class="item-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>

        <!-- 知识库 -->
        <view class="action-item wiki-item" @click="navigateToWiki">
          <view class="item-icon">
            <text class="icon-emoji">📁</text>
          </view>
          <view class="item-content">
            <text class="item-title">知识库</text>
            <text class="item-desc">智能检索对话，知识问答助手</text>
          </view>
          <view class="item-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 右下角悬停对话按钮 - 可拖拽 -->
    <xj-float-button
    icon="💬"
    @click="aiChat"
  />
  </view>
</template>

<script>
import bgIndex from '@/static/bg_index.js'
import XjFloatButton from "@/components/xj-float-button/index.vue";
export default {
  name: "HomePage",
  components: {
    XjFloatButton,
  },
  data() {
    return {
      bgIndex: bgIndex, // 背景图片base64
      statusBarHeight: 0,
      currentMode: 'system', // 'chat' | 'system'，默认显示系统模式
      userAvatar: '', // 用户头像，为空时显示AI文字
      // 今日统计数据
      todayStats: {
        memories: 0,
        tasks: 0,
        accounts: 0
      },
      // 各模块统计
      memoryCount: 0,
      wikiCount: 0,
      taskCount: 0,
      accountingAmount: 0,
      // 待办统计数据
      taskStats: {
        pending: 0,    // 未完成
        completed: 0, // 已完成
        priority: 0,   // 高和紧急级别
        urgent: 0,     // 2天内到期
        total: 0      // 总数
      },
      // 记账统计数据
      accountingStats: {
        monthExpense: 0,     // 本月支出
        monthIncome: 0,      // 本月收入
        totalRecords: 0       // 记账总数
      },
      // 悬浮按钮位置和拖拽状态
      floatingBtnPosition: {
        right: 40,    // 距离右边的距离
        bottom: 40    // 距离底部的距离
      },
      isDragging: false,      // 是否正在拖拽
      startPosition: {        // 拖拽开始位置
        x: 0,
        y: 0
      },
      startBtnPosition: {     // 按钮拖拽开始时的位置
        right: 40,
        bottom: 40
      }
    };
  },

  onLoad() {
    this.initPage();
    // this.loadStats();
    this.initFloatingBtn();
  },

  onShow() {
    this.loadStats();
  },

  methods: {
    // 导航到用户页面
    navigateToUser() {
      uni.navigateTo({
        url: '/pages_user/index/index'
      });
    },

    // 初始化页面
    initPage() {
      // 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 0
    },

    // 初始化悬浮按钮位置
    initFloatingBtn() {
      // 从本地存储读取上次保存的位置
      try {
        const savedPosition = uni.getStorageSync('floatingBtnPosition');
        if (savedPosition) {
          this.floatingBtnPosition = savedPosition;
        }
      } catch (e) {
        console.log('读取悬浮按钮位置失败:', e);
      }
    },

    // 保存悬浮按钮位置到本地存储
    saveFloatingBtnPosition() {
      try {
        uni.setStorageSync('floatingBtnPosition', this.floatingBtnPosition);
      } catch (e) {
        console.log('保存悬浮按钮位置失败:', e);
      }
    },

    // 切换模式
    switchMode(mode) {
      if (mode === 'chat') {
        // 跳转到聊天页面
        this.navigateToChat();
      } else {
        this.currentMode = mode;
      }
    },

    // 获取问候语
    getGreeting() {
      const hour = new Date().getHours();
      if (hour < 6) return "夜深了，注意休息";
      if (hour < 9) return "早上好";
      if (hour < 12) return "上午好";
      if (hour < 14) return "中午好";
      if (hour < 18) return "下午好";
      if (hour < 22) return "晚上好";
      return "夜深了，注意休息";
    },

    // 加载统计数据
    async loadStats() {
      try {
        // 并行请求记账模块和待办事项模块的统计数据
        const [accountingRes, taskRes] = await Promise.all([
          this.loadAccountingStats(),
          this.loadTaskStats(),
          this.loadMemoryStats()
        ]);

        // 更新首页统计显示
        this.updateStatsDisplay(accountingRes, taskRes);
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 使用默认数据
        this.setDefaultStats();
        // 提示用户网络问题（可选）
        // uni.showToast({
        //   title: '数据加载失败，显示默认数据',
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    },

    // 加载记账模块统计数据
    async loadAccountingStats() {
      try {
        const res = await this.$ajax.get('/accounting/home/<USER>');
        if (res?.code === 200 && res.data) {
          // 更新记账统计数据
          this.accountingStats = {
            monthExpense: res.data.monthExpense || 0,
            monthIncome: res.data.monthIncome || 0,
            totalRecords: res.data.totalRecords || 0
          };
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取记账统计数据失败:', error);
        return null;
      }
    },

    // 加载Memory模块统计数据
    async loadMemoryStats() {
      try {
        const res = await this.$ajax.get('/memory/home/<USER>');
        if (res?.code == 200 && res.data) {
          this.memoryCount = res.data?.dataCount || 0;
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取记忆统计数据失败:', error);
        return null;
      }
    },

    // 加载待办事项模块统计数据
    async loadTaskStats() {
      try {
        const res = await this.$ajax.get('/task/home/<USER>');
        if (res?.code === 200 && res.data) {
          // 更新待办统计数据
          this.taskStats = {
            pending: res.data.unfinishedTasks || 0,
            completed: res.data.completedTasks || 0,
            priority: res.data.highUrgentTasks || 0,
            urgent: res.data.expiringSoonTasks || 0,
            total: res.data.totalTasks || 0
          };
          return res.data;
        }
        return null;
      } catch (error) {
        console.error('获取待办统计数据失败:', error);
        return null;
      }
    },

    // 更新统计显示
    updateStatsDisplay(accountingData, taskData) {
      // 更新今日统计显示
      this.todayStats = {
        memories: 3, // 记忆模块暂时保持默认值
        tasks: taskData?.unfinishedTasks || 0,
        accounts: accountingData?.totalRecords || 0
      };

      // 更新各模块计数
      this.taskCount = taskData?.totalTasks || 0;
      this.accountingAmount = accountingData?.monthExpense || 0;
    },

    // 设置默认统计数据
    setDefaultStats() {
      this.todayStats = {
        memories: 0,
        tasks: 0,
        accounts: 0
      };
      this.taskStats = {
        pending: 0,
        completed: 0,
        priority: 0,
        urgent: 0,
        total: 0
      };
      this.accountingStats = {
        monthExpense: 0,
        monthIncome: 0,
        totalRecords: 0
      };
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return '0.00';
      }

      // 确保是数字类型
      const num = Number(amount);

      // 格式化为两位小数
      return num.toFixed(2);
    },

    // 手动刷新统计数据
    async refreshStats() {
      try {
        uni.showLoading({
          title: '正在刷新...'
        });
        await this.loadStats();
        uni.hideLoading();
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '刷新失败',
          icon: 'error',
          duration: 2000
        });
      }
    },

    // 导航到记忆模块
    navigateToMemory() {
      uni.navigateTo({
        url: '/pages_memory/index/index'
      });
    },

    // 导航到知识库模块（待开发）
    navigateToWiki() {
      uni.navigateTo({
        url: '/pages_wiki/index/index'
      });
    },

    // 导航到待办模块
    navigateToTask() {
      uni.navigateTo({
        url: '/pages_task/index/index'
      });
    },

    // 导航到记账模块（待开发）
    navigateToAccounting() {
      uni.navigateTo({
        url: '/pages_accounting/index/index'
      });
    },

    // 快速添加记忆
    quickAddMemory() {
      uni.navigateTo({
        url: '/pages_memory/detail/index'
      });
    },

    // 快速添加待办
    quickAddTask() {
      uni.navigateTo({
        url: '/pages_task/detail/save'
      });
    },

    // 语音创建待办
    quickVoiceTask() {
      uni.navigateTo({
        url: '/pages_task/detail/save?ai=1&autoVoice=1'
      });
    },

    // AI识别创建待办
    quickAiTask() {
      uni.navigateTo({
        url: '/pages_task/detail/save?ai=1'
      });
    },

    // 快速记账
    quickAddAccount() {
      uni.navigateTo({
        url: '/pages_accounting/add/index'
      });
    },

    // 语音创建记账
    quickVoiceAccount() {
      uni.navigateTo({
        url: '/pages_accounting/add/simple?ai=1&autoVoice=1'
      });
    },

    // AI识别创建记账
    quickAiAccount() {
      uni.navigateTo({
        url: '/pages_accounting/add/simple?ai=1'
      });
    },

    // 导航到聊天页面
    navigateToChat() {
      uni.navigateTo({
        url: '/pages_chat/index/index'
      });
    },

    // AI对话
    aiChat() {
      this.navigateToChat();
    },
  }
};
</script>

<style lang="scss" scoped>
.home-container {
  min-height: 100vh;
  background-position-y: 90rpx;
  background-size: 100% 100%;
  padding-top: 88rpx;
  position: relative;
}

// 背景图片
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1;
  opacity: 0.2;
}

// 自定义导航栏
.custom-navbar {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .navbar-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 40rpx;

    .mode-switch {
      display: flex;
      background-color: #f5f5f5;
      border-radius: 12rpx;
      padding: 4rpx;

      .switch-item {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 12rpx 32rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #666666;
        transition: all 0.3s ease;
        cursor: pointer;

        .switch-icon {
          font-size: 32rpx;
        }

        &.active {
          background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
          color: #ffffff;
          font-weight: 500;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }

  .navbar-shadow {
    height: 2rpx;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.08), transparent);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  }
}

// 顶部欢迎区域
.header-section {
  padding: 30rpx 40rpx 20rpx;
  position: relative;
  z-index: 10;
  margin-bottom: 5rpx;

  .welcome-card {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border-radius: 24rpx;
    padding: 30rpx 40rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.15);

    // 动态背景效果
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 300rpx;
      height: 300rpx;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -30%;
      left: -10%;
      width: 200rpx;
      height: 200rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 8s ease-in-out infinite reverse;
    }

    .welcome-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;

      .greeting-text {
        flex: 1;

        .greeting-main {
          display: block;
          font-size: 40rpx;
          font-weight: 700;
          color: #ffffff;
          margin-bottom: 8rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }

        .greeting-sub {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
          line-height: 1.4;
        }
      }

      .avatar-section {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .stats-row {
      display: flex;
      justify-content: space-between;

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-number {
          display: block;
          font-size: 52rpx;
          font-weight: 800;
          color: #ffffff;
          margin-bottom: 8rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }

        .stat-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.85);
        }
      }
    }
  }
}

// 主要功能入口
.main-actions {
  padding: 0 40rpx 120rpx;
  position: relative;
  z-index: 10;

  .actions-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .action-item {
      background: #ffffff;
      border-radius: 20rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      border: 1rpx solid #f0f0f0;
      overflow: hidden;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
      }

      // 普通项目样式
      &:not(.enhanced-task) {
        padding: 32rpx 24rpx;
        display: flex;
        align-items: center;

        .item-icon {
          transform: scale(0.95);
          filter: brightness(1.1);
        }
      }

      // 增强版待办事项样式
      &.enhanced-task {
        padding: 0;

        .task-main-content {
          padding: 32rpx 24rpx 20rpx;
          display: flex;
          align-items: center;
          gap: 20rpx;
          min-height: 120rpx;
        }

        .task-stats {
          display: flex;
          gap: 32rpx;
          margin-left: auto;
          padding-right: 8rpx;
          align-items: center;
          height: 100%;

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 6rpx;
            min-width: 48rpx;

            .stat-number {
              font-size: 32rpx;
              font-weight: 800;
              color: #2c3e50;
              line-height: 1;
            }

            .stat-label {
              font-size: 22rpx;
              color: #8c9196;
              font-weight: 500;
              white-space: nowrap;
            }
          }
        }

        // 记账统计特殊样式
        .accounting-stats {
          .stat-item {
            .stat-number {
              font-weight: 600; // 减轻字重
              display: flex;
              align-items: flex-end; // 底部对齐

              .currency-symbol {
                font-size: 18rpx; // 非常小的￥符号
                margin-right: 2rpx;
                align-self: flex-end; // 与数字底部对齐
              }
            }
          }

          .expense-stat {
            .stat-number {
              color: #ff4d4f; // 支出用红色

              .currency-symbol {
                color: #ff4d4f;
              }
            }
            .stat-label {
              color: #ff7875;
            }
          }

          .income-stat {
            .stat-number {
              color: #52c41a; // 收入用绿色

              .currency-symbol {
                color: #52c41a;
              }
            }
            .stat-label {
              color: #73d13d;
            }
          }
        }

        .quick-actions {
          display: flex;
          border-top: 1rpx solid #f5f5f5;
          background: #fafafa;

          .quick-btn {
            flex: 1;
            padding: 20rpx 16rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8rpx;
            transition: all 0.2s ease;
            position: relative;

            &:not(:last-child)::after {
              content: '';
              position: absolute;
              right: 0;
              top: 20%;
              bottom: 20%;
              width: 1rpx;
              background: #e8e8e8;
            }

            &:active {
              background: rgba(24, 144, 255, 0.05);
              transform: scale(0.98);
            }

            .quick-icon {
              font-size: 28rpx;
              line-height: 1;
            }

            .quick-text {
              font-size: 22rpx;
              color: #666;
              font-weight: 500;
            }

            // 不同按钮的颜色
            &.quick-add {
              .quick-icon { color: #52c41a; }
              .quick-text { color: #52c41a; }
            }

            &.quick-voice {
              .quick-icon { color: #1890ff; }
              .quick-text { color: #1890ff; }
            }

            &.quick-ai {
              .quick-icon { color: #722ed1; }
              .quick-text { color: #722ed1; }
            }
          }
        }
      }

      .item-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        flex-shrink: 0;
        position: relative;
        transition: all 0.3s ease;

        .icon-emoji {
          font-size: 36rpx;
          position: relative;
          z-index: 2;
        }
      }

      // 统一简洁的图标样式
      .item-icon {
        background: #f8f9fa;
        border: 1rpx solid #e9ecef;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

        .icon-emoji {
          color: #6c757d;
          filter: none;
        }
      }

      // 待办事项保持蓝色主题
      &.task-item .item-icon {
        background: #f0f8ff;
        border: 1rpx solid #d6e9ff;

        .icon-emoji {
          color: #1890ff;
        }
      }

      // 记账工具保持金色主题
      &.accounting-item .item-icon {
        background: #fffbf0;
        border: 1rpx solid #ffe7ba;

        .icon-emoji {
          color: #faad14;
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0; // 防止文字溢出

        .item-title {
          font-size: 32rpx;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 8rpx;
          line-height: 1.3;
        }

        .item-desc {
          font-size: 26rpx;
          color: #7f8c8d;
          line-height: 1.5;
          font-weight: 400;
        }
      }

      .item-arrow {
        margin-left: 12rpx;
        flex-shrink: 0;

        .arrow-icon {
          font-size: 36rpx;
          color: #bdc3c7;
          font-weight: 300;
        }
      }
    }
  }
}

</style>
