<!--
 * @Description: 待办编辑页面 - 新建/编辑待办事项
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30
-->
<template>
  <view class="todo-save-container">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 内容输入 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-title">内容</view>
                    <view class="ai-recognition-btn" @click="handleAiRecognitionClick">
            <text class="ai-icon">⚡</text>
            <text class="ai-text">AI识别</text>
          </view>
        </view>
        <textarea
          class="content-input"
          placeholder="请输入待办事项内容"
          v-model="formData.content"
          maxlength="500"
          :auto-height="true"
        />
      </view>

      <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">图片</view>
        <xj-upload-image
          v-model="formData.images"
          :max-count="9"
          upload-text="添加图片"
          @upload-success="onImageUploadSuccess"
          @upload-error="onImageUploadError"
          @delete="onImageDelete"
        />
      </view>

      <!-- 状态选择 -->
      <view class="form-section">
        <view class="section-title">状态</view>
        <view class="status-options">
          <view
            v-for="(config, status) in statusConfigs"
            :key="status"
            class="status-option"
            :class="{ active: formData.status === config.name }"
            @click="selectStatus(config.name)"
          >
            <text class="status-icon">{{ config.icon }}</text>
            <text class="status-name">{{ config.name }}</text>
          </view>
        </view>
      </view>

      <!-- 分类选择 -->
      <view class="form-section">
        <view class="section-title">分类</view>
        <view class="category-selector" @click="showCategoryPicker = true">
          <view class="selected-category" v-if="selectedCategory">
            <text class="category-icon">{{ selectedCategory.icon }}</text>
            <text class="category-name">{{ selectedCategory.name }}</text>
          </view>
          <text class="placeholder" v-else>请选择分类</text>
          <text class="arrow">›</text>
        </view>
      </view>

      <!-- 优先级选择 -->
      <view class="form-section">
        <view class="section-title">优先级</view>
        <view class="priority-options">
          <view
            v-for="(config, priority) in priorityConfigs"
            :key="priority"
            class="priority-option"
            :class="{ active: formData.priority == config.name }"
            @click="selectPriority(config.name)"
          >
            <text class="priority-icon">{{ config.icon }}</text>
            <text class="priority-name">{{ config.name }}</text>
          </view>
        </view>
      </view>

      <!-- 标签选择 -->
      <view class="form-section">
        <view class="section-title">标签</view>
        <view class="tags-selector" @click="showTagPicker = true">
          <view class="selected-tags" v-if="selectedTags.length > 0">
            <view
              v-for="tag in selectedTags"
              :key="tag.id"
              class="tag-item"
              :style="{
                backgroundColor: tag.color + '20',
                color: tag.color,
                borderColor: tag.color + '40'
              }"
            >
              <text>{{ tag.name }}</text>
            </view>
          </view>
          <text class="placeholder" v-else>请选择标签</text>
          <text class="arrow">›</text>
        </view>
      </view>

      <!-- 置顶设置 -->
      <view class="form-section">
        <view class="switch-row">
          <view class="switch-label">
            <text class="switch-icon">📌</text>
            <text class="switch-text">置顶显示</text>
          </view>
          <switch
            :checked="formData.isCommond === 1"
            @change="onTopSwitchChange"
            color="#1890ff"
            style="transform: scale(0.7);"
          />
        </view>
      </view>

      <!-- 截止时间 -->
      <view class="form-section">
        <view class="section-title">截止时间</view>
        <picker
          mode="multiSelector"
          :value="dateTimePickerValue"
          :range="dateTimeRange"
          @change="onDeadlineChange"
          @columnchange="onDateTimeColumnChange"
        >
          <view class="date-picker">
            <text>{{ formattedDeadline || '请选择截止时间' }}</text>
            <text class="arrow">›</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="action-buttons">
                <button class="btn btn-ai" @click="handleAiRecognitionClick">
          <text class="btn-icon">⚡</text>
          <text>AI识别</text>
        </button>
        <button class="btn  btn-primary" @click="handleSave" :disabled="!canSave">
          <text class="btn-icon">💾</text>
          <text>{{ isEditMode ? '更新' : '保存' }}</text>
        </button>
      </view>
    </view>

    <!-- 分类选择弹窗 -->
    <u-popup :show="showCategoryPicker" @close="showCategoryPicker = false" mode="bottom" :round="20">
      <view class="category-picker-popup">
        <!-- 拖拽指示器 -->
        <view class="drag-indicator"></view>

        <!-- 标题栏 -->
        <view class="picker-header">
          <text class="picker-title">选择分类</text>
          <view class="picker-close" @click="showCategoryPicker = false">
            <text>×</text>
          </view>
        </view>

        <!-- 分类列表 -->
        <view class="picker-content">
          <view
            v-for="category in categories"
            :key="category.id"
            class="category-item"
            :class="{ selected: formData.categoryId == category.id }"
            @click="selectCategory(category)"
          >
            <text class="category-icon">{{ category.icon }}</text>
            <text class="category-name">{{ category.name }}</text>
            <text class="category-check" v-if="formData.categoryId == category.id">✓</text>
          </view>

          <!-- 空状态 -->
          <view class="empty-categories" v-if="categories.length === 0">
            <text class="empty-text">暂无分类</text>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="picker-footer">
          <button class="confirm-btn" @click="showCategoryPicker = false">
            确定
          </button>
        </view>
      </view>
    </u-popup>

    <!-- 标签选择弹窗 -->
    <u-popup :show="showTagPicker" @close="showTagPicker = false" mode="bottom" :round="20">
      <view class="tag-picker-popup">
        <!-- 拖拽指示器 -->
        <view class="drag-indicator"></view>

        <!-- 标题栏 -->
        <view class="picker-header">
          <text class="picker-title">选择标签</text>
          <view class="picker-close" @click="showTagPicker = false">
            <text>×</text>
          </view>
        </view>

        <!-- 快速添加标签 -->
        <view class="quick-add-section">
          <view class="section-title">快速添加</view>
          <view class="add-tag-form">
            <input
              class="tag-input"
              type="text"
              placeholder="输入标签名称"
              v-model="newTagName"
              maxlength="10"
            />
            <button class="add-btn" @click="quickAddTag" :disabled="!newTagName.trim()">
              添加
            </button>
          </view>
        </view>

        <!-- 标签网格 -->
        <view class="picker-content">
          <view class="section-title">选择标签</view>
          <view class="tags-grid">
            <view
              v-for="tag in tags"
              :key="tag.id"
              class="tag-chip"
              :class="{ selected: formData.tags.includes(tag.id) }"
              :style="{
                backgroundColor: formData.tags.includes(tag.id) ? tag.color + '20' : 'rgba(0,0,0,0.05)',
                borderColor: formData.tags.includes(tag.id) ? tag.color : 'transparent',
                color: formData.tags.includes(tag.id) ? tag.color : '#666'
              }"
              @click="toggleTag(tag)"
            >
              <text class="tag-name">{{ tag.name }}</text>
              <text class="tag-check" v-if="formData.tags.includes(tag.id)">✓</text>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-tags" v-if="tags.length === 0">
            <text class="empty-text">暂无标签，快速添加一个吧</text>
          </view>
        </view>

        <!-- 底部按钮组 -->
        <view class="picker-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="showTagPicker = false">
              取消
            </button>

            <button class="confirm-btn" @click="showTagPicker = false">
              确定
            </button>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- AI识别弹窗组件 -->
    <xj-aitextarea-popup
      ref="aiPopupRef"
      :show="showAiRecognition"
      @close="handleAiPopupClose"
      @submit="handleAiSubmit"
    />
  </view>
</template>

<script>
import {
  STATUS_CONFIG,
  PRIORITY_CONFIG,
  TODO_STATUS,
  PRIORITY_LEVELS
} from '../data/constants.js'
import { formatDate, getCurrentTimestamp } from '../data/utils.js'
import XjUploadImage from '@/components/xj-upload-image/index.vue'
import XjAitextareaPopup from '@/components/xj-aitextarea-popup/index.vue'

export default {
  name: 'TodoSave',
  components: {
    XjUploadImage,
    XjAitextareaPopup
  },
  data() {
    return {
      isEditMode: false,
      todoId: '',
      formData: {
        content: '',
        images: [],
        status: '未开始',
        categoryId: 0,
        priority: '普通',
        tags: [],
        deadline: '',
        isCommond: 0 // 0=普通 1=置顶
      },
      categories: [],
      tags: [],
      showCategoryPicker: false,
      showTagPicker: false,
      showAiRecognition: false, // AI识别弹窗状态
      newTagName: '', // 新标签名称
      dateTimePickerValue: [0, 0, 0, 0, 0], // [年, 月, 日, 时, 分]
      dateTimeRange: [],
      hasTriggeredAutoActions: false // 标志：是否已经触发过自动操作
    }
  },

  computed: {
    statusConfigs() {
      return STATUS_CONFIG
    },

    priorityConfigs() {
      return PRIORITY_CONFIG
    },

    selectedCategory() {
      return this.categories.find(cat => cat.id == this.formData.categoryId) || null
    },

    selectedTags() {
      return this.tags.filter(tag => this.formData.tags.includes(tag.id))
    },

    // 格式化显示的截止时间
    formattedDeadline() {
      if (!this.formData.deadline) return ''

      const date = new Date(this.formData.deadline)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}`
    },

    canSave() {
      return this.formData.content.trim().length > 0
    }
  },

  onLoad(options) {
    this.initPage(options)
  },

  methods: {
    // 初始化页面
    async initPage(options) {
      await this.loadData()
      this.initDateTimePicker()

      if (options.id) {
        // 编辑模式
        this.isEditMode = true
        this.todoId = options.id
        await this.loadTodoData(options.id)

        // 设置导航栏标题
        uni.setNavigationBarTitle({
          title: '编辑待办'
        })
      } else {
        // 新建模式
        uni.setNavigationBarTitle({
          title: '新建待办'
        })
      }

      // 处理AI对话相关参数 - 只执行一次
      console.log('页面参数:', options)
      console.log('是否已触发过自动操作:', this.hasTriggeredAutoActions)

      if (!this.hasTriggeredAutoActions && (options?.ai == '1' || options?.ai == 1)) {
        console.log('检测到AI参数，准备弹出AI识别弹窗')
        this.hasTriggeredAutoActions = true // 标记已经触发过

        // 延迟一下确保页面渲染完成
        this.$nextTick(() => {
          setTimeout(() => {
            this.showAiRecognition = true
            console.log('AI识别弹窗已显示')

            // 如果同时设置了自动录音
            if (options?.autoVoice == '1' || options?.autoVoice == 1) {
              console.log('检测到自动录音参数，准备触发自动录音')
              // 再延迟一下确保AI弹窗已经显示
              setTimeout(() => {
                this.triggerAutoVoiceRecording()
              }, 300)
            }
          }, 100)
        })
      }
    },

    // 加载基础数据
    async loadData() {
      try {
        // 加载分类数据
        const _categoriesRes = await this.$ajax.get('/task/category/list')
        if (_categoriesRes?.code == 200) {
          this.categories = Array.isArray(_categoriesRes?.data) ? _categoriesRes.data : []
        } else {
          console.error('加载分类失败:', _categoriesRes?.msg)
          this.categories = []
        }

        this.loadTags(); // 加载标签数据

        console.log('加载基础数据完成 - 分类:', this.categories.length, '标签:', this.tags.length)
      } catch (error) {
        console.error('加载基础数据失败:', error)
        this.categories = []
        this.tags = []
      }
    },
    // 加载标签数据
    async loadTags() {
      const _tagsRes = await this.$ajax.get('/task/tag/list')
        if (_tagsRes?.code == 200) {
          this.tags = Array.isArray(_tagsRes?.data) ? _tagsRes.data : []
        } else {
          console.error('加载标签失败:', _tagsRes?.msg)
          this.tags = []
        }
    },

    // 加载待办数据
    async loadTodoData(todoId) {
      try {
        uni.showLoading({ title: '加载中...' })

        // 调用API获取待办详情
        const response = await this.$ajax.get('/task/get', { id: todoId, isEdit:1 })

        if (response?.code === 200 && response.data) {
          const todo = response.data
          console.log('加载待办详情成功:', todo)

          // 映射API数据到表单数据
          this.formData = {
            content: todo.content || '',
            images: this.parseImages(todo.images),
            status: todo.status || '未开始',
            categoryId: parseInt(todo.categoryId) || 0, // 确保为数字类型
            priority: todo.priority || '普通',
            tags: this.convertTagObjectsToIds(todo.tags || []),
            deadline: todo.deadline ? this.formatDeadlineFromTimestamp(todo.deadline) : '',
            isCommond: parseInt(todo.isCommond) || 0 // 置顶状态
          }

          console.log('表单数据初始化完成:', this.formData)
          console.log('当前分类列表:', this.categories)
          console.log('选中的分类ID:', this.formData.categoryId)
          console.log('匹配的分类:', this.selectedCategory)
        } else {
          throw new Error(response?.msg || '获取待办详情失败')
        }

        uni.hideLoading()
      } catch (error) {
        console.error('加载待办数据失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 200)
      }
    },

    // 图片上传成功回调
    onImageUploadSuccess(result) {
      console.log('图片上传成功:', result)
      uni.showToast({
        title: '图片上传成功',
        icon: 'success',
        duration: 1500
      })
    },

    // 图片上传失败回调
    onImageUploadError(error) {
      console.error('图片上传失败:', error)
      uni.showToast({
        title: '图片上传失败，请重试',
        icon: 'none',
        duration: 2000
      })
    },

    // 图片删除回调
    onImageDelete(result) {
      console.log('图片删除:', result)
      uni.showToast({
        title: '图片已删除',
        icon: 'success',
        duration: 1500
      })
    },

    // 选择状态
    selectStatus(status) {
      this.formData.status = status
    },

    // 选择分类
    selectCategory(category) {
      this.formData.categoryId = category.id
      this.showCategoryPicker = false
    },

    // 选择优先级
    selectPriority(priority) {
      this.formData.priority = priority
    },

    // 置顶开关变化
    onTopSwitchChange(e) {
      this.formData.isCommond = e.detail.value ? 1 : 0
    },

    // 切换标签选择
    toggleTag(tag) {
      const index = this.formData.tags.indexOf(tag.id)
      if (index > -1) {
        this.formData.tags.splice(index, 1)
      } else {
        this.formData.tags.push(tag.id)
      }
    },

    // 快速添加标签
    async quickAddTag() {
      const tagName = this.newTagName.trim()
      if (!tagName) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        })
        return
      }

      // 检查是否已存在同名标签
      const existingTag = this.tags.find(tag => tag.name === tagName)
      if (existingTag) {
        uni.showToast({
          title: '标签已存在',
          icon: 'none'
        })
        // 自动选中已存在的标签
        if (!this.formData.tags.includes(existingTag.id)) {
          this.formData.tags.push(existingTag.id)
        }
        this.newTagName = ''
        return
      }

        uni.showLoading({ title: '添加中...' })

        // 生成随机颜色
        const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16']
        const randomColor = colors[Math.floor(Math.random() * colors.length)]

        const response = await this.$ajax.post('/task/tag/save', {
          id: 0,
          name: tagName,
          color: randomColor
        })

        uni.hideLoading()

        // 检查响应结构
        if (response?.code == 200) {
          // 清空输入框
          this.newTagName = ''

          // 重新加载标签列表
          await this.loadTags()

          // 自动选中新添加的标签（通过名称匹配）
          const newTag = this.tags.find(tag => tag.name === tagName)
          if (newTag && !this.formData.tags.includes(newTag.id)) {
            this.formData.tags.push(newTag.id)
          }

          this.$toast('添加成功')
        } else {
          this.$toast(response?.msg || '添加失败')
        }
    },

    // 初始化日期时间选择器
    initDateTimePicker() {
      const now = new Date()
      const currentYear = now.getFullYear()

      // 年份范围：当前年份到5年后
      const years = []
      for (let i = currentYear; i <= currentYear + 5; i++) {
        years.push(i + '年')
      }

      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push(String(i).padStart(2, '0') + '月')
      }

      const days = []
      for (let i = 1; i <= 31; i++) {
        days.push(String(i).padStart(2, '0') + '日')
      }

      const hours = []
      for (let i = 0; i <= 23; i++) {
        hours.push(String(i).padStart(2, '0') + '时')
      }

      const minutes = []
      for (let i = 0; i <= 59; i++) {
        minutes.push(String(i).padStart(2, '0') + '分')
      }

      this.dateTimeRange = [years, months, days, hours, minutes]

      // 设置默认值为当前时间
      this.dateTimePickerValue = [
        0, // 当前年份在数组中的索引
        now.getMonth(), // 当前月份索引
        now.getDate() - 1, // 当前日期索引
        now.getHours(), // 当前小时索引
        now.getMinutes() // 当前分钟索引
      ]
    },

    // 日期时间列变化
    onDateTimeColumnChange(e) {
      const { column, value } = e.detail
      this.dateTimePickerValue[column] = value

      // 如果改变了年份或月份，需要更新天数
      if (column === 0 || column === 1) {
        this.updateDaysInMonth()
      }
    },

    // 更新月份天数
    updateDaysInMonth() {
      const yearIndex = this.dateTimePickerValue[0]
      const monthIndex = this.dateTimePickerValue[1]

      const year = parseInt(this.dateTimeRange[0][yearIndex])
      const month = monthIndex + 1

      const daysInMonth = new Date(year, month, 0).getDate()
      const days = []
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(String(i).padStart(2, '0') + '日')
      }

      this.dateTimeRange[2] = days

      // 如果当前选中的日期超过了该月的天数，调整为最后一天
      if (this.dateTimePickerValue[2] >= daysInMonth) {
        this.dateTimePickerValue[2] = daysInMonth - 1
      }
    },

    // 截止时间变化
    onDeadlineChange(e) {
      const values = e.detail.value

      // 构建日期时间字符串
      const yearStr = this.dateTimeRange[0][values[0]].replace('年', '')
      const monthStr = this.dateTimeRange[1][values[1]].replace('月', '')
      const dayStr = this.dateTimeRange[2][values[2]].replace('日', '')
      const hourStr = this.dateTimeRange[3][values[3]].replace('时', '')
      const minuteStr = this.dateTimeRange[4][values[4]].replace('分', '')

      const selectedDate = new Date(`${yearStr}-${monthStr}-${dayStr} ${hourStr}:${minuteStr}:00`)

      // 检查日期范围：不能早于一个月前
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

      if (selectedDate < oneMonthAgo) {
        uni.showToast({
          title: '截止时间不能早于一个月前',
          icon: 'none'
        })
        return
      }

      this.dateTimePickerValue = values
      const dateTimeStr = `${yearStr}-${monthStr}-${dayStr} ${hourStr}:${minuteStr}:00`
      this.formData.deadline = dateTimeStr
    },

    // 从时间戳格式化截止时间
    formatDeadlineFromTimestamp(timestamp) {
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')

      // 同时更新选择器的值
      const currentYear = new Date().getFullYear()

      // 确保年份在有效范围内（当前年到5年后）
      let yearIndex = year - currentYear
      if (yearIndex < 0) yearIndex = 0
      if (yearIndex > 5) yearIndex = 5

      const monthIndex = date.getMonth()
      const dayIndex = date.getDate() - 1
      const hourIndex = date.getHours()
      const minuteIndex = date.getMinutes()

      this.dateTimePickerValue = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex]

      return `${year}-${month}-${day} ${hour}:${minute}:00`
    },

    // 处理AI识别按钮点击
    handleAiRecognitionClick() {
      if (this.isEditMode) {
        this.$toast('AI识别仅在新建待办时可用')
        return
      }
      this.showAiRecognition = true
    },

    // 处理AI弹窗关闭
    handleAiPopupClose() {
      this.showAiRecognition = false
      // 重置自动操作标志，允许用户再次手动触发
      // 注意：这里不重置，避免用户关闭后重新触发自动操作
      // this.hasTriggeredAutoActions = false
    },

    // 取消操作
    handleCancel() {
      uni.navigateBack()
    },

    // 保存操作
    async handleSave() {
      if (!this.canSave) {
        uni.showToast({
          title: '请填写内容',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: this.isEditMode ? '更新中...' : '保存中...' })

        // 转换截止时间为时间戳
        let deadline = 0
        if (this.formData.deadline) {
          deadline = Math.floor(new Date(this.formData.deadline).getTime() / 1000)
        }

        const todoData = {
          id: this.isEditMode ? this.todoId : 0,
          categoryId: this.formData.categoryId,
          content: this.formData.content.trim(),
          images: this.formatImages(this.formData.images),
          status: this.formData.status,
          priority: this.formData.priority,
          deadline: deadline,
          tags: this.formatTagNames(this.formData.tags),
          isCommond: this.formData.isCommond
        }

        const _res = await this.$ajax.post('/task/save', todoData)
        console.log("🚀 -> handleSave -> _res:", _res)
        uni.hideLoading()

        if (_res?.code == 200) {
          this.$toast('保存成功')
        } else {
          this.$toast(_res?.msg || '保存失败')
        }

        setTimeout(() => {
          uni.navigateBack()
        }, 200)

      } catch (error) {
        uni.hideLoading()
        console.error('保存失败:', error)
      }
    },

    // 工具方法：格式化标签名称数组
    formatTagNames(tagIds) {
      if (!Array.isArray(tagIds) || tagIds.length === 0) {
        return []
      }

      // 根据标签ID获取标签名称
      const tagNames = tagIds.map(tagId => {
        const tag = this.tags.find(t => t.id === tagId)
        return tag ? tag.name : null
      }).filter(name => name !== null)

      return tagNames
    },

    // 工具方法：将标签名称数组转换为标签ID数组（用于编辑模式加载数据）
    convertTagNamesToIds(tagNames) {
      if (!Array.isArray(tagNames) || tagNames.length === 0) {
        return []
      }

      // 根据标签名称获取标签ID
      const tagIds = tagNames.map(tagName => {
        const tag = this.tags.find(t => t.name === tagName)
        return tag ? tag.id : null
      }).filter(id => id !== null)

      return tagIds
    },

    // 工具方法：将标签对象数组转换为标签ID数组（用于API数据加载）
    convertTagObjectsToIds(tagObjects) {
      if (!Array.isArray(tagObjects) || tagObjects.length === 0) {
        return []
      }

      // 从标签对象数组中提取ID
      return tagObjects.map(tag => tag.id).filter(id => id !== null && id !== undefined)
    },

    // 工具方法：格式化图片数组为JSON字符串
    formatImages(images) {
      return Array.isArray(images) ? JSON.stringify(images) : '[]'
    },

    // 工具方法：解析JSON字符串为数组
    parseJsonArray(jsonStr) {
      try {
        return JSON.parse(jsonStr) || []
      } catch (error) {
        console.warn('解析JSON数组失败:', error)
        return []
      }
    },

    // 工具方法：解析图片数据
    parseImages(images) {
      if (Array.isArray(images)) {
        return images
      }
      if (typeof images === 'string') {
        try {
          const parsed = JSON.parse(images)
          return Array.isArray(parsed) ? parsed : []
        } catch (error) {
          console.warn('解析图片JSON失败:', error)
          return []
        }
      }
      return []
    },

    // AI识别提交回调
    async handleAiSubmit(recognizedContent) {
      console.log("🚀 -> handleAiSubmit -> recognizedContent:", recognizedContent)
      if(recognizedContent.trim() == ""){
        // this.$toast('识别结果为空')
        return
      }

      // AI识别只在新建模式下可用
      if (this.isEditMode) {
        this.$toast('AI识别仅在新建待办时可用')
        return
      }

      try {
        uni.showLoading({ title: 'AI识别中...' })

        const _res = await this.$ajax.post('/task/ai/extract', {
          content: recognizedContent
        })
        console.log("🚀 -> handleAiSubmit -> _res:", _res)

        uni.hideLoading()

        if (_res?.code == 200 && _res?.data) {
          const { name, arguments: aiArgs } = _res.data

          // 只处理addTask类型的识别结果
          if (name === 'addTask' && aiArgs) {
            await this.fillFormFromAiResult(aiArgs)
            this.$toast('AI识别完成，已自动填充表单')
          } else {
            this.$toast('AI识别结果格式不正确')
          }
        } else {
          this.$toast(_res?.msg || 'AI识别失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('AI识别失败:', error)
        this.$toast('AI识别失败，请重试')
      }
    },

    // 根据AI识别结果填充表单
    async fillFormFromAiResult(aiArgs) {
      console.log('AI识别结果:', aiArgs)

      // 填充内容
      if (aiArgs.content) {
        this.formData.content = aiArgs.content
      }

      // 填充状态
      if (aiArgs.status) {
        this.formData.status = aiArgs.status
      }

      // 填充优先级
      if (aiArgs.priority) {
        this.formData.priority = aiArgs.priority
      }

      // 填充分类ID
      if (aiArgs.category_id) {
        this.formData.categoryId = parseInt(aiArgs.category_id)
      }

      // 填充截止时间
      if (aiArgs.deadline) {
        this.formData.deadline = aiArgs.deadline
        // 更新时间选择器的值
        this.updateDateTimePickerFromDeadline(aiArgs.deadline)
      }

      // 处理标签 - AI返回的是标签名称数组
      if (aiArgs.tags && Array.isArray(aiArgs.tags)) {
        const tagIds = []

        for (const tagName of aiArgs.tags) {
          // 查找已存在的标签
          let existingTag = this.tags.find(tag => tag.name === tagName)

          if (existingTag) {
            // 标签已存在，直接添加ID
            tagIds.push(existingTag.id)
          } else {
            // 标签不存在，创建新标签
            try {
              const newTagId = await this.createTagByName(tagName)
              if (newTagId) {
                tagIds.push(newTagId)
              }
            } catch (error) {
              console.error('创建标签失败:', tagName, error)
            }
          }
        }

        this.formData.tags = tagIds
      }
    },

    // 根据截止时间字符串更新时间选择器
    updateDateTimePickerFromDeadline(deadlineStr) {
      try {
        const date = new Date(deadlineStr)
        if (isNaN(date.getTime())) {
          console.warn('无效的截止时间格式:', deadlineStr)
          return
        }

        const currentYear = new Date().getFullYear()
        const year = date.getFullYear()

        // 计算年份在选择器中的索引
        let yearIndex = year - currentYear
        if (yearIndex < 0) yearIndex = 0
        if (yearIndex > 5) yearIndex = 5

        const monthIndex = date.getMonth()
        const dayIndex = date.getDate() - 1
        const hourIndex = date.getHours()
        const minuteIndex = date.getMinutes()

        this.dateTimePickerValue = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex]
      } catch (error) {
        console.error('更新时间选择器失败:', error)
      }
    },

    // 根据标签名称创建新标签
    async createTagByName(tagName) {
      try {
        // 生成随机颜色
        const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16']
        const randomColor = colors[Math.floor(Math.random() * colors.length)]

        const response = await this.$ajax.post('/task/tag/save', {
          id: 0,
          name: tagName,
          color: randomColor
        })

        if (response?.code == 200) {
          // 重新加载标签列表
          await this.loadTags()

          // 返回新创建的标签ID
          const newTag = this.tags.find(tag => tag.name === tagName)
          return newTag ? newTag.id : null
        } else {
          console.error('创建标签失败:', response?.msg)
          return null
        }
      } catch (error) {
        console.error('创建标签异常:', error)
        return null
      }
    },

    // 触发自动录音功能
    triggerAutoVoiceRecording() {
      try {
        console.log('开始触发自动录音功能')
        console.log('AI弹窗组件引用:', this.$refs.aiPopupRef)

        // 通过ref调用AI弹窗组件的自动录音方法
        if (this.$refs.aiPopupRef && this.$refs.aiPopupRef.triggerAutoVoice) {
          console.log('调用AI弹窗组件的自动录音方法')
          this.$refs.aiPopupRef.triggerAutoVoice()
        } else {
          console.warn('AI弹窗组件未找到或不支持自动录音功能', {
            ref: this.$refs.aiPopupRef,
            method: this.$refs.aiPopupRef?.triggerAutoVoice
          })
        }
      } catch (error) {
        console.error('触发自动录音失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.todo-save-container {
  background-color: $background-color;
  min-height: 100vh;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

.form-container {
  padding: $spacing-lg;

  .form-section {
    margin-bottom: $spacing-xl;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-md;

      .section-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
      }

      .ai-recognition-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 12rpx 20rpx;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        border-radius: 24rpx;
        transition: all $transition-fast;
        cursor: pointer;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

        .ai-icon {
          font-size: 24rpx;
          line-height: 1;
        }

        .ai-text {
          font-size: 22rpx;
          color: $white;
          font-weight: 500;
          line-height: 1.2;
        }

        &:active {
          transform: scale(0.95);
          box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
        }

        &:hover {
          box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.4);
        }
      }
    }

    .section-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: $spacing-md;
    }

    .content-input {
      width: 100%;
      min-height: 120rpx;
      padding: $spacing-lg;
      background-color: $card-background;
      border-radius: $border-radius-md;
      border: 1rpx solid $border-color;
      font-size: $font-size-md;
      color: $text-primary;
      line-height: 1.5;
      transition: all $transition-fast;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 4rpx rgba($primary-color, 0.1);
      }
    }



    .status-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .status-option {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 12rpx 16rpx;
        background-color: $card-background;
        border-radius: 12rpx;
        border: 2rpx solid $border-color;
        transition: all $transition-fast;
        cursor: pointer;
        width: calc(25% - 12rpx); // 一行4个，减去gap
        min-height: 64rpx;

        .status-icon {
          font-size: 24rpx;
          line-height: 1;
          flex-shrink: 0;
        }

        .status-name {
          font-size: 22rpx;
          color: $text-secondary;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
        }

        &.active {
          border-color: $primary-color;
          background-color: $primary-light;
          transform: scale(1.02);

          .status-name {
            color: $primary-color;
            font-weight: 600;
          }
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .category-selector, .tags-selector {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-lg;
      background-color: $card-background;
      border-radius: $border-radius-md;
      border: 1rpx solid $border-color;
      transition: all $transition-fast;
      cursor: pointer;

      .selected-category {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .category-icon {
          font-size: $font-size-lg;
        }

        .category-name {
          font-size: $font-size-md;
          color: $text-primary;
        }
      }

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-xs;
        flex: 1;

        .tag-item {
          padding: 4rpx $spacing-sm;
          border-radius: $border-radius-sm;
          font-size: $font-size-sm;
          font-weight: 500;
          border: 1rpx solid;
        }
      }

      .placeholder {
        color: $text-placeholder;
        font-size: $font-size-md;
      }

      .arrow {
        color: $text-light;
        font-size: $font-size-lg;
      }

      &:active {
        background-color: darken($card-background, 2%);
      }
    }

    .priority-options {
      display: flex;
      flex-wrap: nowrap;
      gap: 12rpx;

      .priority-option {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 12rpx 16rpx;
        background-color: $card-background;
        border-radius: 12rpx;
        border: 2rpx solid $border-color;
        transition: all $transition-fast;
        cursor: pointer;
        width: 20%; // 5个优先级，每个20%
        min-height: 64rpx;

        .priority-icon {
          font-size: 24rpx;
          line-height: 1;
          flex-shrink: 0;
        }

        .priority-name {
          font-size: 22rpx;
          color: $text-secondary;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
        }

        &.active {
          border-color: $primary-color;
          background-color: $primary-light;
          transform: scale(1.02);

          .priority-name {
            color: $primary-color;
            font-weight: 600;
          }
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .switch-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-lg;
      background-color: $card-background;
      border-radius: $border-radius-md;
      border: 1rpx solid $border-color;

      .switch-label {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .switch-icon {
          font-size: $font-size-lg;
          line-height: 1;
        }

        .switch-text {
          font-size: $font-size-md;
          color: $text-primary;
          font-weight: 500;
        }
      }
    }

    .date-picker {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-lg;
      background-color: $card-background;
      border-radius: $border-radius-md;
      border: 1rpx solid $border-color;
      font-size: $font-size-md;
      color: $text-primary;
      transition: all $transition-fast;

      .arrow {
        color: $text-light;
        font-size: $font-size-lg;
      }

      &:active {
        background-color: darken($card-background, 2%);
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $card-background;
  padding: $spacing-lg;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.06);
  z-index: 100;

  .action-buttons {
    display: flex;
    gap: $spacing-md;

    .btn {
      flex: 1;
      height: 72rpx;
      border-radius: $border-radius-md;
      font-size: 26rpx;
      font-weight: 500;
      transition: all $transition-fast;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 24rpx;
        line-height: 1;
      }

      &.btn-secondary {
        background-color: rgba(0, 0, 0, 0.06);
        color: $text-primary;
        border: 1rpx solid rgba(0, 0, 0, 0.1);

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.98);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: $white;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);

        &:active {
          background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
          transform: scale(0.98);
          box-shadow: 0 2rpx 12rpx rgba(24, 144, 255, 0.4);
        }

        &:disabled {
          background: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          opacity: 0.6;
        }
      }
    }
  }
}

// 分类选择弹窗样式
.category-picker-popup {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .picker-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .picker-close {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: $text-secondary;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .picker-content {
    padding: 0 40rpx 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .category-item {
      display: flex;
      align-items: center;
      gap: 20rpx;
      padding: 24rpx 0;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      transition: all $transition-fast;
      cursor: pointer;

      .category-icon {
        font-size: 32rpx;
        width: 48rpx;
        text-align: center;
        line-height: 1;
      }

      .category-name {
        flex: 1;
        font-size: 28rpx;
        color: $text-primary;
        font-weight: 500;
      }

      .category-check {
        font-size: 24rpx;
        color: $primary-color;
        font-weight: 600;
      }

      &.selected {
        background-color: rgba($primary-color, 0.05);

        .category-name {
          color: $primary-color;
          font-weight: 600;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .empty-categories {
      text-align: center;
      padding: 80rpx 0;

      .empty-text {
        font-size: 26rpx;
        color: $text-light;
      }
    }
  }

  // 底部按钮
  .picker-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: $white;

    .confirm-btn {
      width: 100%;
      height: 72rpx;
      border-radius: 12rpx;
      background-color: $primary-color;
      color: $white;
      font-size: 26rpx;
      font-weight: 600;
      border: none;
      transition: all $transition-fast;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);

      &:active {
        background-color: $primary-dark;
        transform: scale(0.98);
      }
    }
  }
}

// 标签选择弹窗样式
.tag-picker-popup {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .picker-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .picker-close {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: $text-secondary;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 快速添加区域
  .quick-add-section {
    padding: 0 40rpx 32rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 20rpx;
    }

    .add-tag-form {
      display: flex;
      gap: 16rpx;
      align-items: center;

      .tag-input {
        flex: 1;
        height: 72rpx;
        padding: 0 20rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.1);
        border-radius: 12rpx;
        font-size: 26rpx;
        background-color: rgba(0, 0, 0, 0.02);
        transition: all $transition-fast;

        &:focus {
          border-color: $primary-color;
          background-color: $white;
        }

        &::placeholder {
          color: $text-light;
        }
      }

      .add-btn {
        height: 72rpx;
        padding: 0 24rpx;
        border-radius: 12rpx;
        background-color: $primary-color;
        color: $white;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        transition: all $transition-fast;
        min-width: 120rpx;

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }

        &:disabled {
          background-color: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          transform: none;
        }
      }
    }
  }

  // 内容区域
  .picker-content {
    padding: 32rpx 40rpx 40rpx;
    max-height: 50vh;
    overflow-y: auto;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 20rpx;
    }

    .tags-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .tag-chip {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 16rpx 20rpx;
        border-radius: 24rpx;
        border: 2rpx solid;
        font-size: 26rpx;
        font-weight: 500;
        transition: all $transition-fast;
        cursor: pointer;
        min-width: 80rpx;
        justify-content: center;

        .tag-name {
          line-height: 1.2;
          white-space: nowrap;
        }

        .tag-check {
          font-size: 20rpx;
          font-weight: 600;
          margin-left: 4rpx;
        }

        &.selected {
          transform: scale(1.02);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .empty-tags {
      text-align: center;
      padding: 80rpx 0;

      .empty-text {
        font-size: 26rpx;
        color: $text-light;
      }
    }
  }

  // 底部按钮组
  .picker-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: $white;

    .button-group {
      display: flex;
      gap: 16rpx;

      .cancel-btn {
        flex: 1;
        height: 72rpx;
        border-radius: 12rpx;
        background-color: rgba(0, 0, 0, 0.05);
        color: $text-secondary;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.98);
        }
      }

      .confirm-btn {
        flex: 2;
        height: 72rpx;
        border-radius: 12rpx;
        background-color: $primary-color;
        color: $white;
        font-size: 26rpx;
        font-weight: 600;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }
      }
    }
  }
}</style>
