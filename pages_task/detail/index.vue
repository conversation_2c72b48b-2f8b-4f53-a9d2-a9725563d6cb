<!--
 * @Description: 待办详情页面 - 查看待办事项详情
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30
-->
<template>
  <view class="todo-detail-container">
    <!-- 详情内容 -->
    <view class="detail-container" v-if="todoData">
      <!-- 内容卡片 -->
      <view class="content-card">
        <!-- 置顶标识 -->
        <view class="top-badge" v-if="todoData.isCommond == 1">
          📌
        </view>
        <view class="todo-content" v-html="formatTextWithLinks(todoData.content)"></view>
      </view>

      <!-- 属性信息卡片 -->
      <view class="property-card">
        <!-- 状态 -->
        <view class="property-item" @click="showStatusPicker = true">
          <view class="property-label">状态</view>
          <view class="property-value">
            <view class="status-badge" :style="{
              backgroundColor: getStatusConfig(todoData.status).color + '20',
              color: getStatusConfig(todoData.status).color
            }">
              <text class="status-icon">{{ getStatusConfig(todoData.status).icon }}</text>
              <text class="status-name">{{ todoData.status }}</text>
            </view>
            <u-icon name="arrow-right" size="12" color="#bbb"></u-icon>
          </view>
        </view>

        <!-- 分类 -->
        <view class="property-item" v-if="todoData.categoryName">
          <view class="property-label">分类</view>
          <view class="property-value pr-10">
            <view class="category-badge" :style="{
              backgroundColor: todoData.categoryColor + '20',
              color: todoData.categoryColor
            }">
              <text class="category-icon">{{ todoData.categoryIcon }}</text>
              <text class="category-name">{{ todoData.categoryName }}</text>
            </view>
          </view>
          <u-icon name="arrow-right" size="12" color="#bbb"></u-icon>
        </view>

        <!-- 优先级 -->
        <view class="property-item" v-if="todoData.priority" @click="showPriorityPicker = true">
          <view class="property-label">优先级</view>
          <view class="property-value">
            <view class="priority-badge" :style="{
              backgroundColor: getPriorityConfig(todoData.priority).color + '20',
              color: getPriorityConfig(todoData.priority).color
            }">
              <text class="priority-icon">{{ getPriorityConfig(todoData.priority).icon }}</text>
              <text class="priority-name">{{ todoData.priority }}</text>
            </view>
            <u-icon name="arrow-right" size="12" color="#bbb"></u-icon>
          </view>
        </view>

        <!-- 标签 -->
        <view class="property-item tags-item" v-if="todoData.tags && todoData.tags.length > 0" @click="showTagPicker = true">
          <view class="property-header mb-20">
            <view class="property-label">标签</view>
            <u-icon name="arrow-right" size="12" color="#bbb"></u-icon>
          </view>
          <view class="tags-content">
            <view class="tags-row">
              <view
                v-for="tag in todoData.tags"
                :key="tag.id"
                class="tag-badge"
                :style="{
                  backgroundColor: tag.color + '20',
                  color: tag.color
                }"
              >
                <text>{{ tag.name }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间信息卡片 -->
      <view class="property-card time-info-card">
        <view class="property-header">
          <text class="property-title">时间信息</text>
        </view>
        <view class="property-content">
          <view class="time-list">
            <view class="time-item deadline-item" v-if="todoData.deadline" @click="toggleDeadlineEditor">
              <text class="time-label e1">⏰ 截止时间</text>
              <text class="time-value  mr-10">{{ formatDeadline(todoData.deadline) }}</text>
              <u-icon :name="showDeadlineEditor ? 'arrow-down' : 'arrow-right'" size="12" color="#bbb"></u-icon>
            </view>

            <!-- 截止时间编辑区域 -->
            <view class="deadline-editor" v-if="showDeadlineEditor" >
              <view class="editor-content">
                <view class="datetime-selector">
                  <view class="datetime-display">
                    <text class="datetime-text"  @click="openDateTimePicker">{{ formattedSelectedDeadline || '请选择截止时间' }}</text>
                    <view class="datetime-actions">
                      <u-icon
                        v-if="selectedDeadline"
                        name="close-circle-fill"
                        size="16"
                        color="#ff4d4f"
                        @click="clearDeadline"
                        class="clear-icon"
                      ></u-icon>
                      <text class="arrow"  @click="openDateTimePicker">›</text>
                    </view>
                  </view>

                  <!-- 快捷选择按钮 -->
                  <view class="quick-actions">
                    <view class="quick-title">快捷选择</view>
                    <view class="quick-buttons">
                      <button class="quick-btn" @click="setQuickDeadline('today')">今天</button>
                      <button class="quick-btn" @click="setQuickDeadline('tomorrow')">明天</button>
                      <button class="quick-btn" @click="setQuickDeadline('thisweek')">本周六</button>
                      <button class="quick-btn" @click="setQuickDeadline('monthend')">本月底</button>
                    </view>
                  </view>
                </view>

                <!-- 原生picker -->
                <picker
                  v-show="false"
                  ref="dateTimePicker"
                  mode="multiSelector"
                  :value="dateTimePickerValue"
                  :range="dateTimeRange"
                  @change="onDeadlineChange"
                  @columnchange="onDateTimeColumnChange"
                >
                  <view></view>
                </picker>

                <!-- 操作按钮 -->
                <view class="editor-buttons">
                  <button class="cancel-btn" @click="cancelDeadlineEdit">取消</button>
                  <button class="confirm-btn" @click="updateDeadline">确定</button>
                </view>
              </view>
            </view>
            <view class="time-item">
              <text class="time-label e1">📅 创建时间</text>
              <text class="time-value mr-10">{{ formatTime(todoData.timeCreate) }}</text>
              <u-icon name="arrow-right" size="12" color="#bbb"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 图片卡片 -->
      <view class="property-card image-info-card" v-if="todoData.images && todoData.images.length > 0">
        <view class="property-header">
          <text class="property-title">图片附件</text>
        </view>
        <view class="property-content">
          <view class="images-grid">
            <image
              v-for="(image, index) in todoData.images"
              :key="index"
              :src="image"
              mode="aspectFill"
              class="detail-image"
              @click="previewImage(image, index)"
            />
          </view>
        </view>
      </view>

      <!-- 时间线卡片 -->
      <view class="property-card timeline-card" v-if="todoData.statusLogs && todoData.statusLogs.length > 0">
        <view class="property-header">
          <text class="property-title">变更历史</text>
        </view>
        <view class="property-content">
                    <view class="timeline-list">
            <view
              v-for="(log, index) in todoData.statusLogs"
              :key="log.id"
              class="timeline-item"
              :class="{
                'is-last': index === todoData.statusLogs.length - 1,
                'system-log': log.isSystem === 1,
                'user-reply': log.isSystem === 0
              }"
            >
              <view class="timeline-dot"></view>
              <view class="timeline-content">
                <!-- 系统记录显示 -->
                <template v-if="log.isSystem === 1">
                  <view class="timeline-text" v-html="formatTextWithLinks(log.remark)"></view>
                  <view class="timeline-time-row">
                    <view class="timeline-time">{{ log.timeCreateFormatted }}</view>
                    <div class="e1"></div>
                    <view class="delete-btn" @click="deleteReply(log.id)">🗑️</view>
                  </view>
                </template>

                <!-- 用户回复显示 -->
                <template v-else>
                  <view class="reply-header ml-45">
                    <image
                      class="user-avatar"
                      :src="log.userAvatar || '/static/default-avatar.png'"
                      mode="aspectFill"
                    />
                    <view class="user-info e1">
                      <view class="user-name">{{ log.userName || '用户' }}</view>
                      <view class="reply-time-row">
                        <view class="reply-time">{{ log.timeCreateFormatted }}</view>
                        <div class="e1"></div>
                        <view class="delete-btn" @click="deleteReply(log.id)">🗑️</view>
                      </view>
                    </view>
                  </view>
                  <view class="reply-content ml-45">
                    <view class="reply-text" v-html="formatTextWithLinks(log.remark)"></view>
                  </view>
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-else>
      <text>加载中...</text>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="todoData">
      <view class="action-buttons">
        <button class="btn btn-track" @click="showTrackReplyPicker = true">
          <text class="btn-icon">💬</text>
          <text>跟踪回复</text>
        </button>
        <button class="btn btn-edit" @click="handleEdit">
          <text class="btn-icon">✏️</text>
          <text>编辑</text>
        </button>
        <button class="btn btn-delete" @click="handleDelete">
          <text class="btn-icon">🗑️</text>
          <text>删除</text>
        </button>
      </view>
    </view>

    <!-- 状态选择弹窗 -->
    <u-popup :show="showStatusPicker" @close="showStatusPicker = false" mode="bottom" :round="20">
      <view class="picker-popup">
        <view class="drag-indicator"></view>
        <view class="picker-header">
          <text class="picker-title">修改状态</text>
          <view class="picker-close" @click="showStatusPicker = false">
            <text>×</text>
          </view>
        </view>
        <view class="picker-content">
          <view class="status-options">
            <view
              v-for="(config, status) in statusConfigs"
              :key="status"
              class="status-option"
              :class="{ active: selectedStatus === config.name }"
              @click="selectStatus(config.name)"
            >
              <text class="status-icon">{{ config.icon }}</text>
              <text class="status-name">{{ config.name }}</text>
            </view>
          </view>
        </view>
        <view class="picker-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="showStatusPicker = false">取消</button>
            <button class="confirm-btn" @click="updateStatus">确定</button>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 优先级选择弹窗 -->
    <u-popup :show="showPriorityPicker" @close="showPriorityPicker = false" mode="bottom" :round="20">
      <view class="picker-popup">
        <view class="drag-indicator"></view>
        <view class="picker-header">
          <text class="picker-title">修改优先级</text>
          <view class="picker-close" @click="showPriorityPicker = false">
            <text>×</text>
          </view>
        </view>
        <view class="picker-content">
          <view class="priority-options">
            <view
              v-for="(config, priority) in priorityConfigs"
              :key="priority"
              class="priority-option"
              :class="{ active: selectedPriority === config.name }"
              @click="selectPriority(config.name)"
            >
              <text class="priority-icon">{{ config.icon }}</text>
              <text class="priority-name">{{ config.name }}</text>
            </view>
          </view>
        </view>
        <view class="picker-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="showPriorityPicker = false">取消</button>
            <button class="confirm-btn" @click="updatePriority">确定</button>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 标签选择弹窗 -->
    <u-popup :show="showTagPicker" @close="showTagPicker = false" mode="bottom" :round="20">
      <view class="picker-popup">
        <view class="drag-indicator"></view>
        <view class="picker-header">
          <text class="picker-title">修改标签</text>
          <view class="picker-close" @click="showTagPicker = false">
            <text>×</text>
          </view>
        </view>
        <view class="picker-content">
          <view class="tags-grid">
            <view
              v-for="tag in allTags"
              :key="tag.id"
              class="tag-chip"
              :class="{ selected: selectedTagIds.includes(tag.id) }"
              :style="{
                backgroundColor: selectedTagIds.includes(tag.id) ? tag.color + '20' : 'rgba(0,0,0,0.05)',
                borderColor: selectedTagIds.includes(tag.id) ? tag.color : 'transparent',
                color: selectedTagIds.includes(tag.id) ? tag.color : '#666'
              }"
              @click="toggleTag(tag)"
            >
              <text class="tag-name">{{ tag.name }}</text>
              <text class="tag-check" v-if="selectedTagIds.includes(tag.id)">✓</text>
            </view>
          </view>
        </view>
        <view class="picker-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="showTagPicker = false">取消</button>
            <button class="confirm-btn" @click="updateTags">确定</button>
          </view>
        </view>
      </view>
    </u-popup>



    <!-- 跟踪回复弹窗 -->
    <u-popup :show="showTrackReplyPicker" @close="showTrackReplyPicker = false" mode="bottom" :round="20">
      <view class="track-reply-popup">
        <view class="drag-indicator"></view>

        <!-- 标题栏 -->
        <view class="popup-header">
          <view class="header-left">
            <view class="header-icon">💬</view>
            <text class="popup-title">添加跟踪回复</text>
          </view>
          <view class="popup-close" @click="showTrackReplyPicker = false">
            <text>×</text>
          </view>
        </view>

        <!-- 内容区域 -->
        <view class="popup-content">
          <view class="reply-form">
            <view class="form-label">
              <text class="label-text">回复内容</text>
              <text class="label-count">{{ replyContent.length }}/500</text>
            </view>
            <textarea
              class="reply-textarea"
              placeholder="请输入跟踪回复内容..."
              v-model="replyContent"
              maxlength="500"
              :show-confirm-bar="false"
            />
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="popup-footer">
          <view class="footer-buttons">
            <button class="cancel-btn" @click="cancelReply">
              取消
            </button>
            <button
              class="submit-btn"
              @click="submitReply"
              :disabled="!replyContent.trim()"
              :class="{ disabled: !replyContent.trim() }"
            >
              <text class="submit-icon">✨</text>
              <text>发送回复</text>
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { STATUS_CONFIG, PRIORITY_CONFIG } from '../data/constants.js'
import { formatDate } from '../data/utils.js'

export default {
  name: 'TodoDetail',
  data() {
    return {
      todoId: '',
      todoData: null,

      // 弹窗控制
      showStatusPicker: false,
      showPriorityPicker: false,
      showTagPicker: false,
      showTrackReplyPicker: false,

      // 展开控制
      showDeadlineEditor: false,

      // 选择状态
      selectedStatus: '',
      selectedPriority: '',
      selectedTagIds: [],
      selectedDeadline: '',

      // 跟踪回复
      replyContent: '',

      // 基础数据
      allTags: [],

      // 日期时间选择器数据
      dateTimePickerValue: [0, 0, 0, 0, 0],
      dateTimeRange: [],
      tempDate: ''
    }
  },

  computed: {
    statusConfigs() {
      return STATUS_CONFIG
    },

    priorityConfigs() {
      return PRIORITY_CONFIG
    },

    // 格式化显示的截止时间
    formattedSelectedDeadline() {
      if (!this.selectedDeadline) return ''

      const date = new Date(this.selectedDeadline)
      if (isNaN(date.getTime())) return ''

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}`
    }
  },

  onLoad(options) {
    this.todoId = options?.id || 0
  },
  onShow() {
    this.todoId > 0 && this.loadTodoDetail()
    this.loadBasicData()
  },

  methods: {
    // 加载基础数据
    async loadBasicData() {
      try {
        // 加载标签数据
        const tagsRes = await this.$ajax.get('/task/tag/list')
        if (tagsRes?.code == 200) {
          this.allTags = Array.isArray(tagsRes?.data) ? tagsRes.data : []
        }
      } catch (error) {
        console.error('加载基础数据失败:', error)
      }
    },

    // 加载待办详情
    async loadTodoDetail() {
      try {
        uni.showLoading({ title: '加载中...' })

        // 调用API获取待办详情
        const response = await this.$ajax.get('/task/get', { id: this.todoId })

        if (response?.code === 200 && response.data) {
          this.todoData = response.data
          console.log('加载待办详情成功:', this.todoData)

          // 初始化选择状态
          this.selectedStatus = this.todoData.status
          this.selectedPriority = this.todoData.priority
          this.selectedTagIds = this.todoData.tags ? this.todoData.tags.map(tag => tag.id) : []
          this.selectedDeadline = this.todoData.deadline ? this.formatDeadlineFromTimestamp(this.todoData.deadline) : ''
        } else {
          throw new Error(response?.msg || '获取待办详情失败')
        }

        uni.hideLoading()

        // 设置导航栏标题
        uni.setNavigationBarTitle({
          title: '待办详情'
        })



      } catch (error) {
        uni.hideLoading()
        console.error('加载待办详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 200)
      }
    },

    // 预览图片
    previewImage(current, index) {
      uni.previewImage({
        current: index,
        urls: this.todoData.images
      })
    },

    // 格式化截止时间
    formatDeadline(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp * 1000)
      return formatDate(date) + ' ' + date.toTimeString().slice(0, 5)
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp * 1000)
      return formatDate(date) + ' ' + date.toTimeString().slice(0, 5)
    },

    // 获取状态配置（参考save页面）
    getStatusConfig(statusName) {
      const statusKey = Object.keys(STATUS_CONFIG).find(key =>
        STATUS_CONFIG[key].name === statusName
      )
      return STATUS_CONFIG[statusKey] || STATUS_CONFIG.not_started
    },

    // 获取优先级配置（参考save页面）
    getPriorityConfig(priorityName) {
      const priorityKey = Object.keys(PRIORITY_CONFIG).find(key =>
        PRIORITY_CONFIG[key].name === priorityName
      )
      return PRIORITY_CONFIG[priorityKey] || PRIORITY_CONFIG[2]
    },

    // 选择状态
    selectStatus(status) {
      this.selectedStatus = status
    },

    // 选择优先级
    selectPriority(priority) {
      this.selectedPriority = priority
    },

    // 切换标签选择
    toggleTag(tag) {
      const index = this.selectedTagIds.indexOf(tag.id)
      if (index > -1) {
        this.selectedTagIds.splice(index, 1)
      } else {
        this.selectedTagIds.push(tag.id)
      }
    },

    // 更新状态
    async updateStatus() {
      if (this.selectedStatus === this.todoData.status) {
        this.showStatusPicker = false
        this.$toast('状态无变化')
        return
      }

      try {
        uni.showLoading({ title: '更新中...' })

        const response = await this.$ajax.post('/task/save', {
          id: this.todoId,
          categoryId: this.todoData.categoryId,
          content: this.todoData.content,
          images: JSON.stringify(this.todoData.images || []),
          status: this.selectedStatus,
          priority: this.todoData.priority,
          deadline: this.todoData.deadline,
          tags: this.formatTagNames(this.todoData.tags ? this.todoData.tags.map(tag => tag.id) : [])
        })

        uni.hideLoading()

        if (response?.code == 200) {
          this.$toast('状态更新成功')
          this.showStatusPicker = false
          // 重新加载详情
          setTimeout(() => {
            this.loadTodoDetail()
          }, 500)
        } else {
          this.$toast(response?.msg || '更新失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('更新状态失败:', error)
        this.$toast('更新失败')
      }
    },

    // 更新优先级
    async updatePriority() {
      if (this.selectedPriority === this.todoData.priority) {
        this.showPriorityPicker = false
        this.$toast('优先级无变化')
        return
      }

      try {
        uni.showLoading({ title: '更新中...' })

        const response = await this.$ajax.post('/task/save', {
          id: this.todoId,
          categoryId: this.todoData.categoryId,
          content: this.todoData.content,
          images: JSON.stringify(this.todoData.images || []),
          status: this.todoData.status,
          priority: this.selectedPriority,
          deadline: this.todoData.deadline,
          tags: this.formatTagNames(this.todoData.tags ? this.todoData.tags.map(tag => tag.id) : [])
        })

        uni.hideLoading()

        if (response?.code == 200) {
          this.$toast('优先级更新成功')
          this.showPriorityPicker = false
          // 重新加载详情
          setTimeout(() => {
            this.loadTodoDetail()
          }, 500)
        } else {
          this.$toast(response?.msg || '更新失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('更新优先级失败:', error)
        this.$toast('更新失败')
      }
    },

    // 更新标签
    async updateTags() {
      // 检查标签是否有变化
      const currentTagIds = this.todoData.tags ? this.todoData.tags.map(tag => tag.id).sort() : []
      const selectedTagIds = [...this.selectedTagIds].sort()

      if (JSON.stringify(currentTagIds) === JSON.stringify(selectedTagIds)) {
        this.showTagPicker = false
        this.$toast('标签无变化')
        return
      }

      try {
        uni.showLoading({ title: '更新中...' })

        const response = await this.$ajax.post('/task/save', {
          id: this.todoId,
          categoryId: this.todoData.categoryId,
          content: this.todoData.content,
          images: JSON.stringify(this.todoData.images || []),
          status: this.todoData.status,
          priority: this.todoData.priority,
          deadline: this.todoData.deadline,
          tags: this.formatTagNames(this.selectedTagIds)
        })

        uni.hideLoading()

        if (response?.code == 200) {
          this.$toast('标签更新成功')
          this.showTagPicker = false
          // 重新加载详情
          setTimeout(() => {
            this.loadTodoDetail()
          }, 500)
        } else {
          this.$toast(response?.msg || '更新失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('更新标签失败:', error)
        this.$toast('更新失败')
      }
    },

    // 更新截止时间
    async updateDeadline() {
      // 检查截止时间是否有变化
      let newDeadline = 0
      if (this.selectedDeadline) {
        newDeadline = Math.floor(new Date(this.selectedDeadline).getTime() / 1000)
      }

      if (newDeadline === (this.todoData.deadline || 0)) {
        this.showDeadlineEditor = false
        this.$toast('截止时间无变化')
        return
      }

      try {
        uni.showLoading({ title: '更新中...' })

        // 转换截止时间为时间戳
        let deadline = newDeadline

        const response = await this.$ajax.post('/task/save', {
          id: this.todoId,
          categoryId: this.todoData.categoryId,
          content: this.todoData.content,
          images: JSON.stringify(this.todoData.images || []),
          status: this.todoData.status,
          priority: this.todoData.priority,
          deadline: deadline,
          tags: this.formatTagNames(this.todoData.tags ? this.todoData.tags.map(tag => tag.id) : [])
        })

        uni.hideLoading()

        if (response?.code == 200) {
          this.$toast('截止时间更新成功')
          this.showDeadlineEditor = false
          // 重新加载详情
          setTimeout(() => {
            this.loadTodoDetail()
          }, 300)
        } else {
          this.$toast(response?.msg || '更新失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('更新截止时间失败:', error)
        this.$toast('更新失败')
      }
    },





        // 切换截止时间编辑器
    toggleDeadlineEditor() {
      this.showDeadlineEditor = !this.showDeadlineEditor
      if (this.showDeadlineEditor) {
        // 初始化编辑器
        this.initDateTimePicker()
      }
    },

    // 取消截止时间编辑
    cancelDeadlineEdit() {
      this.showDeadlineEditor = false
      // 重置选择状态
      this.selectedDeadline = this.todoData.deadline ? this.formatDeadlineFromTimestamp(this.todoData.deadline) : ''
    },

    // 打开日期时间picker
    openDateTimePicker() {
      this.$nextTick(() => {
        if (this.$refs.dateTimePicker) {
          // 模拟点击picker
          const picker = this.$refs.dateTimePicker
          if (picker.$el) {
            picker.$el.click()
          }
        }
      })
    },

    // 初始化日期时间选择器
    initDateTimePicker() {
      const now = new Date()
      const currentYear = now.getFullYear()

      // 年份范围：当前年份到5年后
      const years = []
      for (let i = currentYear; i <= currentYear + 5; i++) {
        years.push(i + '年')
      }

      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push(String(i).padStart(2, '0') + '月')
      }

      const days = []
      for (let i = 1; i <= 31; i++) {
        days.push(String(i).padStart(2, '0') + '日')
      }

      const hours = []
      for (let i = 0; i <= 23; i++) {
        hours.push(String(i).padStart(2, '0') + '时')
      }

      const minutes = []
      for (let i = 0; i <= 59; i++) {
        minutes.push(String(i).padStart(2, '0') + '分')
      }

      this.dateTimeRange = [years, months, days, hours, minutes]

      // 如果有现有的截止时间，设置为默认值
      if (this.selectedDeadline) {
        const date = new Date(this.selectedDeadline)
        this.dateTimePickerValue = [
          date.getFullYear() - currentYear,
          date.getMonth(),
          date.getDate() - 1,
          date.getHours(),
          date.getMinutes()
        ]
      } else {
        // 设置默认值为当前时间
        this.dateTimePickerValue = [
          0,
          now.getMonth(),
          now.getDate() - 1,
          now.getHours(),
          now.getMinutes()
        ]
      }
    },

    // 日期时间列变化
    onDateTimeColumnChange(e) {
      const { column, value } = e.detail
      this.dateTimePickerValue[column] = value

      // 如果改变了年份或月份，需要更新天数
      if (column === 0 || column === 1) {
        this.updateDaysInMonth()
      }
    },

    // 更新月份天数
    updateDaysInMonth() {
      const yearIndex = this.dateTimePickerValue[0]
      const monthIndex = this.dateTimePickerValue[1]

      const year = parseInt(this.dateTimeRange[0][yearIndex])
      const month = monthIndex + 1

      const daysInMonth = new Date(year, month, 0).getDate()
      const days = []
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(String(i).padStart(2, '0') + '日')
      }

      this.dateTimeRange[2] = days

      // 如果当前选中的日期超过了该月的天数，调整为最后一天
      if (this.dateTimePickerValue[2] >= daysInMonth) {
        this.dateTimePickerValue[2] = daysInMonth - 1
      }
    },

    // 截止时间变化
    onDeadlineChange(e) {
      const values = e.detail.value

      // 构建日期时间字符串
      const yearStr = this.dateTimeRange[0][values[0]].replace('年', '')
      const monthStr = this.dateTimeRange[1][values[1]].replace('月', '')
      const dayStr = this.dateTimeRange[2][values[2]].replace('日', '')
      const hourStr = this.dateTimeRange[3][values[3]].replace('时', '')
      const minuteStr = this.dateTimeRange[4][values[4]].replace('分', '')

      this.dateTimePickerValue = values
      const dateTimeStr = `${yearStr}-${monthStr}-${dayStr} ${hourStr}:${minuteStr}:00`
      this.selectedDeadline = dateTimeStr
    },



        // 快捷设置截止时间
    setQuickDeadline(type) {
      const now = new Date()
      let targetDate = null

      switch (type) {
        case 'today':
          targetDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59)
          break
        case 'tomorrow':
          targetDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 23, 59)
          break
        case 'thisweek':
          // 本周六23:59
          const daysUntilSaturday = (6 - now.getDay()) % 7
          const thisSaturday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + daysUntilSaturday, 23, 59)
          // 如果今天就是周六，则设为今天23:59
          targetDate = daysUntilSaturday === 0 && now.getDay() === 6 ?
            new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59) : thisSaturday
          break
        case 'monthend':
          // 本月最后一天23:59
          const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
          targetDate = new Date(lastDayOfMonth.getFullYear(), lastDayOfMonth.getMonth(), lastDayOfMonth.getDate(), 23, 59)
          break
      }

      if (targetDate) {
        const year = targetDate.getFullYear()
        const month = String(targetDate.getMonth() + 1).padStart(2, '0')
        const day = String(targetDate.getDate()).padStart(2, '0')
        const hour = String(targetDate.getHours()).padStart(2, '0')
        const minute = String(targetDate.getMinutes()).padStart(2, '0')

        this.selectedDeadline = `${year}-${month}-${day} ${hour}:${minute}:00`

        // 同步更新picker的值
        this.updatePickerValue(targetDate)
      }
    },

    // 清除截止时间
    clearDeadline() {
      this.selectedDeadline = ''
    },

    // 更新picker的值
    updatePickerValue(date) {
      const currentYear = new Date().getFullYear()
      this.dateTimePickerValue = [
        date.getFullYear() - currentYear,
        date.getMonth(),
        date.getDate() - 1,
        date.getHours(),
        date.getMinutes()
      ]
    },





    // 从时间戳格式化截止时间
    formatDeadlineFromTimestamp(timestamp) {
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}:00`
    },

    // 工具方法：格式化标签名称数组
    formatTagNames(tagIds) {
      if (!Array.isArray(tagIds) || tagIds.length === 0) {
        return []
      }

      // 根据标签ID获取标签名称
      const tagNames = tagIds.map(tagId => {
        const tag = this.allTags.find(t => t.id === tagId)
        return tag ? tag.name : null
      }).filter(name => name !== null)

      return tagNames
    },

    // 编辑待办
    handleEdit() {
      uni.navigateTo({
        url: `/pages_task/detail/save?id=${this.todoId}`
      })
    },

    // 删除待办
    handleDelete() {
      this.$confirm1('确定要删除这个待办事项吗？', '删除确认', {
        confirmText: '删除',
        cancelText: '取消'
      }).then(async () => {
        try {
          uni.showLoading({ title: '删除中...' })
          await this.$ajax.post('/task/delete', { id: this.todoId })
          uni.hideLoading()

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 200)

        } catch (error) {
          uni.hideLoading()
          console.error('删除失败:', error)
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 取消回复
    cancelReply() {
      this.replyContent = ''
      this.showTrackReplyPicker = false
    },

    // 提交回复
    async submitReply() {
      if (!this.replyContent.trim()) {
        uni.showToast({
          title: '请输入回复内容',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '发送中...' })

        const response = await this.$ajax.post('/task/reply', {
          id: this.todoId,
          content: this.replyContent.trim()
        })

        uni.hideLoading()

        if (response?.code == 200) {
          this.$toast('回复发送成功')
          this.replyContent = ''
          this.showTrackReplyPicker = false

          // 重新加载详情以获取最新的回复记录
          setTimeout(() => {
            this.loadTodoDetail()
          }, 200)
        } else {
          this.$toast(response?.msg || '发送失败')
        }

      } catch (error) {
        uni.hideLoading()
        console.error('发送回复失败:', error)
        this.$toast('发送失败')
      }
    },

    // 工具方法：解析JSON字符串为数组
    parseJsonArray(jsonStr) {
      try {
        return JSON.parse(jsonStr) || []
      } catch (error) {
        console.warn('解析JSON数组失败:', error)
        return []
      }
    },

    // 格式化文本中的链接
    formatTextWithLinks(text) {
      if (!text) return ''

      // URL匹配正则表达式，包含#字符用于锚点
      const urlRegex = /http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+#]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/g

      // 替换URL为可点击的链接
      return text.replace(urlRegex, (url) => {
        return `<a href="${url}" target="_blank" style="color: #1890ff; text-decoration: underline; word-break: break-all;">${url}</a>`
      })
    },

    // 删除回复
    deleteReply(replyId) {
      this.$confirm1('确定要删除这条回复吗？', '删除确认', {
        confirmText: '删除',
        cancelText: '取消'
      }).then(async () => {
        try {
          uni.showLoading({ title: '删除中...' })

          const response = await this.$ajax.post('/task/reply/delete', {
            id: replyId
          })

          uni.hideLoading()

          if (response?.code == 200) {
            this.$toast('删除成功')
            // 重新加载详情以刷新回复列表
            setTimeout(() => {
              this.loadTodoDetail()
            }, 200)
          } else {
            this.$toast(response?.msg || '删除失败')
          }

        } catch (error) {
          uni.hideLoading()
          console.error('删除回复失败:', error)
          this.$toast('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.todo-detail-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: $spacing-lg;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

.detail-container {
  max-width: 750rpx;
  margin: 0 auto;

  .content-card {
    background-color: $card-background;
    border-radius: $border-radius-lg;
    padding: 40rpx 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    position: relative;

    .top-badge {
      position: absolute;
      top: 12rpx;
      right: 12rpx;
      font-size: 32rpx;
      line-height: 1;
      z-index: 2;
    }

    .todo-content {
      font-size: 32rpx;
      color: $text-primary;
      line-height: 1.4;
      white-space: pre-wrap;
      word-break: break-all;
      word-wrap: break-word;
      padding-right: 50rpx; // 为置顶标识留出空间

      // 链接样式
      :deep(a) {
        color: #1890ff;
        text-decoration: underline;
        word-break: break-all;
        transition: color 0.2s ease;

        &:hover {
          color: #40a9ff;
        }

        &:active {
          color: #096dd9;
        }
      }
    }
  }

  .property-card {
    background-color: $card-background;
    border-radius: $border-radius-lg;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .property-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      min-height: 88rpx;
      transition: background-color 0.2s ease;

      &:not(:last-child) {
        border-bottom: 1rpx solid $border-color;
      }

      &:active {
        background-color: $background-color;
      }

      .property-label {
        font-size: 30rpx;
        font-weight: 600;
        color: $text-primary;
        flex-shrink: 0;
      }

      .property-value {
        display: flex;
        align-items: center;
        gap: 16rpx;
        flex: 1;
        justify-content: flex-end;

        .property-arrow {
          font-family: 'iconfont';
          font-size: 28rpx;
          color: #C8C9CC;
          font-weight: 400;
          flex-shrink: 0;
        }

        .status-badge, .category-badge, .priority-badge {
          display: inline-flex;
          align-items: center;
          gap: 8rpx;
          padding: 12rpx 20rpx;
          border-radius: 24rpx;
          font-size: 26rpx;
          font-weight: 500;
          border: 2rpx solid;

          .status-icon, .category-icon, .priority-icon {
            font-size: 20rpx;
            line-height: 1;
          }

          .status-name, .category-name, .priority-name {
            line-height: 1.2;
          }
        }

        .tags-row {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;
          max-width: 400rpx;

          .tag-badge {
            display: inline-flex;
            align-items: center;
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            font-weight: 500;
            border: 2rpx solid;
          }
        }
      }
    }

    // 标签项特殊样式（2行布局）
    .tags-item {
      flex-direction: column;
      align-items: stretch;
      padding: 0;

      .property-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        min-height: 88rpx;

        .property-label {
          font-size: 30rpx;
          font-weight: 600;
          color: $text-primary;
        }

        .property-arrow {
          font-family: 'iconfont';
          font-size: 28rpx;
          color: #C8C9CC;
          font-weight: 400;
        }
      }

      .tags-content {
        padding: 0 32rpx 32rpx;
        margin-top: -16rpx;

        .tags-row {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;

          .tag-badge {
            display: inline-flex;
            align-items: center;
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            font-weight: 500;
            border: 2rpx solid;
          }
        }
      }
    }

    // 时间信息和图片卡片特殊样式
    &.time-info-card, &.image-info-card, &.timeline-card {
      .property-header {
        padding: 32rpx;
        border-bottom: 1rpx solid $border-color;

        .property-title {
          font-size: 30rpx;
          font-weight: 600;
          color: $text-primary;
        }
      }

      .property-content {
        padding: 0 32rpx 32rpx;

                  .time-list {
            .time-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 20rpx 0;
              cursor: pointer;
              transition: background-color 0.2s ease;
              border-radius: 8rpx;

              &:not(:last-child) {
                border-bottom: 1rpx solid $border-color;
              }

              &:active {
                background-color: rgba(0, 0, 0, 0.02);
              }

              &.deadline-item {
                border-bottom: none;
              }

              .time-label {
                font-size: 26rpx;
                color: $text-secondary;
                font-weight: 500;
              }

              .time-value {
                font-size: 26rpx;
                color: $text-primary;
                font-weight: 600;
                text-align: right;
                min-width: 280rpx;
                flex-shrink: 0;
              }
            }

            // 截止时间编辑器样式
            .deadline-editor {
              background-color: rgba(24, 144, 255, 0.02);
              border: 1rpx solid rgba(24, 144, 255, 0.1);
              border-radius: 12rpx;
              margin: 16rpx 0;
              animation: slideDown 0.3s ease-out;

              .editor-content {
                padding: 24rpx;

                .datetime-selector {
                  .datetime-display {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 20rpx;
                    background-color: rgba(0, 0, 0, 0.02);
                    border-radius: 8rpx;
                    border: 1rpx solid rgba(0, 0, 0, 0.05);
                    font-size: 26rpx;
                    color: $text-primary;
                    transition: all $transition-fast;
                    margin-bottom: 24rpx;

                                         .datetime-text {
                       flex: 1;
                     }

                     .datetime-actions {
                       display: flex;
                       align-items: center;
                       gap: 8rpx;

                       .clear-icon {
                         opacity: 0.7;
                         transition: opacity 0.2s ease;

                         &:active {
                           opacity: 1;
                           transform: scale(1.1);
                         }
                       }

                       .arrow {
                         color: $text-light;
                         font-size: 28rpx;
                         flex-shrink: 0;
                       }
                     }

                    &:active {
                      background-color: rgba(0, 0, 0, 0.05);
                    }
                  }

                  .quick-actions {
                    .quick-title {
                      font-size: 24rpx;
                      color: $text-secondary;
                      font-weight: 600;
                      margin-bottom: 16rpx;
                    }

                    .quick-buttons {
                      display: flex;
                      gap: 8rpx;
                      flex-wrap: wrap;

                      .quick-btn {
                        flex: 1;
                        min-width: calc(25% - 6rpx);
                        height: 56rpx;
                        border-radius: 8rpx;
                        background-color: rgba(24, 144, 255, 0.1);
                        color: #1890ff;
                        font-size: 22rpx;
                        font-weight: 500;
                        border: 1rpx solid rgba(24, 144, 255, 0.2);
                        transition: all $transition-fast;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                                                 &:active {
                           background-color: rgba(24, 144, 255, 0.2);
                           transform: scale(0.98);
                         }
                      }
                    }
                  }
                }

                .editor-buttons {
                  display: flex;
                  gap: 12rpx;
                  margin-top: 24rpx;

                  .cancel-btn {
                    flex: 1;
                    height: 64rpx;
                    border-radius: 8rpx;
                    background-color: rgba(0, 0, 0, 0.05);
                    color: $text-secondary;
                    font-size: 24rpx;
                    font-weight: 500;
                    border: none;
                    transition: all $transition-fast;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &:active {
                      background-color: rgba(0, 0, 0, 0.1);
                      transform: scale(0.98);
                    }
                  }

                  .confirm-btn {
                    flex: 2;
                    height: 64rpx;
                    border-radius: 8rpx;
                    background-color: $primary-color;
                    color: $white;
                    font-size: 24rpx;
                    font-weight: 600;
                    border: none;
                    transition: all $transition-fast;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);

                    &:active {
                      background-color: $primary-dark;
                      transform: scale(0.98);
                    }
                  }
                }
              }
            }
          }

          // 滑动动画
          @keyframes slideDown {
            from {
              opacity: 0;
              transform: translateY(-20rpx);
              max-height: 0;
            }
            to {
              opacity: 1;
              transform: translateY(0);
              max-height: 1000rpx;
            }
          }

        .images-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .detail-image {
            width: 200rpx;
            height: 200rpx;
            border-radius: 16rpx;
            border: 1rpx solid $border-color;
          }
        }

        // 时间线样式
        .timeline-list {
          .timeline-item {
            position: relative;
            display: flex;
            align-items: flex-start;
            padding: 16rpx 0;
            padding-left: 60rpx;

            &:not(.is-last) {
              &::after {
                content: '';
                position: absolute;
                left: 19rpx;
                top: 50rpx;
                width: 2rpx;
                height: calc(100% - 32rpx);
                background-color: #E5E7EB;
              }
            }

            .timeline-dot {
              position: absolute;
              left: 12rpx;
              top: 24rpx;
              width: 16rpx;
              height: 16rpx;
              border-radius: 50%;
              background-color: $primary-color;
              border: 3rpx solid $white;
              box-shadow: 0 0 0 2rpx #E5E7EB;
              z-index: 1;
            }

            .timeline-content {
              flex: 1;
              margin-left: 0;

              .timeline-text {
                font-size: 26rpx;
                color: $text-primary;
                line-height: 1.4;
                margin-bottom: 8rpx;
                white-space: pre-wrap;
                word-break: break-all;
                word-wrap: break-word;

                // 链接样式
                :deep(a) {
                  color: #1890ff;
                  text-decoration: underline;
                  word-break: break-all;
                  transition: color 0.2s ease;

                  &:hover {
                    color: #40a9ff;
                  }

                  &:active {
                    color: #096dd9;
                  }
                }
              }

              .timeline-time {
                font-size: 22rpx;
                color: $text-light;
              }
            }

            // 系统记录样式
            &.system-log {
              .timeline-dot {
                width: 12rpx;
                height: 12rpx;
                background-color: #9CA3AF;
                border: 2rpx solid $white;
                box-shadow: 0 0 0 1rpx #E5E7EB;
                top: 22rpx;
                left: 14rpx;
              }

              .timeline-content {
                .timeline-text {
                  font-size: 24rpx;
                  color: $text-secondary;
                  line-height: 1.3;
                  margin-bottom: 4rpx;
                  white-space: pre-wrap;
                  word-break: break-all;
                  word-wrap: break-word;

                  // 链接样式
                  :deep(a) {
                    color: #1890ff;
                    text-decoration: underline;
                    word-break: break-all;
                    transition: color 0.2s ease;

                    &:hover {
                      color: #40a9ff;
                    }

                    &:active {
                      color: #096dd9;
                    }
                  }
                }

                .timeline-time {
                  font-size: 20rpx;
                  color: $text-light;
                }

              .timeline-time-row {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 16rpx;

                .timeline-time {
                  font-size: 20rpx;
                  color: $text-light;
                }

                .delete-btn {
                  font-size: 22rpx;
                  color: #999;
                  transition: all 0.2s ease;
                  cursor: pointer;
                  padding: 4rpx;

                  &:active {
                    color: #ff4d4f;
                    transform: scale(1.2);
                  }
                }
              }
              }
            }

            // 用户回复样式
            &.user-reply {
              padding: 20rpx 0;

              &:not(.is-last) {
                &::after {
                  top: 54rpx;
                  height: calc(100% - 40rpx);
                }
              }

              .timeline-dot {
                background-color: #1890ff;
                top: 28rpx;
              }

              .timeline-content {
                .reply-header {
                  display: flex;
                  align-items: center;
                  gap: 12rpx;
                  margin-bottom: 12rpx;

                  .user-avatar {
                    width: 50rpx;
                    height: 50rpx;
                    border-radius: 50%;
                    background-color: #f5f5f5;
                    border: 1rpx solid #E5E7EB;
                  }

                  .user-info {
                    .user-name {
                      font-size: 26rpx;
                      color: $text-primary;
                      font-weight: 600;
                      margin-bottom: 2rpx;
                    }

                    .reply-time {
                      font-size: 20rpx;
                      color: $text-light;
                    }

                    .reply-time-row {
                      display: flex;
                      align-items: center;
                      justify-content: flex-end;
                      gap: 16rpx;

                      .reply-time {
                        font-size: 20rpx;
                        color: $text-light;
                      }

                      .delete-btn {
                        font-size: 22rpx;
                        color: #999;
                        transition: all 0.2s ease;
                        cursor: pointer;
                        padding: 4rpx;

                        &:active {
                          color: #ff4d4f;
                          transform: scale(1.2);
                        }
                      }
                    }
                  }
                }

                .reply-content {
                  background-color: rgba(24, 144, 255, 0.05);
                  border: 1rpx solid rgba(24, 144, 255, 0.2);
                  border-radius: 8rpx;
                  padding: 16rpx 20rpx;
                  margin-left: 0;

                  .reply-text {
                    font-size: 26rpx;
                    color: $text-primary;
                    line-height: 1.4;
                    white-space: pre-wrap;
                    word-break: break-all;
                    word-wrap: break-word;

                    // 链接样式
                    :deep(a) {
                      color: #1890ff;
                      text-decoration: underline;
                      word-break: break-all;
                      transition: color 0.2s ease;

                      &:hover {
                        color: #40a9ff;
                      }

                      &:active {
                        color: #096dd9;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  font-size: $font-size-lg;
  color: $text-light;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $card-background;
  padding: 20rpx;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.06);
  z-index: 100;

  .action-buttons {
    display: flex;
    gap: 12rpx;

    .btn {
      height: 72rpx;
      border-radius: $border-radius-md;
      font-size: 24rpx;
      font-weight: 500;
      transition: all $transition-fast;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6rpx;

      .btn-icon {
        font-size: 22rpx;
        line-height: 1;
      }

      &.btn-track {
        flex: 2; // 50% 宽度
        background-color: $white;
        color: $text-primary;
        border: 2rpx solid $border-color;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

        &:active {
          background-color: #f5f5f5;
          transform: scale(0.98);
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
        }
      }

      &.btn-edit {
        flex: 1; // 25% 宽度
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: $white;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);

        &:active {
          background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
          transform: scale(0.98);
          box-shadow: 0 2rpx 12rpx rgba(24, 144, 255, 0.4);
        }
      }

      &.btn-delete {
        flex: 1; // 25% 宽度
        background-color: #ff4d4f;
        color: $white;
        box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);

        &:active {
          background-color: #cf1322;
          transform: scale(0.98);
        }
      }
    }
  }
}

// 通用弹窗样式
.picker-popup {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .picker-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .picker-close {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: $text-secondary;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .picker-content {
    padding: 32rpx 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .status-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .status-option {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 16rpx 20rpx;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 12rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.05);
        transition: all $transition-fast;
        cursor: pointer;
        width: calc(50% - 8rpx);
        min-height: 72rpx;

        .status-icon {
          font-size: 24rpx;
          line-height: 1;
          flex-shrink: 0;
        }

        .status-name {
          font-size: 26rpx;
          color: $text-secondary;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
        }

        &.active {
          border-color: $primary-color;
          background-color: $primary-light;
          transform: scale(1.02);

          .status-name {
            color: $primary-color;
            font-weight: 600;
          }
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .priority-options {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .priority-option {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 16rpx 20rpx;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 12rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.05);
        transition: all $transition-fast;
        cursor: pointer;
        width: calc(33.33% - 8rpx);
        min-height: 72rpx;

        .priority-icon {
          font-size: 24rpx;
          line-height: 1;
          flex-shrink: 0;
        }

        .priority-name {
          font-size: 26rpx;
          color: $text-secondary;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
        }

        &.active {
          border-color: $primary-color;
          background-color: $primary-light;
          transform: scale(1.02);

          .priority-name {
            color: $primary-color;
            font-weight: 600;
          }
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .tags-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .tag-chip {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 16rpx 20rpx;
        border-radius: 24rpx;
        border: 2rpx solid;
        font-size: 26rpx;
        font-weight: 500;
        transition: all $transition-fast;
        cursor: pointer;
        min-width: 80rpx;
        justify-content: center;

        .tag-name {
          line-height: 1.2;
          white-space: nowrap;
        }

        .tag-check {
          font-size: 20rpx;
          font-weight: 600;
          margin-left: 4rpx;
        }

        &.selected {
          transform: scale(1.02);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .datetime-selector {
      .datetime-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 12rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.05);
        font-size: 28rpx;
        color: $text-primary;
        transition: all $transition-fast;
        margin-bottom: 32rpx;

        .datetime-text {
          flex: 1;
        }

        .arrow {
          color: $text-light;
          font-size: 32rpx;
          flex-shrink: 0;
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }

      .quick-actions {
        .quick-title {
          font-size: 26rpx;
          color: $text-secondary;
          font-weight: 600;
          margin-bottom: 20rpx;
        }

        .quick-buttons {
          display: flex;
          gap: 12rpx;
          flex-wrap: wrap;

          .quick-btn {
            flex: 1;
            min-width: calc(25% - 9rpx);
            height: 64rpx;
            border-radius: 12rpx;
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            font-size: 24rpx;
            font-weight: 500;
            border: 2rpx solid rgba(24, 144, 255, 0.2);
            transition: all $transition-fast;
            display: flex;
            align-items: center;
            justify-content: center;

            &:active {
              background-color: rgba(24, 144, 255, 0.2);
              transform: scale(0.98);
            }

            &:last-child {
              background-color: rgba(255, 77, 79, 0.1);
              color: #ff4d4f;
              border-color: rgba(255, 77, 79, 0.2);

              &:active {
                background-color: rgba(255, 77, 79, 0.2);
              }
            }
          }
        }
      }
    }

    .date-picker {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 12rpx;
      border: 2rpx solid rgba(0, 0, 0, 0.05);
      font-size: 28rpx;
      color: $text-primary;
      transition: all $transition-fast;

      .arrow {
        color: $text-light;
        font-size: 32rpx;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }

  // 底部按钮组
  .picker-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: $white;

    .button-group {
      display: flex;
      gap: 16rpx;

      .cancel-btn {
        flex: 1;
        height: 72rpx;
        border-radius: 12rpx;
        background-color: rgba(0, 0, 0, 0.05);
        color: $text-secondary;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.98);
        }
      }

      .confirm-btn {
        flex: 2;
        height: 72rpx;
        border-radius: 12rpx;
        background-color: $primary-color;
        color: $white;
        font-size: 26rpx;
        font-weight: 600;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }
      }
    }
  }
}

// 跟踪回复弹窗样式
.track-reply-popup {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(24, 144, 255, 0.1);
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(64, 169, 255, 0.02) 100%);

    .header-left {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .header-icon {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
      }

      .popup-title {
        font-size: 36rpx;
        font-weight: 600;
        color: $text-primary;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .popup-close {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(24, 144, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: #1890ff;
        line-height: 1;
      }

      &:active {
        background-color: rgba(24, 144, 255, 0.2);
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .popup-content {
    padding: 40rpx;

    .reply-form {
      .form-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .label-text {
          font-size: 28rpx;
          font-weight: 600;
          color: $text-primary;
        }

        .label-count {
          font-size: 24rpx;
          color: $text-light;
          background-color: rgba(24, 144, 255, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 16rpx;
        }
      }

      .reply-textarea {
        width: 100%;
        height: 300rpx;
        max-height: 400rpx;
        padding: 24rpx;
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(64, 169, 255, 0.02) 100%);
        border: 2rpx solid rgba(24, 144, 255, 0.2);
        border-radius: 16rpx;
        font-size: 28rpx;
        color: $text-primary;
        line-height: 1.5;
        transition: all $transition-fast;
        resize: none;
        overflow-y: auto;
        box-sizing: border-box;

        &:focus {
          border-color: #1890ff;
          background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(64, 169, 255, 0.05) 100%);
          box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
        }

        &::placeholder {
          color: $text-placeholder;
        }
      }
    }
  }

  // 底部按钮
  .popup-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(24, 144, 255, 0.1);
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(64, 169, 255, 0.02) 100%);

    .footer-buttons {
      display: flex;
      gap: 20rpx;

      .cancel-btn {
        flex: 1;
        height: 72rpx;
        border-radius: 16rpx;
        background-color: rgba(0, 0, 0, 0.05);
        color: $text-secondary;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.98);
        }
      }

      .submit-btn {
        flex: 2;
        height: 72rpx;
        border-radius: 16rpx;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: $white;
        font-size: 26rpx;
        font-weight: 600;
        border: none;
        transition: all $transition-fast;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.4);

        .submit-icon {
          font-size: 22rpx;
          line-height: 1;
        }

        &:active {
          background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
          transform: scale(0.98);
          box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.5);
        }

        &.disabled {
          background: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          transform: none;

          &:active {
            transform: none;
            box-shadow: none;
          }
        }
      }
    }
  }
}
</style>
