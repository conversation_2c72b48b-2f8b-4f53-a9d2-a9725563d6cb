# 待办记事模块 API 文档

## 概述

待办记事模块提供完整的任务管理功能，包括分类管理、标签管理和待办事项管理。所有接口都需要用户认证，通过 `Authorization` 头部传递 token。

## 基础信息

- **模块路径**: `/task`
- **认证方式**: <PERSON><PERSON> (Header: `Authorization`)
- **响应格式**: JSON
- **字符编码**: UTF-8

## 分类管理 API

### 1. 保存分类

**接口地址**: `POST /task/category/save`

**功能说明**: 新增或编辑分类（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 分类ID，0为新增，>0为编辑
  "name": "工作任务",          // 分类名称，必填
  "icon": "💼",               // emoji图标，可选，默认📝
  "color": "blue",            // 背景颜色，可选，默认primary
  "weight": 100               // 排序权重，可选，默认0，数值越大越靠前
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存分类",
  "data": 123                 // 新增时返回分类ID，编辑时返回true
}
```

### 2. 删除分类

**接口地址**: `POST /task/category/delete`

**功能说明**: 软删除分类（分类下有待办事项时不允许删除）

**请求参数**:
```json
{
  "id": 123                   // 分类ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除分类",
  "data": true
}
```

### 3. 获取分类列表

**接口地址**: `GET /task/category/list`

**功能说明**: 获取用户的分类列表

**请求参数**:
```
withCount: true             // 是否统计每个分类下的待办事项数量，可选
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类列表成功",
  "data": [
    {
      "id": 1,
      "name": "工作任务",
      "icon": "💼",
      "color": "blue",
      "weight": 100,
      "timeCreate": 1640995200,
      "taskCount": 5            // 当withCount=true时返回
    }
  ]
}
```

### 4. 获取分类详情

**接口地址**: `GET /task/category/get`

**功能说明**: 获取单个分类的详细信息

**请求参数**:
```
id: 123                     // 分类ID，必填
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类详情成功",
  "data": {
    "id": 1,
    "name": "工作任务",
    "icon": "💼",
    "color": "blue",
    "weight": 100,
    "timeCreate": 1640995200
  }
}
```

## 标签管理 API

### 1. 保存标签

**接口地址**: `POST /task/tag/save`

**功能说明**: 新增或编辑标签（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 标签ID，0为新增，>0为编辑
  "name": "重要",             // 标签名称，必填
  "color": "red"              // 背景颜色，可选，默认default
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存标签",
  "data": 456                 // 新增时返回标签ID，编辑时返回true
}
```

### 2. 删除标签

**接口地址**: `POST /task/tag/delete`

**功能说明**: 软删除标签（同时删除相关联的关系）

**请求参数**:
```json
{
  "id": 456                   // 标签ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除标签",
  "data": true
}
```

### 3. 获取标签列表

**接口地址**: `GET /task/tag/list`

**功能说明**: 获取用户的标签列表

**请求参数**:
```
withCount: true             // 是否统计每个标签的使用次数，可选
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取标签列表成功",
  "data": [
    {
      "id": 1,
      "name": "重要",
      "color": "red",
      "timeCreate": 1640995200,
      "useCount": 3             // 当withCount=true时返回
    }
  ]
}
```

### 4. 获取标签详情

**接口地址**: `GET /task/tag/get`

**功能说明**: 获取单个标签的详细信息

**请求参数**:
```
id: 456                     // 标签ID，必填
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取标签详情成功",
  "data": {
    "id": 1,
    "name": "重要",
    "color": "red",
    "timeCreate": 1640995200
  }
}
```

### 5. 搜索标签

**接口地址**: `GET /task/tag/search`

**功能说明**: 根据关键词搜索标签（用于输入时的自动补全）

**请求参数**:
```
keyword: 重要               // 搜索关键词，必填
limit: 10                   // 返回数量限制，可选，默认10
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "搜索标签成功",
  "data": [
    {
      "id": 1,
      "name": "重要",
      "color": "red"
    }
  ]
}
```

## 待办事项管理 API

### 1. 保存待办事项

**接口地址**: `POST /task/save`

**功能说明**: 新增或编辑待办事项（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 待办事项ID，0为新增，>0为编辑
  "categoryId": 1,            // 分类ID，可选，0或不传则使用默认分类
  "content": "完成项目文档",   // 待办事项内容，必填
  "images": "[\"http://example.com/1.jpg\"]",  // 图片链接数组JSON字符串，可选
  "status": "未开始",         // 状态，可选，默认"未开始"
  "priority": "高",           // 优先级，可选，允许为空
  "deadline": 1641081600,     // 截止时间戳，可选，0表示无截止时间
  "tagIds": "[1,2,3]"         // 标签ID数组JSON字符串，可选
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存待办事项",
  "data": 789                 // 新增时返回待办事项ID，编辑时返回true
}
```

### 2. 删除待办事项

**接口地址**: `POST /task/delete`

**功能说明**: 软删除待办事项（同时删除标签关联关系）

**请求参数**:
```json
{
  "id": 789                   // 待办事项ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除待办事项",
  "data": true
}
```

### 3. 获取有效状态列表

**接口地址**: `GET /task/statuses`

**功能说明**: 获取系统支持的所有状态

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取状态列表成功",
  "data": ["未开始", "进行中", "已完成", "已取消", "已关闭", "搁置中"]
}
```

### 4. 获取有效优先级列表

**接口地址**: `GET /task/priorities`

**功能说明**: 获取系统支持的所有优先级

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取优先级列表成功",
  "data": ["紧急", "高", "中等", "普通", "低"]
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或登录已过期 |
| -1 | 参数错误 |
| -2 | 业务逻辑错误（如名称重复、数量超限等） |
| -3 | 数据格式错误 |
| -4 | 保存失败 |

## 注意事项

1. 所有接口都需要在请求头中携带 `Authorization: Bearer {token}`
2. 分类和标签名称在同一用户下不能重复
3. 分类最多50个，标签最多100个
4. 删除分类时，如果分类下有待办事项则不允许删除
5. 图片和标签ID需要以JSON数组字符串格式传递
6. 时间戳使用10位Unix时间戳格式
7. 状态变更会自动记录到状态跟踪表中
