<!--
 * @Description: 分类管理页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view class="category-manage-container">
    <!-- 分类列表 -->
    <view class="category-list">
      <view
        v-for="category in categories"
        :key="category.id"
        class="category-item"
      >
        <view class="category-info">
          <view class="category-icon" :style="{ color: category.color }">
            {{ category.icon }}
          </view>
          <view class="category-details">
            <text class="category-name">{{ category.name }}</text>
            <text class="category-count">{{ getCategoryTodoCount(category.id) }}个待办</text>
          </view>
        </view>

        <view class="category-actions">
          <view class="action-btn edit-btn" @click="editCategory(category)">
            <text>编辑</text>
          </view>
          <view class="action-btn delete-btn" @click="deleteCategoryItem(category)">
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="categories.length === 0">
      <view class="empty-icon">📁</view>
      <view class="empty-text">暂无分类</view>
      <view class="empty-desc">点击右下角按钮创建第一个分类</view>
    </view>

    <!-- 添加按钮 -->
    <view class="fab" @click="addCategory">
      <text>+</text>
    </view>

    <!-- 分类编辑弹窗 -->
    <u-popup :show="showEditDialog" @close="closeEditDialog" mode="bottom" :round="20">
      <view class="bottom-dialog">
        <!-- 拖拽指示器 -->
        <view class="drag-indicator"></view>

        <!-- 标题栏 -->
        <view class="dialog-header">
          <text class="dialog-title">{{ isEditMode ? '编辑分类' : '新建分类' }}</text>
          <view class="close-btn" @click="closeEditDialog">
            <text>×</text>
          </view>
        </view>

        <view class="dialog-content">
          <!-- 分类名称 -->
          <view class="form-section">
            <text class="section-title">分类名称</text>
            <view class="input-wrapper">
              <input
                class="name-input"
                type="text"
                placeholder="请输入分类名称"
                v-model="editForm.name"
                maxlength="20"
              />
            </view>
          </view>

          <!-- 图标选择 -->
          <view class="form-section">
            <text class="section-title">选择图标</text>
            <view class="icon-palette">
              <view
                v-for="icon in iconOptions"
                :key="icon"
                class="icon-item"
                :class="{ selected: editForm.icon === icon }"
                @click="selectIcon(icon)"
              >
                <text class="icon-text">{{ icon }}</text>
              </view>
            </view>
          </view>

          <!-- 颜色选择 -->
          <view class="form-section">
            <text class="section-title">选择颜色</text>
            <view class="color-palette">
              <view
                v-for="color in colorOptions"
                :key="color"
                class="color-item"
                :class="{ selected: editForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="selectColor(color)"
              >
                <view class="color-check" v-if="editForm.color === color">
                  <text>✓</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="dialog-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="closeEditDialog">取消</button>
            <button class="confirm-btn" @click="saveCategoryItem" :disabled="!canSave">
              {{ isEditMode ? '更新' : '创建' }}
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>

export default {
  name: 'CategoryManage',
  data() {
    return {
      categories: [], // 确保初始化为空数组
      showEditDialog: false,
      isEditMode: false,
      editingCategoryId: '',
      editForm: {
        name: '',
        icon: '📁',
        color: '#1890ff'
      },
      iconOptions: [
        '📁', '💼', '📚', '💡', '🎯', '🌟', '🔥', '💎',
        '🚀', '⚡', '🎨', '🏠', '💰', '🎵', '🍎', '⚽',
        '🌈', '🔧', '📱', '💻', '🎮', '📷', '✈️', '🚗'
      ],
      colorOptions: [
        '#1890ff', '#52c41a', '#faad14', '#ff4d4f',
        '#722ed1', '#13c2c2', '#eb2f96', '#f5222d',
        '#fa541c', '#fa8c16', '#a0d911', '#2f54eb',
        '#ff85c0', '#ffc53d', '#95de64', '#5cdbd3'
      ]
    }
  },

  computed: {
    canSave() {
      return this.editForm.name.trim().length > 0
    }
  },

  onShow() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      try {
        uni.showLoading({ title: '加载中...' })
        const _res = await this.$ajax.get('/task/category/list')
        if (_res?.code != 200) {
          uni.hideLoading()
          uni.showToast({
            title: _res?.msg || '加载失败',
            icon: 'none'
          })
          return
        }

        // 确保返回的数据是数组格式，防止find方法报错
        this.categories = Array.isArray(_res?.data) ? _res?.data : []
        console.log('加载分类数据:', this.categories)
        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('加载分类列表失败:', error)
        // 出错时也要确保categories是数组
        this.categories = []
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },

    // 获取分类下的待办数量
    getCategoryTodoCount(categoryId) {
      // 确保categories是数组，防止find方法报错
      if (!Array.isArray(this.categories)) {
        console.warn('categories不是数组格式:', this.categories)
        return 0
      }

      // 从API返回的数据中获取待办数量
      const category = this.categories.find(c => c.id === categoryId)
      console.log("🚀 -> getCategoryTodoCount -> category:", category)
      return category ? (category.taskCount || 0) : 0
    },

    // 添加分类
    addCategory() {
      console.log('点击添加分类按钮')
      this.isEditMode = false
      this.editingCategoryId = ''
      this.editForm = {
        name: '',
        icon: '📁',
        color: '#1890ff'
      }
      this.showEditDialog = true
      console.log('弹窗状态:', this.showEditDialog)
    },

    // 编辑分类
    editCategory(category) {
      this.isEditMode = true
      this.editingCategoryId = category.id
      this.editForm = {
        name: category.name,
        icon: category.icon,
        color: category.color
      }
      this.showEditDialog = true
    },

    // 删除分类
    deleteCategoryItem(category) {
      const todoCount = this.getCategoryTodoCount(category.id)
      let message = `确定要删除分类"${category.name}"吗？`

      if (todoCount > 0) {
        message += `\n该分类下有${todoCount}个待办事项，删除后这些待办将变为无分类状态。`
      }

      this.$confirm1(message, '删除确认', {
        confirmText: '删除',
        cancelText: '取消'
      }).then(async () => {
        try {
          uni.showLoading({ title: '删除中...' })
          await this.$ajax.post('/task/category/delete', { id: category.id })
          uni.hideLoading()

          await this.loadData()
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          uni.hideLoading()
          console.error('删除分类失败:', error)
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 选择图标
    selectIcon(icon) {
      this.editForm.icon = icon
    },

    // 选择颜色
    selectColor(color) {
      this.editForm.color = color
    },

    // 保存分类
    async saveCategoryItem() {
      if (!this.canSave) {
        uni.showToast({
          title: '请填写分类名称',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: this.isEditMode ? '更新中...' : '创建中...' })

        const categoryData = {
          id: this.isEditMode ? this.editingCategoryId : 0,
          name: this.editForm.name.trim(),
          icon: this.editForm.icon,
          color: this.editForm.color,
          weight: 0 // 默认权重
        }

        const _res = await this.$ajax.post('/task/category/save', categoryData)
        if (_res?.code != 200) {
          uni.hideLoading()
          uni.showToast({
            title: _res?.msg || '保存失败',
            icon: 'none'
          })
          return
        }

        uni.hideLoading()

        this.closeEditDialog()
        await this.loadData()

        uni.showToast({
          title: this.isEditMode ? '更新成功' : '创建成功',
          icon: 'success'
        })

      } catch (error) {
        uni.hideLoading()
        console.error('保存分类失败:', error)
      }
    },

    // 关闭编辑弹窗
    closeEditDialog() {
      this.showEditDialog = false
      this.isEditMode = false
      this.editingCategoryId = ''
      this.editForm = {
        name: '',
        icon: '📁',
        color: '#1890ff'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.category-manage-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: $spacing-lg;
}

.category-list {
  .category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: $card-background;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-sm;
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    transition: all $transition-normal;

    &:active {
      transform: translateY(1rpx) scale(0.995);
      box-shadow: $shadow-md;
    }

    .category-info {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      flex: 1;

      .category-icon {
        font-size: 48rpx;
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: $border-radius-md;
      }

      .category-details {
        flex: 1;

        .category-name {
          display: block;
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4rpx;
        }

        .category-count {
          font-size: $font-size-sm;
          color: $text-light;
        }
      }
    }

    .category-actions {
      display: flex;
      gap: $spacing-sm;

      .action-btn {
        padding: $spacing-xs $spacing-md;
        border-radius: $border-radius-sm;
        font-size: $font-size-sm;
        font-weight: 500;
        transition: all $transition-fast;
        cursor: pointer;

        &.edit-btn {
          background-color: $primary-light;
          color: $primary-color;

          &:active {
            background-color: darken($primary-light, 5%);
            transform: scale(0.95);
          }
        }

        &.delete-btn {
          background-color: rgba($error-color, 0.1);
          color: $error-color;

          &:active {
            background-color: rgba($error-color, 0.15);
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  margin-top: 30vh;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-lg;
    opacity: 0.3;
  }

  .empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    font-weight: 500;
  }

  .empty-desc {
    font-size: $font-size-md;
    color: $text-light;
    text-align: center;
  }
}

.fab {
  position: fixed;
  right: $spacing-lg;
  bottom: $spacing-xl;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  font-weight: 300;
  box-shadow: $shadow-lg;
  z-index: 999;
  transition: all $transition-normal;

  &:active {
    transform: scale(0.95);
    box-shadow: $shadow-md;
  }
}

// 底部弹窗样式
.bottom-dialog {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .close-btn {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: $text-secondary;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .dialog-content {
    padding: 0 40rpx 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-section {
      margin-bottom: 48rpx;

      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 24rpx;
      }

      // 输入框样式
      .input-wrapper {
        .name-input {
          width: 100%;
          height: 88rpx;
          padding: 0 24rpx;
          background-color: rgba(0, 0, 0, 0.03);
          border-radius: 16rpx;
          border: 2rpx solid transparent;
          font-size: 28rpx;
          color: $text-primary;
          transition: all $transition-fast;

          &:focus {
            border-color: $primary-color;
            background-color: $white;
            box-shadow: 0 0 0 6rpx rgba($primary-color, 0.08);
          }
        }
      }

      // 图标选择器
      .icon-palette {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 16rpx;

        .icon-item {
          width: 72rpx;
          height: 72rpx;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.03);
          transition: all $transition-fast;
          cursor: pointer;
          border: 3rpx solid transparent;

          .icon-text {
            font-size: 32rpx;
            line-height: 1;
          }

          &.selected {
            background-color: $primary-color;
            border-color: $primary-color;
            transform: scale(1.05);

            .icon-text {
              filter: grayscale(1) brightness(10);
            }
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      // 颜色选择器
      .color-palette {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 16rpx;

        .color-item {
          width: 72rpx;
          height: 72rpx;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all $transition-fast;
          cursor: pointer;
          border: 4rpx solid transparent;
          position: relative;

          .color-check {
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);

            text {
              font-size: 20rpx;
              color: $text-primary;
              font-weight: 600;
            }
          }

          &.selected {
            border-color: rgba(0, 0, 0, 0.15);
            transform: scale(1.1);
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  // 底部按钮区域
  .dialog-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: $white;

    .button-group {
      display: flex;
      gap: 24rpx;

      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        transition: all $transition-fast;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .cancel-btn {
        background-color: rgba(0, 0, 0, 0.05);
        color: $text-secondary;

        &:active {
          background-color: rgba(0, 0, 0, 0.08);
          transform: scale(0.98);
        }
      }

      .confirm-btn {
        background-color: $primary-color;
        color: $white;
        box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.3);

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }

        &:disabled {
          background-color: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          opacity: 0.6;
        }
      }
    }
  }
}
</style>
