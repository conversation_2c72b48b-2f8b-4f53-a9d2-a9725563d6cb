/**
 * @Description: 待办系统通用样式 - 简约大气设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 */

// 主题色彩变量 - 简约大气配色
$primary-color: #2979ff;
$primary-light: #e3f2fd;
$primary-dark: #1565c0;
$background-color: #ffffff;      // 白色背景
$card-background: #ffffff;
$text-primary: #1a1a1a;         // 更深的主文字色
$text-secondary: #666666;
$text-light: #999999;
$text-placeholder: #cccccc;
$border-color: #f0f0f0;         // 更淡的边框色
$divider-color: #f5f5f5;        // 分割线颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$purple-color: #722ed1;
$shadow-color: rgba(0, 0, 0, 0.04);  // 淡阴影
$white: #ffffff;

// 状态颜色
$status-pending: #999999;        // 未开始 - 灰色
$status-progress: #faad14;       // 进行中 - 橙色
$status-completed: #52c41a;      // 已完成 - 绿色
$status-cancelled: #ff4d4f;      // 已废弃 - 红色
$status-hold: #722ed1;           // 搁置中 - 紫色

// 间距变量 - 精确控制
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-xxl: 48rpx;

// 字体大小
$font-size-xs: 22rpx;
$font-size-sm: 26rpx;
$font-size-md: 30rpx;
$font-size-lg: 34rpx;
$font-size-xl: 38rpx;
$font-size-xxl: 42rpx;

// 圆角
$border-radius-sm: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 20rpx;

// 阴影
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
$shadow-nav: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);

// 过渡动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 通用容器样式
.container {
  background-color: $background-color;
  min-height: 100vh;
  padding: 0;
}

.page-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: $spacing-md;
}

// 卡片样式
.card {
  background: $card-background;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all $transition-normal;
  position: relative;
  overflow: hidden;

  &:active {
    transform: translateY(1rpx) scale(0.995);
    box-shadow: $shadow-md;
  }

  &.card-hover {
    &:hover {
      box-shadow: $shadow-md;
      transform: translateY(-2rpx);
    }
  }
}

// 按钮样式
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: 500;
  transition: all $transition-fast;
  border: none;
  cursor: pointer;
  
  &.btn-primary {
    background-color: $primary-color;
    color: $white;
    
    &:active {
      background-color: $primary-dark;
      transform: scale(0.98);
    }
  }
  
  &.btn-secondary {
    background-color: $border-color;
    color: $text-primary;
    
    &:active {
      background-color: darken($border-color, 5%);
      transform: scale(0.98);
    }
  }
  
  &.btn-ghost {
    background-color: transparent;
    color: $primary-color;
    border: 1rpx solid $primary-color;
    
    &:active {
      background-color: $primary-light;
      transform: scale(0.98);
    }
  }
  
  &.btn-small {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
  
  &.btn-large {
    padding: $spacing-lg $spacing-xl;
    font-size: $font-size-lg;
  }
  
  &.btn-round {
    border-radius: 50rpx;
  }
  
  &.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
}

// 浮动按钮
.fab {
  position: fixed;
  right: $spacing-lg;
  bottom: $spacing-xl;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  box-shadow: $shadow-lg;
  z-index: 999;
  transition: all $transition-normal;
  
  &:active {
    transform: scale(0.95);
    box-shadow: $shadow-md;
  }
}

// 标签样式
.tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  border: 1rpx solid transparent;
  
  &.tag-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
    border-color: rgba($primary-color, 0.2);
  }
  
  &.tag-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
    border-color: rgba($success-color, 0.2);
  }
  
  &.tag-warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
    border-color: rgba($warning-color, 0.2);
  }
  
  &.tag-error {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
    border-color: rgba($error-color, 0.2);
  }
  
  &.tag-purple {
    background-color: rgba($purple-color, 0.1);
    color: $purple-color;
    border-color: rgba($purple-color, 0.2);
  }
  
  &.tag-gray {
    background-color: rgba($text-light, 0.1);
    color: $text-light;
    border-color: rgba($text-light, 0.2);
  }
}

// 徽章样式
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2rpx $spacing-xs;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  min-width: 32rpx;
  height: 32rpx;
  
  &.badge-primary {
    background-color: $primary-color;
    color: $white;
  }
  
  &.badge-success {
    background-color: $success-color;
    color: $white;
  }
  
  &.badge-warning {
    background-color: $warning-color;
    color: $white;
  }
  
  &.badge-error {
    background-color: $error-color;
    color: $white;
  }
  
  &.badge-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    padding: 0;
    min-width: auto;
  }
}

// 分割线
.divider {
  height: 1rpx;
  background-color: $divider-color;
  margin: $spacing-md 0;
  
  &.divider-vertical {
    width: 1rpx;
    height: auto;
    margin: 0 $spacing-md;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  color: $text-light;
  
  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-lg;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: $font-size-md;
    margin-bottom: $spacing-sm;
  }
  
  .empty-desc {
    font-size: $font-size-sm;
    color: $text-placeholder;
  }
}

// 加载状态
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-light;
  
  .loading-text {
    margin-left: $spacing-sm;
    font-size: $font-size-sm;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
