/**
 * @Description: 待办系统常量定义
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 */

// 待办状态常量
export const TODO_STATUS = {
  NOT_STARTED: 'not_started',   // 未开始
  IN_PROGRESS: 'in_progress',   // 进行中
  PROCESSING: 'processing',     // 处理中
  COMPLETED: 'completed',       // 已完成
  CANCELLED: 'cancelled',       // 已取消
  CLOSED: 'closed',            // 已关闭
  ON_HOLD: 'on_hold'           // 搁置中
}

// 状态配置
export const STATUS_CONFIG = {
  [TODO_STATUS.NOT_STARTED]: {
    name: '未开始',
    color: '#999999',
    bgColor: '#f5f5f5',
    icon: '⏸️'
  },
  [TODO_STATUS.IN_PROGRESS]: {
    name: '进行中',
    color: '#1890ff',
    bgColor: '#e6f7ff',
    icon: '🔄'
  },
  [TODO_STATUS.PROCESSING]: {
    name: '处理中',
    color: '#13c2c2',
    bgColor: '#e6fffb',
    icon: '⚙️'
  },
  [TODO_STATUS.COMPLETED]: {
    name: '已完成',
    color: '#52c41a',
    bgColor: '#f6ffed',
    icon: '✅'
  },
  [TODO_STATUS.CANCELLED]: {
    name: '已取消',
    color: '#ff4d4f',
    bgColor: '#fff2f0',
    icon: '❌'
  },
  [TODO_STATUS.CLOSED]: {
    name: '已关闭',
    color: '#8c8c8c',
    bgColor: '#f0f0f0',
    icon: '🔒'
  },
  [TODO_STATUS.ON_HOLD]: {
    name: '搁置中',
    color: '#722ed1',
    bgColor: '#f9f0ff',
    icon: '⏳'
  }
}

// 优先级常量
export const PRIORITY_LEVELS = {
  URGENT: 5,   // 紧急
  HIGH: 4,     // 高
  MEDIUM: 3,   // 中等
  NORMAL: 2,   // 普通
  LOW: 1       // 低
}

// 优先级配置
export const PRIORITY_CONFIG = {
  [PRIORITY_LEVELS.URGENT]: {
    name: '紧急',
    color: '#ff4d4f',
    icon: '🔴'
  },
  [PRIORITY_LEVELS.HIGH]: {
    name: '高',
    color: '#ff7a45',
    icon: '🟠'
  },
  [PRIORITY_LEVELS.MEDIUM]: {
    name: '中等',
    color: '#faad14',
    icon: '🟡'
  },
  [PRIORITY_LEVELS.NORMAL]: {
    name: '普通',
    color: '#1890ff',
    icon: '🔵'
  },
  [PRIORITY_LEVELS.LOW]: {
    name: '低',
    color: '#52c41a',
    icon: '🟢'
  }
}

// 默认分类
export const DEFAULT_CATEGORIES = [
  {
    id: 'work',
    name: '工作',
    color: '#1890ff',
    icon: '💼'
  },
  {
    id: 'personal',
    name: '个人',
    color: '#52c41a',
    icon: '👤'
  },
  {
    id: 'study',
    name: '学习',
    color: '#722ed1',
    icon: '📚'
  },
  {
    id: 'life',
    name: '生活',
    color: '#faad14',
    icon: '🏠'
  },
  {
    id: 'health',
    name: '健康',
    color: '#ff85c0',
    icon: '💪'
  },
  {
    id: 'finance',
    name: '财务',
    color: '#13c2c2',
    icon: '💰'
  }
]

// 默认标签
export const DEFAULT_TAGS = [
  {
    id: 'important',
    name: '重要',
    color: '#ff4d4f'
  },
  {
    id: 'urgent',
    name: '紧急',
    color: '#faad14'
  },
  {
    id: 'easy',
    name: '简单',
    color: '#52c41a'
  },
  {
    id: 'difficult',
    name: '困难',
    color: '#722ed1'
  },
  {
    id: 'meeting',
    name: '会议',
    color: '#1890ff'
  },
  {
    id: 'deadline',
    name: '截止日期',
    color: '#ff7a45'
  },
  {
    id: 'review',
    name: '复习',
    color: '#13c2c2'
  },
  {
    id: 'planning',
    name: '规划',
    color: '#eb2f96'
  }
]

// 存储键名
export const STORAGE_KEYS = {
  TODOS: 'task_todos',
  CATEGORIES: 'task_categories',
  TAGS: 'task_tags',
  SETTINGS: 'task_settings'
}

// 日期格式
export const DATE_FORMAT = 'YYYY-MM-DD'
export const DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'

// 页面路径
export const PAGE_PATHS = {
  INDEX: '/pages_task/index/index',
  DETAIL: '/pages_task/detail/index',
  CATEGORY_MANAGE: '/pages_task/category-manage/index',
  TAG_MANAGE: '/pages_task/tag-manage/index'
}
