/**
 * @Description: 待办系统工具函数
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 */

import { DATE_FORMAT, DATETIME_FORMAT } from './constants.js'

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 获取当前时间戳
 * @returns {number} 时间戳
 */
export function getCurrentTimestamp() {
  return new Date().getTime()
}

/**
 * 格式化日期
 * @param {number|Date|string} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = DATE_FORMAT) {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return ''
  }
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化相对时间
 * @param {number} timestamp 时间戳
 * @returns {string} 相对时间描述
 */
export function formatRelativeTime(timestamp) {
  const now = Date.now()
  const diff = now - timestamp
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else {
    return formatDate(timestamp, 'MM-DD')
  }
}

/**
 * 获取日期范围内的所有日期
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @returns {Array} 日期数组
 */
export function getDateRange(startDate, endDate) {
  const dates = []
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  while (start <= end) {
    dates.push(formatDate(start))
    start.setDate(start.getDate() + 1)
  }
  
  return dates
}

/**
 * 获取本周日期范围
 * @param {Date} date 基准日期
 * @returns {Object} 包含开始和结束日期的对象
 */
export function getWeekRange(date = new Date()) {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
  
  const monday = new Date(d.setDate(diff))
  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6)
  
  return {
    start: formatDate(monday),
    end: formatDate(sunday)
  }
}

/**
 * 获取本月日期范围
 * @param {Date} date 基准日期
 * @returns {Object} 包含开始和结束日期的对象
 */
export function getMonthRange(date = new Date()) {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth()
  
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  return {
    start: formatDate(firstDay),
    end: formatDate(lastDay)
  }
}

/**
 * 判断日期是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const today = formatDate(new Date())
  const targetDate = formatDate(date)
  return today === targetDate
}

/**
 * 判断日期是否已过期
 * @param {string|Date} date 日期
 * @returns {boolean} 是否已过期
 */
export function isOverdue(date) {
  const today = new Date()
  const targetDate = new Date(date)
  today.setHours(0, 0, 0, 0)
  targetDate.setHours(0, 0, 0, 0)
  return targetDate < today
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date} date1 日期1
 * @param {string|Date} date2 日期2
 * @returns {number} 天数差
 */
export function getDaysDiff(date1, date2) {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  const timeDiff = Math.abs(d2.getTime() - d1.getTime())
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 验证日期格式
 * @param {string} dateString 日期字符串
 * @returns {boolean} 是否为有效日期
 */
export function isValidDate(dateString) {
  const date = new Date(dateString)
  return !isNaN(date.getTime()) && dateString.match(/^\d{4}-\d{2}-\d{2}$/)
}

/**
 * 获取颜色的透明度版本
 * @param {string} color 颜色值
 * @param {number} opacity 透明度 0-1
 * @returns {string} 带透明度的颜色
 */
export function getColorWithOpacity(color, opacity) {
  // 简单处理十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.slice(1)
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0')
    return `${color}${alpha}`
  }
  return color
}
