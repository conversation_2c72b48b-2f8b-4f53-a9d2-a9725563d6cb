/**
 * @Description: 测试数据生成器
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 */

import todoStorage from './storage.js'
import { TODO_STATUS, PRIORITY_LEVELS } from './constants.js'
import { formatDate, getCurrentTimestamp } from './utils.js'

/**
 * 生成测试待办数据
 */
export function generateTestTodos() {
  const categories = todoStorage.getCategories()
  const tags = todoStorage.getTags()

  if (categories.length === 0 || tags.length === 0) {
    console.warn('请先确保有分类和标签数据')
    return
  }

  const testTodos = [
    {
      title: '完成项目需求分析文档',
      description: '整理客户需求，编写详细的需求分析文档，包括功能模块划分、技术架构设计和开发时间评估。需要与产品经理和技术团队进行多轮讨论确认。',
      status: TODO_STATUS.IN_PROGRESS,
      categoryId: categories.find(c => c.name === '工作')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '重要')?.id,
        tags.find(t => t.name === '截止日期')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), // 2天前开始
      endDate: formatDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3天后截止
      priority: PRIORITY_LEVELS.HIGH
    },
    {
      title: '学习Vue3新特性',
      description: '深入学习Vue3的Composition API、Teleport、Fragments等新特性，完成相关练习项目。',
      status: TODO_STATUS.PENDING,
      categoryId: categories.find(c => c.name === '学习')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '简单')?.id,
        tags.find(t => t.name === '规划')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date()),
      endDate: formatDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7天后
      priority: PRIORITY_LEVELS.NORMAL
    },
    {
      title: '周末家庭聚餐准备',
      description: '准备周末的家庭聚餐，包括菜单规划、食材采购、餐具准备等。邀请爷爷奶奶一起来吃饭。',
      status: TODO_STATUS.PENDING,
      categoryId: categories.find(c => c.name === '生活')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '规划')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // 明天
      endDate: formatDate(new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)), // 后天
      priority: PRIORITY_LEVELS.MEDIUM
    },
    {
      title: '健身房训练计划',
      description: '制定新的健身训练计划，包括力量训练、有氧运动和拉伸放松。目标是每周至少3次训练。',
      status: TODO_STATUS.COMPLETED,
      categoryId: categories.find(c => c.name === '健康')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '重要')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)), // 5天前
      endDate: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)), // 昨天完成
      priority: PRIORITY_LEVELS.MEDIUM
    },
    {
      title: '团队会议准备',
      description: '准备下周的团队周会，整理本周工作总结，准备下周工作计划汇报材料。',
      status: TODO_STATUS.PENDING,
      categoryId: categories.find(c => c.name === '工作')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '会议')?.id,
        tags.find(t => t.name === '紧急')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // 明天
      endDate: formatDate(new Date(Date.now() + 4 * 24 * 60 * 60 * 1000)), // 4天后
      priority: PRIORITY_LEVELS.HIGH
    },
    {
      title: '投资理财学习',
      description: '学习基金投资知识，了解不同类型基金的特点和风险，制定个人投资策略。',
      status: TODO_STATUS.ON_HOLD,
      categoryId: categories.find(c => c.name === '财务')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '困难')?.id,
        tags.find(t => t.name === '规划')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)), // 3天前
      endDate: formatDate(new Date(Date.now() + 10 * 24 * 60 * 60 * 1000)), // 10天后
      priority: PRIORITY_LEVELS.LOW
    },
    {
      title: '读书笔记整理',
      description: '整理最近阅读的《深入理解计算机系统》读书笔记，总结重点知识点，制作思维导图。',
      status: TODO_STATUS.IN_PROGRESS,
      categoryId: categories.find(c => c.name === '学习')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '复习')?.id,
        tags.find(t => t.name === '重要')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)), // 昨天
      endDate: formatDate(new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)), // 5天后
      priority: PRIORITY_LEVELS.NORMAL
    },
    {
      title: '年度体检预约',
      description: '联系医院预约年度体检，包括常规检查、血液检查、心电图等项目。提前了解注意事项。',
      status: TODO_STATUS.CANCELLED,
      categoryId: categories.find(c => c.name === '健康')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '重要')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)), // 7天前
      endDate: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), // 2天前
      priority: PRIORITY_LEVELS.MEDIUM
    },
    {
      title: '个人网站优化',
      description: '优化个人博客网站的性能和SEO，更新文章内容，添加新的技术分享文章。',
      status: TODO_STATUS.PENDING,
      categoryId: categories.find(c => c.name === '个人')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '简单')?.id,
        tags.find(t => t.name === '规划')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)), // 后天
      endDate: formatDate(new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)), // 14天后
      priority: PRIORITY_LEVELS.LOW
    },
    {
      title: '月度预算规划',
      description: '制定下个月的收支预算，分析上个月的消费情况，调整不合理的支出项目。',
      status: TODO_STATUS.PENDING,
      categoryId: categories.find(c => c.name === '财务')?.id || categories[0].id,
      tags: [
        tags.find(t => t.name === '重要')?.id,
        tags.find(t => t.name === '截止日期')?.id
      ].filter(Boolean),
      startDate: formatDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)), // 3天后
      endDate: formatDate(new Date(Date.now() + 8 * 24 * 60 * 60 * 1000)), // 8天后
      priority: PRIORITY_LEVELS.URGENT
    }
  ]

  // 添加测试数据
  let successCount = 0
  testTodos.forEach(todoData => {
    try {
      todoStorage.addTodo(todoData)
      successCount++
    } catch (error) {
      console.error('添加测试待办失败:', error)
    }
  })

  console.log(`✅ 成功生成 ${successCount} 条测试待办数据`)

  // 显示成功提示
  uni.showToast({
    title: `生成${successCount}条测试数据`,
    icon: 'success',
    duration: 2000
  })

  return successCount
}

/**
 * 清空所有待办数据
 */
export function clearAllTodos() {
  try {
    const todos = todoStorage.getTodos()
    todos.forEach(todo => {
      todoStorage.deleteTodo(todo.id)
    })
    console.log('已清空所有待办数据')
  } catch (error) {
    console.error('清空待办数据失败:', error)
  }
}

/**
 * 重置所有数据（包括分类和标签）
 */
export function resetAllData() {
  try {
    todoStorage.clearAllData()
    console.log('已重置所有数据')
  } catch (error) {
    console.error('重置数据失败:', error)
  }
}

/**
 * 生成完整的测试数据集
 */
export function generateFullTestData() {
  try {
    // 先重置数据
    resetAllData()

    // 等待一下确保数据已清空
    setTimeout(() => {
      // 生成测试待办数据
      generateTestTodos()

      console.log('完整测试数据生成完成！')
      console.log('数据统计:', todoStorage.getTodoStats())
    }, 100)
  } catch (error) {
    console.error('生成测试数据失败:', error)
  }
}

/**
 * 获取数据统计信息
 */
export function getDataStats() {
  const stats = {
    todos: todoStorage.getTodos().length,
    categories: todoStorage.getCategories().length,
    tags: todoStorage.getTags().length,
    todoStats: todoStorage.getTodoStats(),
    categoryStats: todoStorage.getCategoryStats(),
    tagStats: todoStorage.getTagStats()
  }

  console.log('数据统计:', stats)
  return stats
}

// 导出所有函数
export default {
  generateTestTodos,
  clearAllTodos,
  resetAllData,
  generateFullTestData,
  getDataStats
}
