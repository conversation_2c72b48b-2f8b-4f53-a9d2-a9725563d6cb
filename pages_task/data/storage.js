/**
 * @Description: 待办系统数据存储管理
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 */

import {
  STORAGE_KEYS,
  DEFAULT_CATEGORIES,
  DEFAULT_TAGS,
  TODO_STATUS,
  PRIORITY_LEVELS
} from './constants.js'
import { generateId, getCurrentTimestamp, formatDate, deepClone } from './utils.js'

/**
 * 待办数据存储管理类
 */
class TodoStorage {
  constructor() {
    this.initDefaultData()
  }

  // ==================== 初始化 ====================

  /**
   * 初始化默认数据
   */
  initDefaultData() {
    // 初始化默认分类
    if (!this.getCategories().length) {
      DEFAULT_CATEGORIES.forEach(category => {
        this.addCategory({
          ...category,
          createTime: getCurrentTimestamp()
        })
      })
    }

    // 初始化默认标签
    if (!this.getTags().length) {
      DEFAULT_TAGS.forEach(tag => {
        this.addTag({
          ...tag,
          createTime: getCurrentTimestamp()
        })
      })
    }
  }

  // ==================== 待办事项管理 ====================

  /**
   * 获取所有待办事项
   * @returns {Array} 待办事项列表
   */
  getTodos() {
    try {
      return uni.getStorageSync(STORAGE_KEYS.TODOS) || []
    } catch (error) {
      console.error('获取待办事项失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取待办事项
   * @param {string} id 待办事项ID
   * @returns {Object|null} 待办事项
   */
  getTodoById(id) {
    const todos = this.getTodos()
    return todos.find(todo => todo.id === id) || null
  }

  /**
   * 添加待办事项
   * @param {Object} todoData 待办事项数据
   * @returns {Object} 新增的待办事项
   */
  addTodo(todoData) {
    try {
      const todos = this.getTodos()
      const newTodo = {
        id: generateId(),
        title: todoData.title || '',
        description: todoData.description || '',
        status: todoData.status || TODO_STATUS.PENDING,
        categoryId: todoData.categoryId || '',
        tags: todoData.tags || [],
        startDate: todoData.startDate || formatDate(new Date()),
        endDate: todoData.endDate || '',
        priority: todoData.priority || PRIORITY_LEVELS.NORMAL,
        createTime: getCurrentTimestamp(),
        updateTime: getCurrentTimestamp()
      }

      todos.unshift(newTodo)
      uni.setStorageSync(STORAGE_KEYS.TODOS, todos)
      return newTodo
    } catch (error) {
      console.error('添加待办事项失败:', error)
      throw error
    }
  }

  /**
   * 更新待办事项
   * @param {string} id 待办事项ID
   * @param {Object} updateData 更新数据
   * @returns {Object|null} 更新后的待办事项
   */
  updateTodo(id, updateData) {
    try {
      const todos = this.getTodos()
      const index = todos.findIndex(todo => todo.id === id)

      if (index === -1) {
        throw new Error('待办事项不存在')
      }

      const updatedTodo = {
        ...todos[index],
        ...updateData,
        updateTime: getCurrentTimestamp()
      }

      todos[index] = updatedTodo
      uni.setStorageSync(STORAGE_KEYS.TODOS, todos)
      return updatedTodo
    } catch (error) {
      console.error('更新待办事项失败:', error)
      throw error
    }
  }

  /**
   * 删除待办事项
   * @param {string} id 待办事项ID
   * @returns {boolean} 是否删除成功
   */
  deleteTodo(id) {
    try {
      const todos = this.getTodos()
      const filteredTodos = todos.filter(todo => todo.id !== id)

      if (filteredTodos.length === todos.length) {
        throw new Error('待办事项不存在')
      }

      uni.setStorageSync(STORAGE_KEYS.TODOS, filteredTodos)
      return true
    } catch (error) {
      console.error('删除待办事项失败:', error)
      throw error
    }
  }

  /**
   * 批量删除待办事项
   * @param {Array} ids 待办事项ID数组
   * @returns {boolean} 是否删除成功
   */
  deleteTodos(ids) {
    try {
      const todos = this.getTodos()
      const filteredTodos = todos.filter(todo => !ids.includes(todo.id))
      uni.setStorageSync(STORAGE_KEYS.TODOS, filteredTodos)
      return true
    } catch (error) {
      console.error('批量删除待办事项失败:', error)
      throw error
    }
  }

  /**
   * 根据条件筛选待办事项
   * @param {Object} filters 筛选条件
   * @returns {Array} 筛选后的待办事项列表
   */
  filterTodos(filters = {}) {
    let todos = this.getTodos()

    // 按状态筛选
    if (filters.status) {
      todos = todos.filter(todo => todo.status === filters.status)
    }

    // 按分类筛选
    if (filters.categoryId) {
      todos = todos.filter(todo => todo.categoryId === filters.categoryId)
    }

    // 按标签筛选
    if (filters.tagIds && filters.tagIds.length > 0) {
      todos = todos.filter(todo =>
        filters.tagIds.some(tagId => todo.tags.includes(tagId))
      )
    }

    // 按日期范围筛选
    if (filters.startDate && filters.endDate) {
      todos = todos.filter(todo => {
        const todoDate = todo.startDate || todo.endDate
        return todoDate >= filters.startDate && todoDate <= filters.endDate
      })
    }

    // 按优先级筛选
    if (filters.priority) {
      todos = todos.filter(todo => todo.priority === filters.priority)
    }

    // 按关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      todos = todos.filter(todo =>
        todo.title.toLowerCase().includes(keyword) ||
        todo.description.toLowerCase().includes(keyword)
      )
    }

    return todos
  }

  /**
   * 获取最近的待办事项
   * @param {number} limit 限制数量
   * @returns {Array} 最近的待办事项列表
   */
  getRecentTodos(limit = 10) {
    const todos = this.getTodos()
    return todos
      .sort((a, b) => b.updateTime - a.updateTime)
      .slice(0, limit)
  }

  // ==================== 分类管理 ====================

  /**
   * 获取所有分类
   * @returns {Array} 分类列表
   */
  getCategories() {
    try {
      return uni.getStorageSync(STORAGE_KEYS.CATEGORIES) || []
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取分类
   * @param {string} id 分类ID
   * @returns {Object|null} 分类
   */
  getCategoryById(id) {
    const categories = this.getCategories()
    return categories.find(category => category.id === id) || null
  }

  /**
   * 添加分类
   * @param {Object} categoryData 分类数据
   * @returns {Object} 新增的分类
   */
  addCategory(categoryData) {
    try {
      const categories = this.getCategories()
      const newCategory = {
        id: categoryData.id || generateId(),
        name: categoryData.name,
        color: categoryData.color || '#1890ff',
        icon: categoryData.icon || '📁',
        createTime: categoryData.createTime || getCurrentTimestamp()
      }

      categories.push(newCategory)
      uni.setStorageSync(STORAGE_KEYS.CATEGORIES, categories)
      return newCategory
    } catch (error) {
      console.error('添加分类失败:', error)
      throw error
    }
  }

  /**
   * 更新分类
   * @param {string} id 分类ID
   * @param {Object} updateData 更新数据
   * @returns {Object|null} 更新后的分类
   */
  updateCategory(id, updateData) {
    try {
      const categories = this.getCategories()
      const index = categories.findIndex(category => category.id === id)

      if (index === -1) {
        throw new Error('分类不存在')
      }

      categories[index] = { ...categories[index], ...updateData }
      uni.setStorageSync(STORAGE_KEYS.CATEGORIES, categories)
      return categories[index]
    } catch (error) {
      console.error('更新分类失败:', error)
      throw error
    }
  }

  /**
   * 删除分类
   * @param {string} id 分类ID
   * @returns {boolean} 是否删除成功
   */
  deleteCategory(id) {
    try {
      const categories = this.getCategories()
      const filteredCategories = categories.filter(category => category.id !== id)

      if (filteredCategories.length === categories.length) {
        throw new Error('分类不存在')
      }

      // 同时清理使用该分类的待办事项
      const todos = this.getTodos()
      const updatedTodos = todos.map(todo => {
        if (todo.categoryId === id) {
          return { ...todo, categoryId: '', updateTime: getCurrentTimestamp() }
        }
        return todo
      })

      uni.setStorageSync(STORAGE_KEYS.CATEGORIES, filteredCategories)
      uni.setStorageSync(STORAGE_KEYS.TODOS, updatedTodos)
      return true
    } catch (error) {
      console.error('删除分类失败:', error)
      throw error
    }
  }

  // ==================== 标签管理 ====================

  /**
   * 获取所有标签
   * @returns {Array} 标签列表
   */
  getTags() {
    try {
      return uni.getStorageSync(STORAGE_KEYS.TAGS) || []
    } catch (error) {
      console.error('获取标签失败:', error)
      return []
    }
  }

  /**
   * 根据ID获取标签
   * @param {string} id 标签ID
   * @returns {Object|null} 标签
   */
  getTagById(id) {
    const tags = this.getTags()
    return tags.find(tag => tag.id === id) || null
  }

  /**
   * 根据ID数组获取标签列表
   * @param {Array} ids 标签ID数组
   * @returns {Array} 标签列表
   */
  getTagsByIds(ids) {
    const tags = this.getTags()
    return tags.filter(tag => ids.includes(tag.id))
  }

  /**
   * 添加标签
   * @param {Object} tagData 标签数据
   * @returns {Object} 新增的标签
   */
  addTag(tagData) {
    try {
      const tags = this.getTags()
      const newTag = {
        id: tagData.id || generateId(),
        name: tagData.name,
        color: tagData.color || '#1890ff',
        createTime: tagData.createTime || getCurrentTimestamp()
      }

      tags.push(newTag)
      uni.setStorageSync(STORAGE_KEYS.TAGS, tags)
      return newTag
    } catch (error) {
      console.error('添加标签失败:', error)
      throw error
    }
  }

  /**
   * 更新标签
   * @param {string} id 标签ID
   * @param {Object} updateData 更新数据
   * @returns {Object|null} 更新后的标签
   */
  updateTag(id, updateData) {
    try {
      const tags = this.getTags()
      const index = tags.findIndex(tag => tag.id === id)

      if (index === -1) {
        throw new Error('标签不存在')
      }

      tags[index] = { ...tags[index], ...updateData }
      uni.setStorageSync(STORAGE_KEYS.TAGS, tags)
      return tags[index]
    } catch (error) {
      console.error('更新标签失败:', error)
      throw error
    }
  }

  /**
   * 删除标签
   * @param {string} id 标签ID
   * @returns {boolean} 是否删除成功
   */
  deleteTag(id) {
    try {
      const tags = this.getTags()
      const filteredTags = tags.filter(tag => tag.id !== id)

      if (filteredTags.length === tags.length) {
        throw new Error('标签不存在')
      }

      // 同时清理使用该标签的待办事项
      const todos = this.getTodos()
      const updatedTodos = todos.map(todo => {
        if (todo.tags.includes(id)) {
          return {
            ...todo,
            tags: todo.tags.filter(tagId => tagId !== id),
            updateTime: getCurrentTimestamp()
          }
        }
        return todo
      })

      uni.setStorageSync(STORAGE_KEYS.TAGS, filteredTags)
      uni.setStorageSync(STORAGE_KEYS.TODOS, updatedTodos)
      return true
    } catch (error) {
      console.error('删除标签失败:', error)
      throw error
    }
  }

  // ==================== 统计分析 ====================

  /**
   * 获取待办事项统计信息
   * @returns {Object} 统计信息
   */
  getTodoStats() {
    const todos = this.getTodos()
    const stats = {
      total: todos.length,
      pending: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0,
      onHold: 0,
      overdue: 0
    }

    const today = formatDate(new Date())

    todos.forEach(todo => {
      // 统计状态
      switch (todo.status) {
        case TODO_STATUS.PENDING:
          stats.pending++
          break
        case TODO_STATUS.IN_PROGRESS:
          stats.inProgress++
          break
        case TODO_STATUS.COMPLETED:
          stats.completed++
          break
        case TODO_STATUS.CANCELLED:
          stats.cancelled++
          break
        case TODO_STATUS.ON_HOLD:
          stats.onHold++
          break
      }

      // 统计过期
      if (todo.endDate && todo.endDate < today && todo.status !== TODO_STATUS.COMPLETED) {
        stats.overdue++
      }
    })

    return stats
  }

  /**
   * 获取分类统计信息
   * @returns {Array} 分类统计列表
   */
  getCategoryStats() {
    const todos = this.getTodos()
    const categories = this.getCategories()

    return categories.map(category => {
      const categoryTodos = todos.filter(todo => todo.categoryId === category.id)
      return {
        ...category,
        todoCount: categoryTodos.length,
        completedCount: categoryTodos.filter(todo => todo.status === TODO_STATUS.COMPLETED).length
      }
    })
  }

  /**
   * 获取标签统计信息
   * @returns {Array} 标签统计列表
   */
  getTagStats() {
    const todos = this.getTodos()
    const tags = this.getTags()

    return tags.map(tag => {
      const tagTodos = todos.filter(todo => todo.tags.includes(tag.id))
      return {
        ...tag,
        todoCount: tagTodos.length,
        completedCount: tagTodos.filter(todo => todo.status === TODO_STATUS.COMPLETED).length
      }
    })
  }

  // ==================== 数据管理 ====================

  /**
   * 清空所有数据
   * @returns {boolean} 是否清空成功
   */
  clearAllData() {
    try {
      uni.removeStorageSync(STORAGE_KEYS.TODOS)
      uni.removeStorageSync(STORAGE_KEYS.CATEGORIES)
      uni.removeStorageSync(STORAGE_KEYS.TAGS)
      uni.removeStorageSync(STORAGE_KEYS.SETTINGS)
      this.initDefaultData()
      return true
    } catch (error) {
      console.error('清空数据失败:', error)
      throw error
    }
  }

  /**
   * 导出数据
   * @returns {Object} 导出的数据
   */
  exportData() {
    return {
      todos: this.getTodos(),
      categories: this.getCategories(),
      tags: this.getTags(),
      exportTime: getCurrentTimestamp()
    }
  }

  /**
   * 导入数据
   * @param {Object} data 要导入的数据
   * @returns {boolean} 是否导入成功
   */
  importData(data) {
    try {
      if (data.todos) {
        uni.setStorageSync(STORAGE_KEYS.TODOS, data.todos)
      }
      if (data.categories) {
        uni.setStorageSync(STORAGE_KEYS.CATEGORIES, data.categories)
      }
      if (data.tags) {
        uni.setStorageSync(STORAGE_KEYS.TAGS, data.tags)
      }
      return true
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const todoStorage = new TodoStorage()

export default todoStorage
