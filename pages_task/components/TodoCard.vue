<!--
 * @Description: 待办事项卡片组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view class="todo-card" @click="handleClick">
    <!-- 左侧状态指示器 -->
    <view class="status-indicator" :style="{ backgroundColor: getStatusConfig(todo.status).color }"></view>

    <!-- 主要内容区域 -->
    <view class="card-content">
      <!-- 置顶标识 -->
      <view class="top-badge" v-if="todo.isCommond == 1">
        <text class="top-icon">📌</text>
      </view>

      <!-- 标题行 - 只显示内容 -->
      <view class="title-row">
        <text class="todo-title">{{ todo.content || '无标题' }}</text>
      </view>

      <!-- 第二行：状态 + 分类 + 优先级 -->
      <view class="meta-row">
        <!-- 状态徽章 -->
        <view class="status-badge" :style="{
          backgroundColor: getStatusConfig(todo.status).color + '20',
          color: getStatusConfig(todo.status).color
        }">
          <text class="status-icon">{{ getStatusConfig(todo.status).icon }}</text>
          <text class="status-name">{{ todo.status }}</text>
        </view>

        <!-- 分类徽章 -->
        <view class="category-badge" v-if="todo.categoryName" :style="{
          backgroundColor: todo.categoryColor + '20',
          color: todo.categoryColor,
          borderColor: todo.categoryColor + '40'
        }">
          <text class="category-icon">{{ todo.categoryIcon }}</text>
          <text class="category-name">{{ todo.categoryName }}</text>
        </view>

        <!-- 优先级徽章 -->
        <view class="priority-badge" v-if="todo.priority" :style="{
          backgroundColor: getPriorityConfig(todo.priority).color + '20',
          color: getPriorityConfig(todo.priority).color
        }">
          <text class="priority-icon">{{ getPriorityConfig(todo.priority).icon }}</text>
          <text class="priority-name">{{ todo.priority }}</text>
        </view>
      </view>

      <!-- 截止时间行 -->
      <view class="deadline-row" v-if="todo.deadline && todo.deadline > 0">
        <view class="deadline-left">
          <text class="deadline-icon">📅</text>
          <text class="deadline-text">{{ formatDeadline(todo.deadline) }}</text>
        </view>
        <!-- 倒计时组件 -->
        <CountdownTimer
          :deadline="todo.deadline"
          :status="todo.status"
        />
      </view>

      <!-- 标签列表 -->
      <view class="tags-row" v-if="todo.tags && todo.tags.length > 0">
        <view
          v-for="tag in todo.tags"
          :key="tag.id"
          class="tag-item"
          :style="{
            backgroundColor: tag.color + '20',
            color: tag.color,
            borderColor: tag.color + '40'
          }"
        >
          <text>{{ tag.name }}</text>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
import { STATUS_CONFIG, PRIORITY_CONFIG } from '../data/constants.js'
import CountdownTimer from './CountdownTimer.vue'

export default {
  name: 'TodoCard',
  components: {
    CountdownTimer
  },
  props: {
    todo: {
      type: Object,
      required: true
    }
  },

  computed: {
    isOverdue() {
      if (!this.todo.deadline || this.todo.deadline === 0) return false
      const now = Math.floor(Date.now() / 1000)
      return this.todo.deadline < now &&
        this.todo.status !== '已完成' &&
        this.todo.status !== '已取消'
    }
  },

  methods: {
    // 处理卡片点击
    handleClick() {
      this.$emit('click', this.todo)
    },



    // 格式化截止时间显示
    formatDeadline(timestamp) {
      if (!timestamp || timestamp === 0) return ''

      const date = new Date(timestamp * 1000)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

      const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '明天'
      } else if (diffDays === -1) {
        return '昨天'
      } else if (diffDays > 0) {
        return `${diffDays}天后`
      } else {
        return `${Math.abs(diffDays)}天前`
      }
    },

    // 获取状态配置（直接用save页面的配置）
    getStatusConfig(statusName) {
      const statusKey = Object.keys(STATUS_CONFIG).find(key =>
        STATUS_CONFIG[key].name === statusName
      )
      return STATUS_CONFIG[statusKey] || STATUS_CONFIG.not_started
    },

    // 获取优先级配置（直接用save页面的配置）
    getPriorityConfig(priorityName) {
      const priorityKey = Object.keys(PRIORITY_CONFIG).find(key =>
        PRIORITY_CONFIG[key].name === priorityName
      )
      return PRIORITY_CONFIG[priorityKey] || PRIORITY_CONFIG[2]
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.todo-card {
  background: $card-background;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all $transition-normal;
  position: relative;
  overflow: hidden;
  display: flex;

  &:active {
    transform: translateY(1rpx) scale(0.995);
    box-shadow: $shadow-md;
  }

  .status-indicator {
    width: 8rpx;
    flex-shrink: 0;
  }

  .card-content {
    flex: 1;
    padding: $spacing-lg;
    position: relative;

    .top-badge {
      position: absolute;
      top: 12rpx;
      right: 12rpx;
      z-index: 1;

      .top-icon {
        font-size: 24rpx;
        line-height: 1;
        color: #ff4d4f;
        filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
      }
    }

    // 标题行
    .title-row {
      margin-bottom: $spacing-md;

      .todo-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
        line-height: 1.4;
        // 最多2行
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        padding-right: 40rpx; // 为置顶标识留出空间
      }
    }

    // 第二行：状态 + 分类 + 优先级
    .meta-row {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-md;

      .status-badge, .category-badge, .priority-badge {
        display: flex;
        align-items: center;
        gap: 4rpx;
        padding: 4rpx $spacing-sm;
        border-radius: $border-radius-sm;
        font-size: $font-size-xs;
        font-weight: 500;
        border: 1rpx solid;

        .status-icon, .category-icon, .priority-icon {
          font-size: $font-size-sm;
          line-height: 1;
        }

        .status-name, .category-name, .priority-name {
          line-height: 1.2;
        }
      }
    }

    // 截止时间行
    .deadline-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-md;

      .deadline-left {
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        .deadline-icon {
          font-size: $font-size-sm;
        }

        .deadline-text {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }
    }

    .tags-row {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      margin-bottom: $spacing-md;

      .tag-item {
        padding: 2rpx $spacing-xs;
        border-radius: $border-radius-sm;
        font-size: $font-size-xs;
        font-weight: 500;
        border: 1rpx solid;
      }
    }


  }
}
</style>
