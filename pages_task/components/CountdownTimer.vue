<!--
 * @Description: 倒计时组件 - 显示任务截止时间倒计时
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-31
 * @Features:
 * - 支持天、小时、分钟、秒的倒计时显示
 * - 小于3小时显示红色警告
 * - 逾期显示"已逾期"
 * - 只在指定状态下显示
-->
<template>
  <view class="countdown-timer" v-if="shouldShowCountdown">
    <view
      class="countdown-content"
      :class="{
        'countdown-warning': isWarning,
        'countdown-overdue': isOverdue
      }"
    >
      <text class="countdown-icon">⏰</text>
      <text class="countdown-text">{{ countdownText }}</text>
    </view>
  </view>
</template>

<script>
import { TODO_STATUS } from '../data/constants.js'

export default {
  name: 'CountdownTimer',
  props: {
    // 截止时间戳（秒）
    deadline: {
      type: [Number, String],
      default: 0
    },
    // 任务状态
    status: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      currentTime: Math.floor(Date.now() / 1000),
      timer: null,
      // 需要显示倒计时的状态（使用中文状态名）
      showCountdownStatuses: [
        '未开始',
        '进行中',
        '处理中',
        '搁置中'
      ]
    }
  },

  computed: {
    // 是否应该显示倒计时
    shouldShowCountdown() {
      return this.deadline &&
             this.deadline > 0 &&
             this.showCountdownStatuses.includes(this.status)
    },

    // 剩余时间（秒）
    remainingTime() {
      return Math.max(0, this.deadline - this.currentTime)
    },

    // 是否已逾期
    isOverdue() {
      return this.deadline > 0 && this.currentTime > this.deadline
    },

    // 是否为警告状态（小于3小时）
    isWarning() {
      return !this.isOverdue && this.remainingTime > 0 && this.remainingTime < 3 * 60 * 60
    },

    // 倒计时文本
    countdownText() {
      if (this.isOverdue) {
        return '已逾期'
      }

      if (this.remainingTime === 0) {
        return '即将到期'
      }

      const days = Math.floor(this.remainingTime / (24 * 60 * 60))
      const hours = Math.floor((this.remainingTime % (24 * 60 * 60)) / (60 * 60))
      const minutes = Math.floor((this.remainingTime % (60 * 60)) / 60)
      const seconds = this.remainingTime % 60

      // 格式化显示，保持长度统一
      if (days > 0) {
        return `${days.toString().padStart(2, '0')}天${hours.toString().padStart(2, '0')}小时${minutes.toString().padStart(2, '0')}分${seconds.toString().padStart(2, '0')}秒`
      } else {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }
    }
  },

  mounted() {
    this.startTimer()
  },

  beforeDestroy() {
    this.stopTimer()
  },

  methods: {
    // 启动定时器
    startTimer() {
      if (this.shouldShowCountdown && !this.isOverdue) {
        this.timer = setInterval(() => {
          this.currentTime = Math.floor(Date.now() / 1000)
        }, 1000)
      }
    },

    // 停止定时器
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    }
  },

  watch: {
    // 监听截止时间变化
    deadline() {
      this.stopTimer()
      this.startTimer()
    },

    // 监听状态变化
    status() {
      this.stopTimer()
      this.startTimer()
    },

    // 监听是否逾期，逾期后停止定时器
    isOverdue(newVal) {
      if (newVal) {
        this.stopTimer()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.countdown-timer {
  display: inline-flex;
}

.countdown-content {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx $spacing-xs;
  border-radius: $border-radius-sm;
  background-color: rgba(24, 144, 255, 0.1);
  border: 1rpx solid rgba(24, 144, 255, 0.2);
  font-size: $font-size-xs;
  color: #1890ff;
  font-weight: 500;
  white-space: nowrap;

  .countdown-icon {
    font-size: $font-size-sm;
    line-height: 1;
  }

  .countdown-text {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5rpx;
    white-space: nowrap;
  }

  // 警告状态（小于3小时）
  &.countdown-warning {
    background-color: rgba(255, 77, 79, 0.1);
    border-color: rgba(255, 77, 79, 0.2);
    color: #ff4d4f;
  }

  // 逾期状态
  &.countdown-overdue {
    background-color: rgba(255, 77, 79, 0.15);
    border-color: rgba(255, 77, 79, 0.3);
    color: #ff4d4f;
    font-weight: 600;

    .countdown-text {
      font-family: inherit;
    }
  }
}

// 响应式调整
@media screen and (max-width: 750rpx) {
  .countdown-content {
    font-size: 22rpx;
    padding: 2rpx 6rpx;
    min-width: 100rpx;

    .countdown-icon {
      font-size: 24rpx;
    }
  }
}
</style>
