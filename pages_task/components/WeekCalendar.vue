<!--
 * @Description: 周类型日历组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view class="week-calendar">
    <!-- 月份标题和切换按钮 -->
    <view class="calendar-header">
      <view class="month-info">
        <text class="month-text">{{ currentMonth }}</text>
        <text class="year-text">{{ currentYear }}</text>
      </view>
      <view class="nav-buttons">
        <view class="nav-btn" @click="prevWeek">
          <text>‹</text>
        </view>
        <view class="nav-btn" @click="nextWeek">
          <text>›</text>
        </view>
      </view>
    </view>

    <!-- 星期标题 -->
    <view class="week-header">
      <view 
        v-for="day in weekDays" 
        :key="day"
        class="week-day-header"
      >
        <text>{{ day }}</text>
      </view>
    </view>

    <!-- 日期列表 -->
    <view class="week-dates">
      <view
        v-for="date in weekDates"
        :key="date.dateString"
        class="date-item"
        :class="{
          'is-today': date.isToday,
          'is-selected': date.dateString === selectedDate,
          'is-other-month': date.isOtherMonth,
          'has-todo': date.hasTodo
        }"
        @click="selectDate(date)"
      >
        <view class="date-number">{{ date.day }}</view>
        <view class="date-dot" v-if="date.hasTodo"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { formatDate, getWeekRange } from '../data/utils.js'
import todoStorage from '../data/storage.js'

export default {
  name: 'WeekCalendar',
  props: {
    selectedDate: {
      type: String,
      default: () => formatDate(new Date())
    }
  },

  data() {
    return {
      currentDate: new Date(),
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],
      weekDates: [],
      todos: []
    }
  },

  computed: {
    currentYear() {
      return this.currentDate.getFullYear()
    },

    currentMonth() {
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', 
                     '7月', '8月', '9月', '10月', '11月', '12月']
      return months[this.currentDate.getMonth()]
    }
  },

  watch: {
    selectedDate: {
      handler(newDate) {
        if (newDate) {
          this.currentDate = new Date(newDate)
          this.generateWeekDates()
        }
      },
      immediate: true
    }
  },

  mounted() {
    this.loadTodos()
    this.generateWeekDates()
  },

  methods: {
    // 加载待办事项
    loadTodos() {
      this.todos = todoStorage.getTodos()
    },

    // 生成一周的日期
    generateWeekDates() {
      const dates = []
      const today = new Date()
      const todayString = formatDate(today)
      
      // 获取当前日期所在周的范围
      const weekRange = getWeekRange(this.currentDate)
      const startDate = new Date(weekRange.start)
      
      // 生成7天的日期
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const dateString = formatDate(date)
        const isToday = dateString === todayString
        const isOtherMonth = date.getMonth() !== this.currentDate.getMonth()
        const hasTodo = this.checkHasTodo(dateString)
        
        dates.push({
          date: date,
          dateString: dateString,
          day: date.getDate(),
          isToday: isToday,
          isOtherMonth: isOtherMonth,
          hasTodo: hasTodo
        })
      }
      
      this.weekDates = dates
    },

    // 检查某日期是否有待办事项
    checkHasTodo(dateString) {
      return this.todos.some(todo => 
        todo.startDate === dateString || todo.endDate === dateString
      )
    },

    // 选择日期
    selectDate(date) {
      this.$emit('date-change', date.dateString)
    },

    // 上一周
    prevWeek() {
      const newDate = new Date(this.currentDate)
      newDate.setDate(newDate.getDate() - 7)
      this.currentDate = newDate
      this.generateWeekDates()
    },

    // 下一周
    nextWeek() {
      const newDate = new Date(this.currentDate)
      newDate.setDate(newDate.getDate() + 7)
      this.currentDate = newDate
      this.generateWeekDates()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.week-calendar {
  .calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-lg;

    .month-info {
      display: flex;
      align-items: baseline;
      gap: $spacing-xs;

      .month-text {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-primary;
      }

      .year-text {
        font-size: $font-size-md;
        color: $text-secondary;
      }
    }

    .nav-buttons {
      display: flex;
      gap: $spacing-sm;

      .nav-btn {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        background-color: $border-color;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: $font-size-lg;
        color: $text-secondary;
        transition: all $transition-fast;

        &:active {
          background-color: darken($border-color, 5%);
          transform: scale(0.95);
        }
      }
    }
  }

  .week-header {
    display: flex;
    margin-bottom: $spacing-md;

    .week-day-header {
      flex: 1;
      text-align: center;
      font-size: $font-size-sm;
      color: $text-light;
      font-weight: 500;
    }
  }

  .week-dates {
    display: flex;
    gap: 4rpx;

    .date-item {
      flex: 1;
      height: 96rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: $border-radius-md;
      position: relative;
      transition: all $transition-fast;
      cursor: pointer;

      .date-number {
        font-size: $font-size-md;
        color: $text-primary;
        font-weight: 500;
        margin-bottom: 4rpx;
      }

      .date-dot {
        width: 8rpx;
        height: 8rpx;
        border-radius: 50%;
        background-color: $primary-color;
      }

      &.is-today {
        .date-number {
          color: $primary-color;
          font-weight: 600;
        }
      }

      &.is-selected {
        background-color: $primary-color;
        
        .date-number {
          color: $white;
        }
        
        .date-dot {
          background-color: $white;
        }
      }

      &.is-other-month {
        .date-number {
          color: $text-placeholder;
        }
      }

      &.has-todo:not(.is-selected) {
        .date-dot {
          background-color: $warning-color;
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
</style>
