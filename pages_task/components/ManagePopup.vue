<!--
 * @Description: 管理弹窗组件 - 从右上角弹出的管理菜单
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30
-->
<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="bottom"
    :round="20"
    :safeAreaInsetTop="true"
    :closeOnClickOverlay="true"
  >
    <view class="manage-popup">
      <!-- 弹窗头部 -->
      <view class="popup-header">
        <text class="popup-title">管理中心</text>
        <view class="close-btn" @click="handleClose">
          <text>✕</text>
        </view>
      </view>

      <!-- 管理选项列表 -->
      <view class="manage-options">
        <!-- 标签管理 -->
        <view class="option-item" @click="goToTagManage">
          <view class="option-icon">
            <text>🏷️</text>
          </view>
          <view class="option-content">
            <text class="option-title">标签管理</text>
            <text class="option-desc">管理待办事项标签</text>
          </view>
          <view class="option-arrow">
            <text>›</text>
          </view>
        </view>

        <!-- 分类管理 -->
        <view class="option-item" @click="goToCategoryManage">
          <view class="option-icon">
            <text>📁</text>
          </view>
          <view class="option-content">
            <text class="option-title">分类管理</text>
            <text class="option-desc">管理待办事项分类</text>
          </view>
          <view class="option-arrow">
            <text>›</text>
          </view>
        </view>

        <!-- 数据统计 -->
        <view class="option-item" @click="goToStatistics">
          <view class="option-icon">
            <text>📊</text>
          </view>
          <view class="option-content">
            <text class="option-title">数据统计</text>
            <text class="option-desc">查看待办完成情况</text>
          </view>
          <view class="option-arrow">
            <text>›</text>
          </view>
        </view>

        <!-- 设置 -->
        <view class="option-item" @click="goToSettings">
          <view class="option-icon">
            <text>⚙️</text>
          </view>
          <view class="option-content">
            <text class="option-title">设置</text>
            <text class="option-desc">应用设置与偏好</text>
          </view>
          <view class="option-arrow">
            <text>›</text>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="popup-safe-area"></view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'ManagePopup',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 跳转到标签管理
    goToTagManage() {
      this.handleClose()
      uni.navigateTo({
        url: '/pages_task/tag-manage/index'
      })
    },

    // 跳转到分类管理
    goToCategoryManage() {
      this.handleClose()
      uni.navigateTo({
        url: '/pages_task/category-manage/index'
      })
    },

    // 跳转到数据统计（暂时显示提示）
    goToStatistics() {
      this.handleClose()
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 跳转到设置（暂时显示提示）
    goToSettings() {
      this.handleClose()
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.manage-popup {
  background-color: $card-background;
  min-height: 400rpx;
  border-radius: 0 0 $border-radius-lg $border-radius-lg;
  overflow: hidden;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg $spacing-lg $spacing-md $spacing-lg;
    border-bottom: 1rpx solid $divider-color;
    background-color: $card-background;

    .popup-title {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $text-primary;
    }

    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $border-color;
      border-radius: 50%;
      font-size: $font-size-lg;
      color: $text-secondary;
      transition: all $transition-fast;

      &:active {
        background-color: darken($border-color, 10%);
        transform: scale(0.95);
      }
    }
  }

  .manage-options {
    padding: $spacing-md;

    .option-item {
      display: flex;
      align-items: center;
      padding: $spacing-lg;
      background-color: $card-background;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-sm;
      border: 1rpx solid rgba(0, 0, 0, 0.06);
      transition: all $transition-fast;

      &:active {
        background-color: $border-color;
        transform: scale(0.98);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .option-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: $border-radius-md;
        font-size: 48rpx;
        margin-right: $spacing-md;
      }

      .option-content {
        flex: 1;

        .option-title {
          display: block;
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4rpx;
        }

        .option-desc {
          font-size: $font-size-sm;
          color: $text-light;
        }
      }

      .option-arrow {
        font-size: $font-size-xl;
        color: $text-light;
        margin-left: $spacing-sm;
      }
    }
  }

  .popup-safe-area {
    height: env(safe-area-inset-bottom);
    min-height: $spacing-md;
  }
}
</style>
