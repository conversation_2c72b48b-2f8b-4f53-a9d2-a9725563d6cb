<!--
 * @Description: 状态徽章组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view 
    class="status-badge" 
    :class="statusClass"
    :style="{ 
      backgroundColor: statusConfig.bgColor,
      color: statusConfig.color,
      borderColor: statusConfig.color + '40'
    }"
  >
    <text class="status-icon">{{ statusConfig.icon }}</text>
    <text class="status-text">{{ statusConfig.name }}</text>
  </view>
</template>

<script>
import { STATUS_CONFIG, TODO_STATUS } from '../data/constants.js'

export default {
  name: 'StatusBadge',
  props: {
    status: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: 'normal', // 'small' | 'normal' | 'large'
      validator: value => ['small', 'normal', 'large'].includes(value)
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    showText: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    statusConfig() {
      return STATUS_CONFIG[this.status] || STATUS_CONFIG[TODO_STATUS.PENDING]
    },

    statusClass() {
      return [
        `status-${this.status}`,
        `size-${this.size}`,
        {
          'icon-only': this.showIcon && !this.showText,
          'text-only': !this.showIcon && this.showText
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx $spacing-sm;
  border-radius: $border-radius-sm;
  font-weight: 500;
  border: 1rpx solid;
  white-space: nowrap;
  transition: all $transition-fast;

  .status-icon {
    font-size: inherit;
    line-height: 1;
  }

  .status-text {
    font-size: inherit;
    line-height: 1;
  }

  // 尺寸变体
  &.size-small {
    padding: 2rpx $spacing-xs;
    font-size: $font-size-xs;
    gap: 2rpx;

    .status-icon {
      font-size: $font-size-sm;
    }
  }

  &.size-normal {
    padding: 4rpx $spacing-sm;
    font-size: $font-size-sm;
    gap: 4rpx;

    .status-icon {
      font-size: $font-size-md;
    }
  }

  &.size-large {
    padding: $spacing-xs $spacing-md;
    font-size: $font-size-md;
    gap: $spacing-xs;

    .status-icon {
      font-size: $font-size-lg;
    }
  }

  // 仅图标模式
  &.icon-only {
    padding: 4rpx;
    min-width: 48rpx;
    justify-content: center;

    &.size-small {
      padding: 2rpx;
      min-width: 40rpx;
    }

    &.size-large {
      padding: $spacing-xs;
      min-width: 56rpx;
    }
  }

  // 仅文本模式
  &.text-only {
    .status-icon {
      display: none;
    }
  }

  // 状态特定样式
  &.status-pending {
    // 使用constants.js中定义的颜色
  }

  &.status-in_progress {
    // 使用constants.js中定义的颜色
  }

  &.status-completed {
    // 使用constants.js中定义的颜色
  }

  &.status-cancelled {
    // 使用constants.js中定义的颜色
  }

  &.status-on_hold {
    // 使用constants.js中定义的颜色
  }

  // 交互效果
  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}
</style>
