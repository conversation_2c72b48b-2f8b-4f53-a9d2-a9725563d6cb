<!--
 * @Description: 确认对话框组件 - 模拟this.$confirm1功能
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <u-popup :show="show" @close="handleCancel" mode="center" :round="20">
    <view class="confirm-dialog">
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <view class="dialog-content">
        <view class="dialog-icon" v-if="type">
          <text>{{ getIcon() }}</text>
        </view>
        <text class="dialog-message">{{ message }}</text>
      </view>

      <view class="dialog-actions">
        <button class="btn btn-secondary" @click="handleCancel">
          {{ cancelText }}
        </button>
        <button class="btn btn-primary" @click="handleConfirm">
          {{ confirmText }}
        </button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'warning', // 'success' | 'warning' | 'error' | 'info'
      validator: value => ['success', 'warning', 'error', 'info'].includes(value)
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    }
  },

  methods: {
    getIcon() {
      const icons = {
        success: '✅',
        warning: '⚠️',
        error: '❌',
        info: 'ℹ️'
      }
      return icons[this.type] || icons.warning
    },

    handleConfirm() {
      this.$emit('confirm')
      this.$emit('close')
    },

    handleCancel() {
      this.$emit('cancel')
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.confirm-dialog {
  background-color: $card-background;
  border-radius: $border-radius-lg;
  width: 560rpx;
  overflow: hidden;

  .dialog-header {
    padding: $spacing-lg $spacing-lg $spacing-sm;
    text-align: center;

    .dialog-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }
  }

  .dialog-content {
    padding: $spacing-md $spacing-lg $spacing-lg;
    text-align: center;

    .dialog-icon {
      font-size: 80rpx;
      margin-bottom: $spacing-md;
    }

    .dialog-message {
      font-size: $font-size-md;
      color: $text-secondary;
      line-height: 1.5;
      white-space: pre-line;
    }
  }

  .dialog-actions {
    display: flex;
    border-top: 1rpx solid $divider-color;

    .btn {
      flex: 1;
      height: 88rpx;
      border-radius: 0;
      font-size: $font-size-md;
      font-weight: 500;
      transition: all $transition-fast;
      border: none;
      border-right: 1rpx solid $divider-color;

      &:last-child {
        border-right: none;
      }

      &.btn-secondary {
        background-color: transparent;
        color: $text-secondary;

        &:active {
          background-color: $border-color;
        }
      }

      &.btn-primary {
        background-color: transparent;
        color: $primary-color;
        font-weight: 600;

        &:active {
          background-color: $primary-light;
        }
      }
    }
  }
}
</style>
