<!--
 * @Description: 待办列表页面 - 完整的筛选和列表功能
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30 19:55:54
 * @Layout: 顶部筛选区域 + 列表区域
-->
<template>
  <view class="todo-list-page">
    <!-- 筛选区域 -->
    <view class="filter-section">
      <!-- 筛选触发器 - 搜索框样式 -->
      <view class="filter-search-trigger" @click="toggleFilter">
        <view class="search-icon">🔍</view>
        <view class="search-content">
          <text class="search-placeholder ml-10" v-if="activeFilterCount === 0">点击筛选待办事项</text>
          <text class="search-active" v-else>已应用 {{ activeFilterCount }} 个筛选条件</text>
        </view>
        <text class="filter-arrow" :class="{ expanded: showFilter }">▼</text>
      </view>

      <!-- 可折叠的筛选内容 -->
      <view class="filter-content" v-show="showFilter">
        <!-- 搜索关键词 -->
        <view class="filter-row mt-20">
          <u-search
          v-model="searchKeyword"
          placeholder="输入关键词搜索"
          :show-action="false"
          bg-color="#f5f5f5"
          border-radius="8"
          @search="onSearch"
          @clear="onSearchClear"
        />
        </view>

        <!-- 状态筛选 -->
        <view class="filter-row">
          <text class="filter-label">状态</text>
          <view class="filter-tags-wrap">
            <view
              class="filter-tag"
              :class="{ active: selectedStatusList.length === 0 }"
              @click="clearStatus()"
            >
              <text class="filter-tag-text">全部</text>
            </view>
            <view
              v-for="(config, status) in statusConfigs"
              :key="status"
              class="filter-tag status-multi-select"
              :class="{ active: selectedStatusList.includes(config.name) }"
              @click="toggleStatus(config.name)"
            >
              <text class="filter-tag-icon">{{ config.icon }}</text>
              <text class="filter-tag-text">{{ config.name }}</text>
              <text class="status-check" v-if="selectedStatusList.includes(config.name)">✓</text>
            </view>
          </view>
        </view>

        <!-- 优先级筛选 -->
        <view class="filter-row">
          <text class="filter-label">优先级</text>
          <scroll-view class="filter-scroll" scroll-x>
            <view class="filter-tags">
              <view
                class="filter-tag"
                :class="{ active: selectedPriority === null }"
                @click="selectPriority(null)"
              >
                <text class="filter-tag-text">全部</text>
              </view>
              <view
                v-for="(config, priority) in priorityConfigs"
                :key="priority"
                class="filter-tag"
                :class="{ active: selectedPriority === config.name }"
                @click="selectPriority(config.name)"
              >
                <text class="filter-tag-icon">{{ config.icon }}</text>
                <text class="filter-tag-text">{{ config.name }}</text>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 分类筛选 -->
        <view class="filter-row" v-if="categories.length > 0">
          <text class="filter-label">分类</text>
          <view class="filter-tags-wrap">
            <view
              class="filter-tag"
              :class="{ active: selectedCategory === null }"
              @click="selectCategory(null)"
            >
              <text class="filter-tag-text">全部</text>
            </view>
            <view
              v-for="category in categories"
              :key="category.id"
              class="filter-tag"
              :class="{ active: selectedCategory === category.id }"
              @click="selectCategory(category.id)"
            >
              <text class="filter-tag-text">{{ category.name }}</text>
            </view>
          </view>
        </view>

        <!-- 标签筛选 -->
        <view class="filter-row" v-if="tags.length > 0">
          <text class="filter-label">标签</text>
          <view class="filter-tags-wrap">
            <view
              class="filter-tag"
              :class="{ active: selectedTags.length === 0 }"
              @click="clearTags()"
            >
              <text class="filter-tag-text">全部</text>
            </view>
            <view
              v-for="tag in tags"
              :key="tag.id"
              class="filter-tag tag-multi-select"
              :class="{ active: selectedTags.includes(tag.name) }"
              @click="toggleTag(tag.name)"
            >
              <text class="filter-tag-text">{{ tag.name }}</text>
              <text class="tag-check" v-if="selectedTags.includes(tag.name)">✓</text>
            </view>
          </view>
        </view>

        <!-- 时间范围筛选 -->
        <view class="filter-row">
          <text class="filter-label">时间</text>
          <view class="time-filter-options">
            <view
              class="time-filter-option"
              :class="{ active: selectedTimeRange === null }"
              @click="selectTimeRange(null)"
            >
              <text class="time-filter-text">全部</text>
            </view>
            <view
              class="time-filter-option"
              :class="{ active: selectedTimeRange === 'today' }"
              @click="selectTimeRange('today')"
            >
              <text class="time-filter-text">今天</text>
            </view>
            <view
              class="time-filter-option"
              :class="{ active: selectedTimeRange === 'week' }"
              @click="selectTimeRange('week')"
            >
              <text class="time-filter-text">本周</text>
            </view>
            <view
              class="time-filter-option"
              :class="{ active: selectedTimeRange === 'month' }"
              @click="selectTimeRange('month')"
            >
              <text class="time-filter-text">本月</text>
            </view>
            <view
              class="time-filter-option"
              :class="{ active: selectedTimeRange === 'year' }"
              @click="selectTimeRange('year')"
            >
              <text class="time-filter-text">本年</text>
            </view>
          </view>
        </view>

        <!-- 重置筛选按钮 -->
        <view class="filter-actions" v-if="activeFilterCount > 0">
          <view class="reset-btn" @click="resetFilters">
            <text class="reset-icon">🔄</text>
            <text class="reset-text">重置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 列表区域 -->
    <view class="list-container">
      <!-- 列表头部 -->
      <view class="list-header">
        <view class="list-left">
          <text class="list-title">待办列表</text>
          <text class="list-count">{{ totalCount }}</text>
        </view>
        <view class="quick-filter-btn"
              :class="{ active: ['未开始', '进行中', '处理中'].every(status => selectedStatusList.includes(status)) }"
              @click="togglePendingFilter">
          <text class="quick-filter-text">未完成</text>
        </view>
      </view>

      <!-- 待办列表 -->
      <view class="todo-list" v-if="todoList.length > 0">
        <TodoCard
          v-for="todo in todoList"
          :key="todo.id"
          :todo="todo"
          @click="goToTodoDetail(todo)"
          @status-change="onTodoStatusChange"
        />
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else-if="!loading">
        <view class="empty-icon">📝</view>
        <view class="empty-text">暂无符合条件的待办事项</view>
        <view class="empty-desc">尝试调整筛选条件或创建新的待办</view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-state" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 底部安全区域 -->
      <view class="scroll-bottom-safe"></view>
    </view>

    <!-- 右下角创建按钮 -->
    <view class="fab" @click="goToCreateTodo">
      <text>+</text>
    </view>
  </view>
</template>

<script>
import TodoCard from '../components/TodoCard.vue'
import { formatDate } from '../data/utils.js'
import { STATUS_CONFIG, PRIORITY_CONFIG } from '../data/constants.js'

export default {
  name: 'TodoListPage',
  components: {
    TodoCard
  },

  data() {
    return {
      // 列表数据
      todoList: [],
      categories: [],
      tags: [],
      loading: false,
      page: 1,
      pageSize: 10,
      totalCount: 0,
      hasMore: true,

      // 筛选条件
      selectedCategory: null,
      selectedTags: [],
      selectedStatusList: [], // 改为数组支持多选
      selectedPriority: null,
      selectedTimeRange: null,
      searchKeyword: '', // 搜索关键词

      // 筛选UI状态
      showFilter: false
    }
  },

  computed: {
    // 状态配置
    statusConfigs() {
      return STATUS_CONFIG
    },

    // 优先级配置
    priorityConfigs() {
      return PRIORITY_CONFIG
    },

    // 计算激活的筛选条件数量
    activeFilterCount() {
      let count = 0
      if (this.selectedCategory !== null) count++
      if (this.selectedTags.length > 0) count++
      if (this.selectedStatusList.length > 0) count++
      if (this.selectedPriority !== null) count++
      if (this.selectedTimeRange !== null) count++
      if (this.searchKeyword.trim() !== '') count++
      return count
    }
  },

  onShow() {
    this.loadBaseData()
    this.loadTodoList()
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      this.page = 1
      this.hasMore = true
      await this.loadTodoList()
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('下拉刷新失败:', error)
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      uni.stopPullDownRefresh()
    }
  },

  // 上拉加载更多
  async onReachBottom() {
    if (!this.hasMore) {
      uni.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 1500
      })
      return
    }

    try {
      await this.loadTodoList()
    } catch (error) {
      console.error('加载更多失败:', error)
      uni.showToast({
        title: '加载失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  methods: {
    // 加载待办事项列表
    async loadTodoList(refresh = true) {
      if (refresh) {
        this.page = 1
        this.hasMore = true
        this.todoList = []
      }

      if (this.loading || !this.hasMore) return

      try {
        this.loading = true

        const params = {
          page: this.page,
          pageSize: this.pageSize
        }

        // 添加筛选参数
        if (this.selectedCategory !== null) {
          params.categoryId = this.selectedCategory
        }
        if (this.selectedTags.length > 0) {
          params.tags = JSON.stringify(this.selectedTags)
        }
        if (this.selectedStatusList.length > 0) {
          params.status = JSON.stringify(this.selectedStatusList)
        }
        if (this.selectedPriority !== null) {
          params.priority = this.selectedPriority
        }
        if (this.selectedTimeRange) {
          const timeRange = this.getTimeRangeTimestamps(this.selectedTimeRange)
          if (timeRange) {
            params.startTime = timeRange.startTime
            params.endTime = timeRange.endTime
          }
        }
        if (this.searchKeyword.trim() !== '') {
          params.keyword = this.searchKeyword.trim()
        }

        console.log('请求参数:', params)
        const response = await this.$ajax.get('/task/list', params)

        if (response?.code === 200) {
          const todos = Array.isArray(response.rows) ? response.rows : []

          if (this.page === 1) {
            this.todoList = todos
          } else {
            this.todoList = [...this.todoList, ...todos]
          }

          this.totalCount = response.total || 0
          this.hasMore = todos.length === this.pageSize

          console.log('加载待办列表成功:', todos.length, '条数据，总计:', this.totalCount, '当前页:', this.page)

          // 只有成功加载数据后才增加页码
          this.page++
        } else {
          throw new Error(response?.msg || '获取待办列表失败')
        }
      } catch (error) {
        console.error('加载待办列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载基础数据
    async loadBaseData() {
      try {
        // 加载分类数据
        const categoriesRes = await this.$ajax.get('/task/category/list')
        if (categoriesRes?.code === 200) {
          this.categories = Array.isArray(categoriesRes.data) ? categoriesRes.data : []
        }

        // 加载标签数据
        const tagsRes = await this.$ajax.get('/task/tag/list')
        if (tagsRes?.code === 200) {
          this.tags = Array.isArray(tagsRes.data) ? tagsRes.data : []
        }

        console.log('加载基础数据成功 - 分类:', this.categories.length, '标签:', this.tags.length)
      } catch (error) {
        console.error('加载基础数据失败:', error)
      }
    },

    // 切换筛选显示
    toggleFilter() {
      this.showFilter = !this.showFilter
    },

    // 筛选方法
    selectCategory(categoryId) {
      this.selectedCategory = categoryId
      this.loadTodoList()
    },

    toggleTag(tagId) {
      const index = this.selectedTags.indexOf(tagId)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tagId)
      }
      this.loadTodoList()
    },

    clearTags() {
      this.selectedTags = []
      this.loadTodoList()
    },

    // 切换状态选择（多选）
    toggleStatus(status) {
      const index = this.selectedStatusList.indexOf(status)
      if (index > -1) {
        this.selectedStatusList.splice(index, 1)
      } else {
        this.selectedStatusList.push(status)
      }
      this.loadTodoList()
    },

    // 清除状态筛选
    clearStatus() {
      this.selectedStatusList = []
      this.loadTodoList()
    },

    selectPriority(priority) {
      this.selectedPriority = priority
      this.loadTodoList()
    },

    selectTimeRange(timeRange) {
      this.selectedTimeRange = timeRange
      this.loadTodoList()
    },

    // 获取时间范围的时间戳
    getTimeRangeTimestamps(timeRange) {
      const now = new Date()
      let startTime, endTime

      switch (timeRange) {
        case 'today':
          // 今天 00:00:00 到 23:59:59
          startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000
          endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime() / 1000
          break

        case 'week':
          // 本周一 00:00:00 到本周日 23:59:59
          const dayOfWeek = now.getDay() || 7 // 周日为0，转换为7
          const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 1)
          const endOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek + 7, 23, 59, 59)
          startTime = startOfWeek.getTime() / 1000
          endTime = endOfWeek.getTime() / 1000
          break

        case 'month':
          // 本月1号 00:00:00 到本月最后一天 23:59:59
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
          startTime = startOfMonth.getTime() / 1000
          endTime = endOfMonth.getTime() / 1000
          break

        case 'year':
          // 本年1月1号 00:00:00 到12月31号 23:59:59
          const startOfYear = new Date(now.getFullYear(), 0, 1)
          const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
          startTime = startOfYear.getTime() / 1000
          endTime = endOfYear.getTime() / 1000
          break

        default:
          return null
      }

      return {
        startTime: Math.floor(startTime),
        endTime: Math.floor(endTime)
      }
    },

    // 切换未完成筛选
    togglePendingFilter() {
      const pendingStatuses = ['未开始', '进行中', '处理中']
      const isPendingSelected = pendingStatuses.every(status => this.selectedStatusList.includes(status))

      if (isPendingSelected) {
        // 如果已经选择了所有未完成状态，则清除筛选
        this.selectedStatusList = []
      } else {
        // 设置为未完成状态（未开始、进行中、处理中）
        this.selectedStatusList = [...pendingStatuses]
      }
      this.loadTodoList()
    },

    // 搜索
    onSearch() {
      this.loadTodoList()
    },

    // 清除搜索
    onSearchClear() {
      this.searchKeyword = ''
      this.loadTodoList()
    },

    resetFilters() {
      this.selectedCategory = null
      this.selectedTags = []
      this.selectedStatusList = []
      this.selectedPriority = null
      this.selectedTimeRange = null
      this.searchKeyword = ''
      this.loadTodoList()
    },

    // 跳转到待办详情
    goToTodoDetail(todo) {
      uni.navigateTo({
        url: `/pages_task/detail/index?id=${todo.id}`
      })
    },

    // 跳转到创建待办
    goToCreateTodo() {
      uni.navigateTo({
        url: '/pages_task/detail/save'
      })
    },

    // 待办状态变化处理
    async onTodoStatusChange(todoId, newStatus) {
      try {
        uni.showLoading({ title: '更新中...' })

        const todo = this.todoList.find(t => t.id === todoId)
        if (!todo) {
          throw new Error('待办事项不存在')
        }

        const updateData = {
          id: todoId,
          categoryId: todo.categoryId,
          content: todo.content,
          images: todo.images || [],
          status: newStatus,
          priority: todo.priority,
          deadline: todo.deadline || 0,
          tags: Array.isArray(todo.tags) ? todo.tags.map(tag => tag.name) : []
        }

        const response = await this.$ajax.post('/task/save', updateData)

        uni.hideLoading()

        if (response?.code === 200) {
          todo.status = newStatus
          this.$toast('状态更新成功')
        } else {
          throw new Error(response?.msg || '更新失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('更新待办状态失败:', error)
        this.$toast(error.message || '更新失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.todo-list-page {
  min-height: 100vh;
  background-color: $background-color;
  padding-bottom: 140rpx;
}

/* 筛选区域 */
.filter-section {
  background-color: $card-background;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid $border-color;
}

.filter-search-trigger {
  display: flex;
  align-items: center;
  margin: $spacing-md;
  padding: $spacing-sm $spacing-lg;
  background-color: #f5f5f5;
  border-radius: 100rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:active {
    background-color: #eeeeee;
  }

  .search-icon {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-right: $spacing-sm;
  }

  .search-content {
    flex: 1;

    .search-placeholder {
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .search-active {
      font-size: $font-size-md;
      color: $primary-color;
      font-weight: 500;
    }
  }

  .filter-arrow {
    font-size: $font-size-sm;
    color: $text-secondary;
    transition: transform 0.3s ease;
    margin-left: $spacing-sm;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.filter-content {
  padding: 0 $spacing-md $spacing-md;
  border-top: 1rpx solid $border-color;
  overflow: hidden;
  transition: all 0.3s ease;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



.filter-row {
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: $font-size-sm;
  font-weight: 500;
  color: $text-primary;
  margin-bottom: $spacing-xs;
  display: block;
}

.search-wrapper {
  width: 100%;
}

.filter-scroll {
  width: 100%;
}

.filter-tags {
  display: flex;
  gap: $spacing-sm;
  padding-right: $spacing-md;
}

.filter-tags-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.filter-tag {
  flex-shrink: 0;
  padding: 6rpx $spacing-sm;
  background-color: $border-color;
  border-radius: $border-radius-sm;
  display: flex;
  align-items: center;
  gap: 4rpx;
  position: relative;

  .filter-tag-icon {
    font-size: 20rpx;
    line-height: 1;
  }

  .filter-tag-text {
    font-size: $font-size-xs;
    color: $text-secondary;
    white-space: nowrap;
  }

  &.active {
    background-color: $primary-color;

    .filter-tag-text {
      color: $white;
    }
  }

  // 标签多选样式
  &.tag-multi-select {
    &.active {
      background-color: rgba($primary-color, 0.1);
      border: 1rpx solid rgba($primary-color, 0.3);

      .filter-tag-text {
        color: $primary-color;
      }
    }

    .tag-check {
      position: absolute;
      right: 4rpx;
      bottom: 4rpx;
      font-size: 16rpx;
      color: $primary-color;
      line-height: 1;
    }
  }

  // 状态多选样式
  &.status-multi-select {
    &.active {
      background-color: rgba($primary-color, 0.1);
      border: 1rpx solid rgba($primary-color, 0.3);

      .filter-tag-text {
        color: $primary-color;
      }
    }

    .status-check {
      position: absolute;
      right: 4rpx;
      bottom: 4rpx;
      font-size: 16rpx;
      color: $primary-color;
      line-height: 1;
    }
  }
}

.filter-group {
  flex: 1;
  margin-right: $spacing-md;

  &:last-child {
    margin-right: 0;
  }
}

.filter-options {
  display: flex;
  gap: 8rpx;
}

.filter-option {
  flex: 1;
  padding: 8rpx $spacing-sm;
  background-color: $border-color;
  border-radius: $border-radius-sm;
  text-align: center;

  .filter-option-text {
    font-size: $font-size-xs;
    color: $text-secondary;
  }

  &.active {
    background-color: $primary-color;

    .filter-option-text {
      color: $white;
    }
  }
}

.time-filter-options {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.time-filter-option {
  padding: 6rpx $spacing-sm;
  background-color: $border-color;
  border-radius: $border-radius-sm;

  .time-filter-text {
    font-size: $font-size-xs;
    color: $text-secondary;
  }

  &.active {
    background-color: $primary-color;

    .time-filter-text {
      color: $white;
    }
  }
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: $spacing-sm;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx $spacing-sm;
  background-color: transparent;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;

  .reset-icon {
    font-size: 20rpx;
    color: $text-secondary;
  }

  .reset-text {
    font-size: $font-size-xs;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-color;
  }
}

/* 列表区域 */
.list-container {
  padding: 0 $spacing-md;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-lg $spacing-xs $spacing-md $spacing-xs;
  background-color: $background-color;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

  .list-left {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  .list-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
  }

  .list-count {
    font-size: $font-size-sm;
    color: $text-light;
    background-color: $border-color;
    padding: 4rpx $spacing-sm;
    border-radius: $border-radius-sm;
    min-width: 40rpx;
    text-align: center;
  }

  .quick-filter-btn {
    padding: 6rpx $spacing-sm;
    background-color: $border-color;
    border-radius: $border-radius-sm;

    .quick-filter-text {
      font-size: $font-size-xs;
      color: $text-secondary;
    }

    &.active {
      background-color: $primary-color;

      .quick-filter-text {
        color: $white;
      }
    }
  }
}

.todo-list {
  padding: $spacing-md 0;

  .todo-card {
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  min-height: 50vh;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-lg;
    opacity: 0.3;
  }

  .empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    font-weight: 500;
  }

  .empty-desc {
    font-size: $font-size-md;
    color: $text-light;
    text-align: center;
  }
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  padding: $spacing-lg;

  .loading-text {
    font-size: $font-size-md;
    color: $text-light;
  }
}

/* 底部安全区域 */
.scroll-bottom-safe {
  height: 40rpx;
}

/* 右下角创建按钮 */
.fab {
  position: fixed;
  right: $spacing-lg;
  bottom: calc($spacing-xl + env(safe-area-inset-bottom));
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  font-weight: 300;
  box-shadow: $shadow-lg;
  z-index: 999;
  transition: all $transition-normal;

  &:active {
    transform: scale(0.95);
    box-shadow: $shadow-md;
  }

  @media (max-height: 600px) {
    bottom: $spacing-lg;
  }
}
</style>
