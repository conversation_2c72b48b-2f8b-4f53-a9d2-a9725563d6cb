<!--
 * @Description: 待办模式组件 - 待办首页界面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
 * @Layout: 垂直flex布局，日历高度自适应，列表区域可滚动
-->
<template>
  <view class="todo-mode mode-content">
    <!-- 日历区域 - 高度自适应 -->
    <view class="calendar-container fs">
      <xj-calendar
        @on-click="onDateChange"
        @month-change="onMonthChange"
        :open="false"
        enableDragSelect
        :signeddates="signedDates"
      />
    </view>

    <!-- 待办列表区域 - 可滚动 -->
    <view class="todo-list-container">
      <view class="section-header fs">
        <view class="section-left">
          <text class="section-title">列表</text>
          <text class="section-count">{{ totalCount }}</text>
          <!-- 语音录音按钮 -->
          <view class="voice-record-btn" @click="goToVoiceCreate">
            <text class="voice-icon">🎤</text>
          </view>
        </view>
        <view class="header-actions">
          <!-- 筛选按钮 -->
          <view class="filter-tabs">
            <view
              class="filter-tab"
              :class="{ active: currentFilter === 'all' }"
              @click="setFilter('all')"
            >
              <text class="filter-text">全部</text>
            </view>
            <view
              class="filter-tab"
              :class="{ active: currentFilter === 'pending' }"
              @click="setFilter('pending')"
            >
              <text class="filter-text">未完成</text>
            </view>
          </view>
          <!-- 查看全部按钮 -->
          <view class="view-all-btn" @click="goToAllTodos">
            <text class="view-all-icon">📋</text>
          </view>
        </view>
      </view>

      <!-- 待办列表 -->
      <view class="todo-list" v-if="recentTodos.length > 0">
        <TodoCard
          v-for="todo in recentTodos"
          :key="todo.id"
          :todo="todo"
          @click="goToTodoDetail(todo)"
          @status-change="onTodoStatusChange"
        />
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <view class="empty-icon">📝</view>
        <view class="empty-text">暂无待办事项</view>
        <view class="empty-desc">点击右下角按钮创建第一个待办</view>
      </view>

      <!-- 底部安全区域 -->
      <view class="scroll-bottom-safe"></view>
    </view>

    <!-- 右下角创建按钮 -->
    <view class="fab" @click="goToCreateTodo">
      <text>+</text>
    </view>

    <!-- 管理弹窗 -->
    <ManagePopup :show="showManagePopup" @close="closeManagePopup" />
  </view>
</template>

<script>
import WeekCalendar from '../components/WeekCalendar.vue'
import TodoCard from '../components/TodoCard.vue'
import ManagePopup from '../components/ManagePopup.vue'
import { formatDate } from '../data/utils.js'

import xjCalendar from '@/components/xj-calendar/xj-calendar.vue'
export default {
  name: 'TodoMode',
  components: {
    WeekCalendar,
    TodoCard,
    ManagePopup,
    xjCalendar
  },

  data() {
    return {
      selectedDate: formatDate(new Date()),
      recentTodos: [],
      categories: [],
      tags: [],
      showManagePopup: false,
      loading: false,
      page: 1,
      pageSize: 10,
      totalCount: 0,
      hasMore: true,
      signedDates: [],
      currentFilter: 'all' // 当前筛选状态：all-全部，pending-未完成
    }
  },

  onShow() {
    this.loadData()
    this.loadMonthDates()
  },


  // 点击右上角
  onNavigationBarButtonTap(e) {
    console.log('点击右上角按钮:', e)
    this.showManagePopup = true
  },

  // 下拉刷新
  async onPullDownRefresh() {
    console.log('下拉刷新')
    try {
      this.page = 1
      this.hasMore = true
      await this.loadTodoList()
      uni.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('下拉刷新失败:', error)
      uni.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      uni.stopPullDownRefresh()
    }
  },

  // 上拉加载更多
  async onReachBottom() {
    console.log('上拉加载更多')
    if (!this.hasMore) {
      uni.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 1500
      })
      return
    }

    try {
      await this.loadTodoList()
    } catch (error) {
      console.error('加载更多失败:', error)
      uni.showToast({
        title: '加载失败',
        icon: 'none',
        duration: 2000
      })
    }
  },

  methods: {
    change(e) {
      console.log(e)
    },
    // 加载数据
    async loadData(refresh = true) {
      if (refresh) {
        this.page = 1
        this.hasMore = true
        this.recentTodos = []
      }

      if (this.loading || !this.hasMore) return

      try {
        this.loading = true

        // 加载待办事项列表
        await this.loadTodoList()

        // 加载基础数据（分类和标签）
        // await this.loadBaseData()

      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载待办事项列表
    async loadTodoList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize
      }

      // 根据筛选条件添加状态参数
      if (this.currentFilter === 'pending') {
        params.status = JSON.stringify(["未开始", "进行中", "处理中"]) // 0表示未完成
      }

      console.log('请求参数:', params)
      const response = await this.$ajax.get('/task/list', params)

      if (response?.code == 200) {
        const todos = Array.isArray(response.rows) ? response.rows : []

        if (this.page == 1) {
          this.recentTodos = todos
        } else {
          this.recentTodos = [...this.recentTodos, ...todos]
        }

        this.totalCount = response.total || 0
        this.hasMore = todos.length === this.pageSize

        console.log('加载待办列表成功:', todos.length, '条数据，总计:', this.totalCount, '当前页:', this.page)

        // 只有成功加载数据后才增加页码，为下次加载做准备
        this.page++
      } else {
        throw new Error(response?.msg || '获取待办列表失败')
      }
    },

    // 加载基础数据
    async loadBaseData() {
      try {
        // 加载分类数据
        const categoriesRes = await this.$ajax.get('/task/category/list')
        if (categoriesRes?.code == 200) {
          this.categories = Array.isArray(categoriesRes.data) ? categoriesRes.data : []
        }

        // 加载标签数据
        const tagsRes = await this.$ajax.get('/task/tag/list')
        if (tagsRes?.code === 200) {
          this.tags = Array.isArray(tagsRes.data) ? tagsRes.data : []
        }

        console.log('加载基础数据成功 - 分类:', this.categories.length, '标签:', this.tags.length)
      } catch (error) {
        console.error('加载基础数据失败:', error)
      }
    },

    // 日期变化处理
    onDateChange(date) {
      console.log("🚀 -> onDateChange -> date:", date)
      this.selectedDate = date
      // 可以根据选中日期筛选待办事项
      this.filterTodosByDate(date)
    },

    // 根据日期筛选待办事项
    async filterTodosByDate(date) {
      try {
        this.loading = true

        const params = {
          page: 1,
          pageSize: this.pageSize,
          dates: JSON.stringify([date])
        }

        const response = await this.$ajax.get('/task/list', params)

        if (response?.code === 200) {
          const filteredTodos = Array.isArray(response.rows) ? response.rows : []

          if (filteredTodos.length > 0) {
            this.recentTodos = filteredTodos
          } else {
            // 如果当天没有待办，重新加载全部数据
            this.page = 1
            await this.loadTodoList()
          }

          this.totalCount = response.total || 0
          console.log(`筛选 ${date} 的待办事项:`, filteredTodos.length, '条')
        } else {
          throw new Error(response?.msg || '筛选失败')
        }
      } catch (error) {
        console.error('日期筛选失败:', error)
        uni.showToast({
          title: '筛选失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },





    // 跳转到待办详情
    goToTodoDetail(todo) {
      uni.navigateTo({
        url: `/pages_task/detail/index?id=${todo.id}`
      })
    },

    // 跳转到创建待办
    goToCreateTodo() {
      uni.navigateTo({
        url: '/pages_task/detail/save'
      })
    },

    // 跳转到语音创建待办
    goToVoiceCreate() {
      uni.navigateTo({
        url: '/pages_task/detail/save?ai=1&autoVoice=1'
      })
    },

    // 待办状态变化处理
    async onTodoStatusChange(todoId, newStatus) {
      try {
        uni.showLoading({ title: '更新中...' })

        // 找到要更新的待办事项
        const todo = this.recentTodos.find(t => t.id === todoId)
        if (!todo) {
          throw new Error('待办事项不存在')
        }

        // 构建更新数据
        const updateData = {
          id: todoId,
          categoryId: todo.categoryId,
          content: todo.content,
          images: todo.images || [],
          status: newStatus,
          priority: todo.priority,
          deadline: todo.deadline || 0,
          tags: Array.isArray(todo.tags) ? todo.tags.map(tag => tag.name) : []
        }

        const response = await this.$ajax.post('/task/save', updateData)

        uni.hideLoading()

        if (response?.code === 200) {
          // 更新本地数据
          todo.status = newStatus

          this.$toast('状态更新成功')
        } else {
          throw new Error(response?.msg || '更新失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('更新待办状态失败:', error)
        this.$toast(error.message || '更新失败')
      }
    },



    // 关闭管理弹窗
    closeManagePopup() {
      this.showManagePopup = false
    },

    // 加载月份数据
    async loadMonthDates(monthParam) {
      if (!monthParam) {
        const now = new Date()
        monthParam = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`
      }
      const response = await this.$ajax.get('/task/month/dates', { month: monthParam })
      if (response?.code == 200) this.signedDates = response.data || []
    },

    // 月份切换事件处理
    onMonthChange(monthParam) {
      this.loadMonthDates(monthParam)
    },

    // 设置筛选条件
    setFilter(filter) {
      this.currentFilter = filter
      this.loadData(true) // 重新加载数据
    },

    // 跳转到查看全部页面
    goToAllTodos() {
      uni.navigateTo({
        url: '/pages_task/index/list'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

/*
 * 主容器布局设计
 * - 使用页面原生滚动，支持下拉刷新和上拉加载
 * - 背景色设置，内容自然流动
 */
.todo-mode {
  min-height: 100vh;
  background-color: $background-color;
  padding-bottom: 140rpx; /* 为FAB按钮留出空间 */
}

/* 日历容器 */
.calendar-container {
  background-color: $card-background;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid $border-color;
}

/* 待办列表容器 */
.todo-list-container {
  padding: 0 $spacing-md;

  /* 列表头部 - 固定不滚动 */
  .section-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg $spacing-xs $spacing-md $spacing-xs;
    background-color: $background-color;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .section-left {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
    }

    .section-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }

    .section-count {
      font-size: $font-size-sm;
      color: $text-light;
      background-color: $border-color;
      padding: 4rpx $spacing-sm;
      border-radius: $border-radius-sm;
      min-width: 40rpx;
      text-align: center;
    }

    .voice-record-btn {
      padding: 8rpx 12rpx;
      border-radius: $border-radius-sm;
      background-color: rgba(24, 144, 255, 0.08);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      margin-left: $spacing-xs;
      border: 1rpx solid rgba(24, 144, 255, 0.15);

      .voice-icon {
        font-size: 24rpx;
        color: #1890ff;
        line-height: 1;
      }

      &:active {
        transform: scale(0.95);
        background-color: rgba(24, 144, 255, 0.12);
        border-color: rgba(24, 144, 255, 0.25);
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
    }

    .filter-tabs {
      display: flex;
      align-items: center;
      gap: 4rpx;
      margin-right: $spacing-sm;
    }

    .filter-tab {
      padding: 8rpx $spacing-sm;
      border-radius: $border-radius-sm;
      background-color: $border-color;

      .filter-text {
        font-size: $font-size-sm;
        color: $text-secondary;
      }

      &.active {
        background-color: $primary-color;

        .filter-text {
          color: $white;
        }
      }
    }

    .view-all-btn {
      padding: 8rpx;

      .view-all-icon {
        font-size: 32rpx;
        color: $text-secondary;
      }
    }


  }



  /* 待办列表 */
  .todo-list {
    padding: $spacing-md 0;

    .todo-card {
      margin-bottom: $spacing-md;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    min-height: 50vh;

    .empty-icon {
      font-size: 120rpx;
      margin-bottom: $spacing-lg;
      opacity: 0.3;
    }

    .empty-text {
      font-size: $font-size-lg;
      color: $text-secondary;
      margin-bottom: $spacing-sm;
      font-weight: 500;
    }

    .empty-desc {
      font-size: $font-size-md;
      color: $text-light;
      text-align: center;
    }
  }

  /* 底部安全区域 */
  .scroll-bottom-safe {
    height: 40rpx;
  }
}

/* 右下角创建按钮 - 固定定位，不受滚动影响 */
.fab {
  position: fixed;
  right: $spacing-lg;
  bottom: calc($spacing-xl + env(safe-area-inset-bottom)); /* 适配安全区域 */
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  font-weight: 300;
  box-shadow: $shadow-lg;
  z-index: 999;
  transition: all $transition-normal;

  &:active {
    transform: scale(0.95);
    box-shadow: $shadow-md;
  }

  /* 确保按钮在所有设备上都可见 */
  @media (max-height: 600px) {
    bottom: $spacing-lg;
  }
}
</style>
