<!--
 * @Description: 标签管理页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view class="tag-manage-container">
    <!-- 标签列表 -->
    <view class="tag-list">
      <view
        v-for="tag in tags"
        :key="tag.id"
        class="tag-row"
      >
        <!-- 标签显示 -->
        <view class="tag-display">
          <view
            class="tag-chip"
            :style="{
              backgroundColor: tag.color + '15',
              color: tag.color,
              borderColor: tag.color + '30'
            }"
          >
            <text class="tag-text">{{ tag.name }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="tag-actions">
          <view class="action-icon edit-icon" @click="editTag(tag)">
            <text>✏️</text>
          </view>
          <view class="action-icon delete-icon" @click="deleteTagItem(tag)">
            <text>🗑️</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="tags.length === 0">
      <view class="empty-icon">🏷️</view>
      <view class="empty-text">暂无标签</view>
      <view class="empty-desc">点击右下角按钮创建第一个标签</view>
    </view>

    <!-- 添加按钮 -->
    <view class="fab" @click="addTag">
      <text>+</text>
    </view>

    <!-- 标签编辑弹窗 -->
    <u-popup :show="showEditDialog" @close="closeEditDialog" mode="bottom" :round="20">
      <view class="bottom-dialog">
        <!-- 拖拽指示器 -->
        <view class="drag-indicator"></view>

        <!-- 标题栏 -->
        <view class="dialog-header">
          <text class="dialog-title">{{ isEditMode ? '编辑标签' : '新建标签' }}</text>
          <view class="close-btn" @click="closeEditDialog">
            <text>×</text>
          </view>
        </view>

        <view class="dialog-content">
          <!-- 标签名称 -->
          <view class="form-section">
            <text class="section-title">标签名称</text>
            <view class="input-wrapper">
              <input
                class="name-input"
                type="text"
                placeholder="请输入标签名称"
                v-model="editForm.name"
                maxlength="15"
              />
            </view>
          </view>

          <!-- 颜色选择 -->
          <view class="form-section">
            <text class="section-title">选择颜色</text>
            <view class="color-palette">
              <view
                v-for="color in colorOptions"
                :key="color"
                class="color-item"
                :class="{ selected: editForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="selectColor(color)"
              >
                <view class="color-check" v-if="editForm.color === color">
                  <text>✓</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 预览效果 -->
          <view class="form-section">
            <text class="section-title">预览效果</text>
            <view class="preview-container">
              <view
                class="tag-preview"
                :style="{
                  backgroundColor: editForm.color + '15',
                  color: editForm.color,
                  borderColor: editForm.color + '30'
                }"
              >
                <text>{{ editForm.name || '标签名称' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="dialog-footer">
          <view class="button-group">
            <button class="cancel-btn" @click="closeEditDialog">取消</button>
            <button class="confirm-btn" @click="saveTagItem" :disabled="!canSave">
              {{ isEditMode ? '更新' : '创建' }}
            </button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>

export default {
  name: 'TagManage',
  data() {
    return {
      tags: [], // 确保初始化为空数组
      showEditDialog: false,
      isEditMode: false,
      editingTagId: '',
      editForm: {
        name: '',
        color: '#1890ff'
      },
      colorOptions: [
        '#1890ff', '#52c41a', '#faad14', '#ff4d4f',
        '#722ed1', '#13c2c2', '#eb2f96', '#f5222d',
        '#fa541c', '#fa8c16', '#a0d911', '#2f54eb',
        '#ff85c0', '#ffc53d', '#95de64', '#5cdbd3',
        '#85a5ff', '#b37feb', '#ff9c6e', '#ffd666',
        '#36cfc9', '#73d13d', '#40a9ff'
      ]
    }
  },

  computed: {
    canSave() {
      return this.editForm.name.trim().length > 0
    }
  },

  onShow() {
    this.loadData()
  },

  mounted() {
    // this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      try {
        uni.showLoading({ title: '加载中...' })
        const _res = await this.$ajax.get('/task/tag/list')
        if (_res?.code != 200) {
          uni.hideLoading()
          uni.showToast({
            title: _res?.msg || '加载失败',
            icon: 'none'
          })
          return
        }
        // 确保返回的数据是数组格式，防止find方法报错
        this.tags = Array.isArray(_res?.data) ? _res?.data : []
        console.log('加载标签数据:', this.tags)
        uni.hideLoading()
      } catch (error) {
        uni.hideLoading()
        console.error('加载标签列表失败:', error)
        // 出错时也要确保tags是数组
        this.tags = []
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },

    // 获取标签下的待办数量
    getTagTodoCount(tagId) {
      // 确保tags是数组，防止find方法报错
      if (!Array.isArray(this.tags)) {
        console.warn('tags不是数组格式:', this.tags)
        return 0
      }

      // 从API返回的数据中获取使用次数
      const tag = this.tags.find(t => t.id === tagId)
      console.log("🚀 -> getTagTodoCount -> tag:", tag)
      return tag ? (tag.useCount || 0) : 0
    },

    // 添加标签
    addTag() {
      console.log('点击添加标签按钮')
      this.isEditMode = false
      this.editingTagId = ''
      this.editForm = {
        name: '',
        color: '#1890ff'
      }
      this.showEditDialog = true
      console.log('弹窗状态:', this.showEditDialog)
    },

    // 编辑标签
    editTag(tag) {
      this.isEditMode = true
      this.editingTagId = tag.id
      this.editForm = {
        name: tag.name,
        color: tag.color
      }
      this.showEditDialog = true
    },

    // 删除标签
    deleteTagItem(tag) {
      const todoCount = this.getTagTodoCount(tag.id)
      let message = `确定要删除标签"${tag.name}"吗？`

      if (todoCount > 0) {
        message += `\n该标签被${todoCount}个待办事项使用，删除后这些待办将移除该标签。`
      }

      this.$confirm1(message, '删除确认', {
        confirmText: '删除',
        cancelText: '取消'
      }).then(async () => {
        try {
          uni.showLoading({ title: '删除中...' })
          await this.$ajax.post('/task/tag/delete', { id: tag.id })
          uni.hideLoading()

          await this.loadData()
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } catch (error) {
          uni.hideLoading()
          console.error('删除标签失败:', error)
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 选择颜色
    selectColor(color) {
      this.editForm.color = color
    },

    // 保存标签
    async saveTagItem() {
      if (!this.canSave) {
        uni.showToast({
          title: '请填写标签名称',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: this.isEditMode ? '更新中...' : '创建中...' })

        const tagData = {
          id: this.isEditMode ? this.editingTagId : 0,
          name: this.editForm.name.trim(),
          color: this.editForm.color
        }

        const _res = await this.$ajax.post('/task/tag/save', tagData)
        if (_res?.code != 200) {
          uni.hideLoading()
          uni.showToast({
            title: _res?.msg || '保存失败',
            icon: 'none'
          })
          return
        }

        uni.hideLoading()

        this.closeEditDialog()
        await this.loadData()

        uni.showToast({
          title: this.isEditMode ? '更新成功' : '创建成功',
          icon: 'success'
        })

      } catch (error) {
        uni.hideLoading()
        console.error('保存标签失败:', error)
      }
    },

    // 关闭编辑弹窗
    closeEditDialog() {
      this.showEditDialog = false
      this.isEditMode = false
      this.editingTagId = ''
      this.editForm = {
        name: '',
        color: '#1890ff'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.tag-manage-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: $spacing-lg;
}

.tag-list {
  .tag-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: $white;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(0, 0, 0, 0.03);
    transition: all $transition-fast;

    &:active {
      transform: translateY(1rpx) scale(0.998);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    }

    .tag-display {
      flex: 1;
      display: flex;
      align-items: center;

      .tag-chip {
        display: inline-flex;
        align-items: center;
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        border: 2rpx solid;
        font-size: 26rpx;
        font-weight: 500;
        transition: all $transition-fast;

        .tag-text {
          line-height: 1.2;
        }
      }
    }

    .tag-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-left: 24rpx;

      .action-icon {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.04);
        transition: all $transition-fast;
        cursor: pointer;

        text {
          font-size: 32rpx;
          line-height: 1;
        }

        &.edit-icon {
          &:active {
            background-color: rgba($primary-color, 0.1);
            transform: scale(0.9);
          }
        }

        &.delete-icon {
          &:active {
            background-color: rgba($error-color, 0.1);
            transform: scale(0.9);
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  margin-top: 30vh;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-lg;
    opacity: 0.3;
  }

  .empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    font-weight: 500;
  }

  .empty-desc {
    font-size: $font-size-md;
    color: $text-light;
    text-align: center;
  }
}

.fab {
  position: fixed;
  right: $spacing-lg;
  bottom: $spacing-xl;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: $primary-color;
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  font-weight: 300;
  box-shadow: $shadow-lg;
  z-index: 999;
  transition: all $transition-normal;

  &:active {
    transform: scale(0.95);
    box-shadow: $shadow-md;
  }
}

// 底部弹窗样式
.bottom-dialog {
  background-color: $white;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  max-height: 85vh;
  overflow: hidden;
  position: relative;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
  }

  // 标题栏
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $text-primary;
    }

    .close-btn {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-fast;

      text {
        font-size: 40rpx;
        color: $text-secondary;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .dialog-content {
    padding: 0 40rpx 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-section {
      margin-bottom: 48rpx;

      .section-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 24rpx;
      }

      // 输入框样式
      .input-wrapper {
        .name-input {
          width: 100%;
          height: 88rpx;
          padding: 0 24rpx;
          background-color: rgba(0, 0, 0, 0.03);
          border-radius: 16rpx;
          border: 2rpx solid transparent;
          font-size: 28rpx;
          color: $text-primary;
          transition: all $transition-fast;

          &:focus {
            border-color: $primary-color;
            background-color: $white;
            box-shadow: 0 0 0 6rpx rgba($primary-color, 0.08);
          }
        }
      }

      // 颜色选择器
      .color-palette {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 20rpx;

        .color-item {
          width: 72rpx;
          height: 72rpx;
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all $transition-fast;
          cursor: pointer;
          border: 4rpx solid transparent;
          position: relative;

          .color-check {
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);

            text {
              font-size: 20rpx;
              color: $text-primary;
              font-weight: 600;
            }
          }

          &.selected {
            border-color: rgba(0, 0, 0, 0.15);
            transform: scale(1.1);
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      // 预览区域
      .preview-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .tag-preview {
          padding: 12rpx 24rpx;
          border-radius: 12rpx;
          font-size: 24rpx;
          font-weight: 500;
          border: 2rpx solid;
          min-width: 160rpx;
          text-align: center;
          transition: all $transition-fast;

          text {
            line-height: 1.2;
          }
        }
      }
    }
  }

  // 底部按钮区域
  .dialog-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: $white;

    .button-group {
      display: flex;
      gap: 24rpx;

      .cancel-btn, .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        transition: all $transition-fast;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .cancel-btn {
        background-color: rgba(0, 0, 0, 0.05);
        color: $text-secondary;

        &:active {
          background-color: rgba(0, 0, 0, 0.08);
          transform: scale(0.98);
        }
      }

      .confirm-btn {
        background-color: $primary-color;
        color: $white;
        box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.3);

        &:active {
          background-color: $primary-dark;
          transform: scale(0.98);
        }

        &:disabled {
          background-color: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          opacity: 0.6;
        }
      }
    }
  }
}
</style>
