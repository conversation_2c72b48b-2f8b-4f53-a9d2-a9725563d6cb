## 1.0.8（2022-10-24）
### 修复小程序平台报错
## 1.0.7（2022-10-14）
### 修改获取权限时机
1.修改获取权限时机，避免由于未获得权限导致首次长按时无法取消录音；
2.优化APP端提示文案（APP端触发longpress事件后，如果手指没有任何移动，则此时松手无法监听到touchEnd事件，小程序端无此问题）
## 1.0.6（2022-09-26）
### 优化震动反馈
## 1.0.5（2022-09-25）
### 新增录音配置recordOptions
- 该配置各端支持情况不同，请自行查看官方说明
- 其中duration为录音时长（最大10分钟），在超限时将自动结束动画并返回录音文件地址
## 1.0.4（2022-09-25）
### 内置发起录音
- H5端不支持录音，故无法使用，有需要可自行在gitee拉取老的纯动画版本
- 已支持多端录音，不再仅是动画效果
- 多端自动判断是否拥有权限（无权限时进行toast提示）
- endRecord回调附带录音文件临时地址（详见下方示例）
## 1.0.3（2022-09-25）
### 增加震动反馈

- 已条件编译、增加支持微信小程序
## 1.0.2（2022-09-25）
### 增加震动反馈

- 已条件编译、仅支持app
## 1.0.1（2022-09-25）
### 新增主动通知组件结束方法

- 如：当录音时长超限时，可主动通知组件结束动画，此时组件会自动回调endRecord事件，正常处理即可。
## 1.0.0（2022-09-25）
### 首次发布

- 下边写不确定的只是没测试，请自行测试
