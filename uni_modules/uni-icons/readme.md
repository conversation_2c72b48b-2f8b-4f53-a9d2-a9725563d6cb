

## Icons 图标
> **组件名：uni-icons**
> 代码块： `uIcons`


用于展示 icons 图标 。

### 安装方式

本组件符合[easycom](https://uniapp.dcloud.io/collocation/pages?id=easycom)规范，`HBuilderX 2.5.5`起，只需将本组件导入项目，在页面`template`中即可直接使用，无需在页面中`import`和注册`components`。

如需通过`npm`方式使用`uni-ui`组件，另见文档：[https://ext.dcloud.net.cn/plugin?id=55](https://ext.dcloud.net.cn/plugin?id=55)

### 基本用法

在 ``template`` 中使用组件

```html
<uni-icons type="contact" size="30"></uni-icons>
```



## API

### Icons Props

|属性名	|类型		|默认值	|说明				|
|:-:	|:-:		|:-:	|:-:				|
|size	|Number		|24		|图标大小			|
|type	|String		|-		|图标图案，参考示例	|
|color	|String		|-		|图标颜色			|


### Icons Events
|事件名	|说明			|返回值|
|:-:	|:-:			|:-:  |
|@click|点击 Icon 触发事件|-    |



## 组件示例

点击查看：[https://hellouniapp.dcloud.net.cn/pages/extUI/icons/icons](https://hellouniapp.dcloud.net.cn/pages/extUI/icons/icons)