## 0.1.4（2021-08-20）
- 修复 在 uni-forms 的动态表单中默认值校验不通过的 bug
## 0.1.3（2021-08-11）
- 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
## 0.1.2（2021-07-30）
- 优化 vue3下事件警告的问题
## 0.1.1
- 优化 errorMessage 属性支持 Boolean 类型
## 0.1.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 0.0.16（2021-06-29）
- 修复 confirmType 属性（仅 type="text" 生效）导致多行文本框无法换行的 bug
## 0.0.15（2021-06-21）
- 修复 passwordIcon 属性拼写错误的 bug
## 0.0.14（2021-06-18）
- 新增 passwordIcon 属性，当type=password时是否显示小眼睛图标
- 修复 confirmType 属性不生效的问题
## 0.0.13（2021-06-04）
- 修复 disabled 状态可清出内容的 bug
## 0.0.12（2021-05-12）
- 新增 组件示例地址
## 0.0.11（2021-05-07）
- 修复 input-border 属性不生效的问题
## 0.0.10（2021-04-30）
- 修复 ios 遮挡文字、显示一半的问题
## 0.0.9（2021-02-05）
- 调整为uni_modules目录规范
- 优化 兼容 nvue 页面
