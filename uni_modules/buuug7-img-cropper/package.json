{"id": "buuug7-img-cropper", "displayName": "uniApp图片裁剪插件", "version": "0.0.9", "description": "uni-app 图片裁剪插件. 该插件基于Cropper.js, 使用 webview 的方式集成 cropper.js 跟 uni-app 进行数据交互, 可以满足图片裁剪的基本需求.", "keywords": ["cropper", "image", "cropper", "图片裁剪", "裁剪"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "3190136675"}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}