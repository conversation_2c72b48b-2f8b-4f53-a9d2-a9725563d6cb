# 使用方法
## 功能列表：
```
1、录音模式： mode 1 长按录音 2 点击录音
2、按住上移动，松开后会取消录音，目前默认的向上移到距离为80px (以按下的那一刻为标准)
3、可试听录音
4、startTxt 按钮下的文案 默认为：按住说话
5、recordTxt 按钮下的录音进行时文案 默认为：松手保存，上移取消
6、noSupportTxt 当前设置不支持录音时提示的文案 默认为：暂不支持录音
7、modalTitle iOS系统 当表示请求授权被拒绝后才进行展示 弹窗标题的文案 默认为：提示
8、modalContent iOS系统 当表示请求授权被拒绝后才进行展示 弹窗内容的文案 默认为：是否要开启麦克风权限？
9、authorizedTxt 由于系统兼容问题，除iOS外其他表示未获得授权时 调用都会有这提示 默认为：请允许使用或在系统设置中打开麦克风权限
10、@cbResult 当完成后的回调方法，方法返回的对象内容如：
	{
		tempFilePath: '', // 录音源
		showTime: '', // 时长 展示值，格式化为 00:00:00 的展示方式
		duration: 0, // 时长 秒数
		tempFile: null // 选择需要上传的源
	}
11、@cbClearResult 当开始录音时可重置录音数据的回调方法，如果有需要清空上一次的录音数据需求可使用，方法返回的对象内容同@cbResult
12、@cbPermissionTips 安卓审核隐私协议需要，使用音频的提示语言,或者直接在字段authorizedTxt处理也可以
```
# 使用方法
## 下载插件并导入到项目后，在页面中直接使用：
```
<xiaopao-record></xiaopao-record>
```

## 获取录音后的数据：
```
<xiaopao-record
	@cbResult="getCbResult"
></xiaopao-record>

// 获取录音结果
getCbResult (obj = {}) {
	console.log(obj)
}
```

## 绑定清除上一次录音的数据：
```
<xiaopao-record
	@cbClearResult="getCbClearResult"
></xiaopao-record>

// 获取录音结果
getCbClearResult (obj = {}) {
	console.log(obj)
}
```

## 当有安卓审核隐私协议需求时：
```
<xiaopao-record
	@cbPermissionTips="doCbPermissionTips"
></xiaopao-record>

// 获取录音结果
doCbPermissionTips () {
	// 这里为安卓审核隐私协议需要，使用音频的提示语言 处理，后续会继续上传相关的组件示例
},
```

## 点击模式配置：
```
1、mode传值为数字2
2、录制前点击按钮文案传值startTxt
3、录制中点击按钮文案传值recordTxt
如下：
<xiaopao-record
	:mode="2"
	startTxt="点击开始录制"
	recordTxt="点击结束录制"
></xiaopao-record>
```