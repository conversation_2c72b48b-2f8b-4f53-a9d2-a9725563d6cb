{"id": "xiaopao-record", "displayName": "长按录音，上移取消 ，并且支持点击录音模式", "version": "1.0.6", "description": "长按录音/点击录音插件", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "长按录音", "点击录音", "record", "xiaopao-record"], "repository": "", "engines": {"HBuilderX": "^4.03"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "录音权限"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "u", "app-uvue": "u", "app-harmony": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}