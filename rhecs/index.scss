// $pxUnit : rpx ;
// uniapp使用rpx单位
/*  #ifndef  APP-PLUS  */
$pxUnit : rpx;
/*  #endif  */
/*  #ifdef  APP-PLUS  */
$pxUnit : px;
/*  #endif  */
// @import 'framework/style/main.scss';

$commonLoopNum: 60; // 循环次数
$fontBegin: 20; // 字体循环开始
$fontEnd: 64; // 字体循环结束
$widthBegin: 30; // 宽度循环开始
$widthEnd: 140; // 宽度循环结束
$borderSizeEnd: 10; //
$borderEnd: 40; // border循环
$stackEnd: 20; // postion循环次数
//
// @import 'framework/style/global.scss'; // Global
// @import 'framework/style/flex.scss'; // Bootstrap
// // @import './bootstrap.scss'; // Bootstrap
// @import 'framework/style/text.scss'; //
// @import 'framework/style/border.scss'; //
// @import 'framework/style/grid.scss'; //
// @import 'framework/style/widget.scss'; //
// @import 'framework/style/animate.scss'; //


@import '../framework/index.css';