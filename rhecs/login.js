/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2025-03-31 21:02:51
 * @LastEditTime: 2025-04-02 09:49:39
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
const _api = require('@/framework/uni/api.js');
const _ajax = _api.default // api请求
// 检查登录并执行登录
export async function apiLogin() {
  const _apiLogin = async (jsCode) => {
    const _res = await _ajax.get(`/wx/req-auth-token?js_code=${jsCode}`);
    if (_res?.code == 200 && _res?.data?.token && _res?.data?.token != "") {
      const timeout = Date.now() + (6 * 24 * 60 * 60 * 1000);
      uni.setStorageSync("yzYicketTimeout", timeout);// 设置6天后的过期时间戳
      uni.setStorageSync("yzYicketToken", _res?.data?.token); // 存储
      return true;
    } else {
      uni.showToast({
        title: _res?.msg || "登录失败",
        icon: "none",
      });
      return false;
    }
  }
  const { code } = await wx.login();
  if (code && code != "") {
    return await _apiLogin(code); // code
  }
}


// 根据是否存在token检查是否登录
export async function checkToken() {
  const token = uni.getStorageSync("yzYicketToken");
  const timeout = uni.getStorageSync("yzYicketTimeout");

  // 检查token是否存在且未过期
  if (!token || token == "" || !timeout || timeout < Date.now()) {
    return await apiLogin();
  }
  return true;
}




