import CryptoJS from 'crypto-js'

class CryptoUtil {
  static DEFAULT_KEY = "2020_KQYY_@)!*+-"

  /**
   * 预处理加密字符串 - 处理URL编码和换行符
   * @param {String} encryptedData - 原始加密字符串
   * @returns {String} 处理后的字符串
   */
  static preprocessEncryptedData(encryptedData) {
    if (!encryptedData) return ''

    let processed = encryptedData

    // 1. URL解码
    try {
      processed = decodeURIComponent(processed)
    } catch (e) {
      // 如果不是URL编码，继续处理
    }

    // 2. 移除所有换行符和空白字符
    processed = processed.replace(/[\r\n\s]/g, '')

    // 3. 确保Base64格式正确
    processed = processed.replace(/[^A-Za-z0-9+/=]/g, '')

    return processed
  }

  /**
   * 加密数据 - 输出与Kotlin完全一致的格式
   * @param {Object} data - 要加密的数据对象
   * @param {String} key - 加密密钥
   * @returns {String} 加密后的字符串（与Kotlin输出格式一致）
   */
  static encryptAppdata(data, key = this.DEFAULT_KEY) {
    try {
      // 1. 转JSON（与Gson().toJson()保持一致）
      const jsonString = JSON.stringify(data)
      console.log('待加密JSON:', jsonString)

      // 2. 密钥处理
      const keyWordArray = CryptoJS.enc.Utf8.parse(key)

      // 3. AES-128-ECB加密
      const encrypted = CryptoJS.AES.encrypt(jsonString, keyWordArray, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      })

      // 4. 转为Base64字符串
      const result = encrypted.toString()
      console.log('加密结果:', result)

      return result
    } catch (error) {
      console.error('加密失败:', error)
      return null
    }
  }

  /**
   * 解密数据 - 严格支持各种输入格式
   * @param {String} encryptedData - 加密的字符串
   * @param {String} key - 解密密钥
   * @returns {Object|null} 解密后的对象
   */
  static decryptAppdata(encryptedData, key = this.DEFAULT_KEY) {
    try {
      console.log('原始加密数据:', encryptedData)

      // 1. 预处理加密字符串
      const processedData = this.preprocessEncryptedData(encryptedData)
      console.log('处理后的数据:', processedData)

      if (!processedData) {
        throw new Error('处理后的数据为空')
      }

      // 2. 密钥处理
      const keyWordArray = CryptoJS.enc.Utf8.parse(key)

      // 3. AES-128-ECB解密
      const decrypted = CryptoJS.AES.decrypt(processedData, keyWordArray, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      })

      // 4. 转为UTF-8字符串
      const decryptedString = decrypted.toString(CryptoJS.enc.Utf8)
      console.log('解密字符串:', decryptedString)

      if (!decryptedString) {
        throw new Error('解密后字符串为空，可能密钥错误或数据损坏')
      }

      // 5. 解析JSON
      const result = JSON.parse(decryptedString)
      console.log('解密结果:', result)

      return result
    } catch (error) {
      console.error('解密失败:', error.message)

      // 尝试不同的处理方式
      return this.tryAlternativeDecrypt(encryptedData, key)
    }
  }

  /**
   * 尝试替代解密方法
   */
  static tryAlternativeDecrypt(encryptedData, key = this.DEFAULT_KEY) {
    const alternatives = [
      // 方式1: 直接使用原始数据
      encryptedData,
      // 方式2: 只URL解码
      (() => {
        try { return decodeURIComponent(encryptedData) } catch { return encryptedData }
      })(),
      // 方式3: 只移除换行符
      encryptedData.replace(/[\r\n]/g, ''),
      // 方式4: URL解码 + 移除换行符
      (() => {
        try {
          return decodeURIComponent(encryptedData).replace(/[\r\n]/g, '')
        } catch {
          return encryptedData.replace(/[\r\n]/g, '')
        }
      })()
    ]

    for (let i = 0; i < alternatives.length; i++) {
      try {
        const data = alternatives[i]
        console.log(`尝试方式${i + 1}:`, data)

        const keyWordArray = CryptoJS.enc.Utf8.parse(key)
        const decrypted = CryptoJS.AES.decrypt(data, keyWordArray, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        })

        const decryptedString = decrypted.toString(CryptoJS.enc.Utf8)
        if (decryptedString) {
          const result = JSON.parse(decryptedString)
          console.log(`方式${i + 1}解密成功:`, result)
          return result
        }
      } catch (error) {
        console.log(`方式${i + 1}失败:`, error.message)
        continue
      }
    }

    console.error('所有解密方式都失败了')
    return null
  }

  /**
   * 生成登录数据
   */
  static generateLoginData(loginName, password, cid) {
    return this.encryptAppdata({
      loginName,
      password,
      cid
    })
  }

  /**
   * 测试方法 - 验证加密解密一致性
   */
  static test() {
    const testData = {
      loginName: "123",
      password: "password",
      phonenumber: "phonenumber",
      roleId: "roleId",
      deptId: "deptId",
      idcard: "idcard"
    }

    console.log('=== 测试开始 ===')
    const encrypted = this.encryptAppdata(testData)
    console.log('加密结果:', encrypted)

    const decrypted = this.decryptAppdata(encrypted)
    console.log('解密结果:', decrypted)

    console.log('数据一致性:', JSON.stringify(testData) === JSON.stringify(decrypted))
    console.log('=== 测试结束 ===')

    return { encrypted, decrypted }
  }
}

export default CryptoUtil