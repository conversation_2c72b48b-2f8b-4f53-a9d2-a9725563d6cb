/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2025-03-15 09:32:20
 * @LastEditTime: 2025-03-15 09:38:38
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
export const urlTobase64 = (fileName) => {
	let img = `/images/${fileName}`
	// 如果是微信小程序环境，返回base64Url
	// #ifdef MP-WEIXIN
	let imgBase64 = wx.getFileSystemManager().readFileSync(img, "base64"),
		base64Url = `data:image/png;base64,${imgBase64}`;
	return base64Url;
	// #endif
	// 如果不是微信小程序环境，直接返回img的url
	return img
};