/*
 * @Description: 调用自定义framework
 * @Author: 徐静(<EMAIL>)
 * @Date: 2022-11-29 19:53:44
 * @LastEditTime: 2025-08-02 10:21:31
 * @Copyright: (c) 2011-2021 http://www.winksoft.net All rights reserved.
 */
import Vue from 'vue'
// import router from '@/router'
import MyRouter from '@/framework/uni/router.js' // 使用UNI的路由
const _cfg = require('@/framework/config/config.js');
/* appId:  wx6eb8c457bebc89c9 [官方] wxfed82798145ae53f [单位测试] */

// 开发环境
// let _devUrl = "http://*************:14680"  //
let _devUrl = "http://localhost:8808"   // 生产环境
// _devUrl = "https://yy.yzckjt.com/alpha-book-api"  // 单位内网
// #ifdef H5
	_devUrl = "http://localhost:8808"
// #endif

// 生产环境
// let _proUrl = "http://************:8082"  // 生产环境
let _proUrl = "https://assistant.rxecs.com"  // 生产环境
// _proUrl = "https://yy.yzckjt.com/alpha-book-api"  // 单位的配置
const _url = process.env.NODE_ENV === 'development' ? _devUrl : _proUrl;

// 跳转页面
const _goLogin = () => {
    MyRouter.to(`/pages_login/login/login`)  // 403跳转登录页面
}


// 构建自定义配置项
_cfg.setConfig({
    baseURL: _url,
    // ruoyi的
    // web请求时，请求头的token的命名
    headerTokenName: "Authorization",
    // headerTokenPrefix: "Bearer ", // 若依框架header -> token需要拼接字符串
    headerTokenPrefix: "", // 若依框架header -> token需要拼接字符串
    apiErrorNeedLoginCode: "401",  // 当请求拿到的code为403时，表示token失效，需要重新登录
    fileUploadPath: "/upload/post-file", // 文件上传路径
    fileUploadBaseUrl: "http://localhost:8805/storage-api", // 文件上传字段名  相对路径
    fileUploadField: "file", // 文件上传字段名
    wkAjaxHeader:false,
    uniLoginUrl: "/pages_chat/login/index", // uni登录页面
    // 自定义的
    // headerTokenName:"rh-user-token",
    // headerTokenPrefix:"", //
    // apiErrorNeedLoginCode: -10001,  // 当请求拿到的code为-10001时，表示token失效，需要重新登录
    // fileUploadPath:"/upload/post-file", // 文件上传路径
    // fileUploadField:"upfile", // 文件上传字段名


    tokenName: "assistantUniToken",  // 存储在localstroage中的token的key
    toLogin: _goLogin, // 跳转到登录页面
    authKey: "User-Origin", // 鉴权key

    apiTimeout: 16000,  // 16秒

})

import './index.scss'; // 引入scss

import cryptoUtil from './cryptoUtil.js'
console.log("🚀 -> index.js -> cryptoUtil:", cryptoUtil)
Vue.prototype.$cryptoUtil = cryptoUtil


Vue.prototype.$withErrorCode = ['401','403','503','412','509'] // 不弹出错误提示的信息

// 自己封装的element方法  仅在使用element时调用
// +---------------------------------------------------
// import myElement from 'framework/script/element.js';
// Vue.prototype.$ele = myElement;
// // import api from 'framework/script/apiElement.js' // 加载自定义axios
// const _api = require('framework/script/apiElement.js');
// Vue.prototype.$tp = Vue.prototype.$ajax = _api.default // 此处命名为http,你可以改 加载自定义http组件库
// +---------------------------------------------------
const _api = require('@/framework/uni/api.js');
Vue.prototype.$tp = Vue.prototype.$api = Vue.prototype.$ajax = _api.default // api请求

Vue.prototype.$getRouter = Vue.prototype.$uto = MyRouter
import confirm from "@/framework/uni/dialog.js";
Vue.prototype.$dialog = confirm
import uniUtil from "@/framework/uni/main.js";
Vue.prototype.$uecs = uniUtil
import EasyPicker from "@/framework/uni/widget/picker.vue";
Vue.component("EasyPicker", EasyPicker)
import MyImage from '@/framework/widget/error/uni_image'
Vue.component('MyImage', MyImage)
// +---------------------------------------------------

Vue.prototype.$sysconfig = _cfg.Config
import vali from '@/framework/script/vali.js';
Vue.prototype.$vali = vali;
import ecs from '@/framework/script/ecs.js';
Vue.prototype.$ecs = ecs;

// 自定义的confirm
// 判断是否为h5
// #ifdef H5
import confirmWidget from "@/framework/widget/util/confirm";
Vue.prototype.$confirm1 = confirmWidget;
// 自定义的toast
import toast from '@/framework/widget/util/toast/toast.js';
Vue.prototype.$toast = toast.install;
// alert
import alertWidget from "@/framework/widget/util/alert";
Vue.prototype.$alert = alertWidget;
// #endif
// #ifdef MP-WEIXIN
import uniDialog from "@/framework/uni/dialog.js";
Vue.prototype.$confirm1 = uniDialog.confirm;
Vue.prototype.$toast = uniDialog.toast;
Vue.prototype.$alert = uniDialog.alert;
// #endif

// alert
import alertField from "@/framework/widget/util/dialog_input";
Vue.prototype.$alertField = alertField;
// 自定义cache
import localStorageCache from "@/framework/script/cache.js";
Vue.prototype.$cache = localStorageCache;

 // 自定义过滤器';
import * as filters from '@/framework/script/filter.js'
Object.keys(filters).forEach(key => Vue.filter(key, filters[key]));




// import { urlTobase64 } from './/urlTobase64'
// Vue.prototype.$urlTobase64 = urlTobase64
