# 个人中心分包 (pages_user)

## 📋 功能概述

个人中心分包提供了完整的用户管理功能，包括个人信息管理、应用设置、关于页面等核心功能。采用简约大气的设计风格，与整体应用保持一致。

## 🏗️ 页面结构

```
pages_user/
├── index/
│   └── index.vue          # 个人中心主页
├── profile/
│   └── index.vue          # 个人资料编辑
├── settings/
│   └── index.vue          # 应用设置
├── about/
│   └── index.vue          # 关于我们
├── styles/
│   └── common.scss        # 通用样式
└── README.md              # 说明文档
```

## 🎨 设计特色

### 视觉设计
- **简约大气**：采用白色背景，清晰的层次结构
- **一致性**：与其他分包保持统一的设计语言
- **现代化**：使用渐变背景、卡片设计、微妙阴影

### 交互体验
- **流畅动画**：页面切换和按钮点击都有平滑过渡
- **反馈及时**：操作后立即给出视觉反馈
- **易用性**：符合用户习惯的交互模式

## 📱 页面功能

### 1. 个人中心主页 (`index/index.vue`)

**核心功能：**
- 自定义导航栏设计
- 用户头像和基本信息展示
- 数据统计卡片（任务、记忆、记账数量）
- 功能菜单列表
- 退出登录按钮

**设计亮点：**
- 渐变色头部背景
- 浮动式用户信息卡片
- 图标化菜单项
- 底部退出按钮

### 2. 个人资料页面 (`profile/index.vue`)

**核心功能：**
- 头像上传和更换
- 基本信息编辑（昵称、签名、性别、生日）
- 联系方式管理（手机、邮箱）
- 表单验证和保存

**设计亮点：**
- 居中式头像编辑区域
- 分组表单布局
- 自定义日期选择器
- 性别选择器

### 3. 应用设置页面 (`settings/index.vue`)

**核心功能：**
- 通知设置（推送、提醒、声音）
- 隐私设置（数据同步、统计）
- 显示设置（主题、字体、动画）
- 数据管理（导出、导入、清理）

**设计亮点：**
- 分组设置列表
- 开关组件集成
- 底部弹窗选择器
- 危险操作警告

### 4. 关于我们页面 (`about/index.vue`)

**核心功能：**
- 应用信息展示
- 版本更新检查
- 功能介绍列表
- 开发团队介绍
- 联系方式和法律信息

**设计亮点：**
- 应用logo展示区域
- 功能特色卡片
- 团队成员介绍
- 版权信息

## 🎯 技术特点

### 样式架构
- 使用SCSS预处理器
- 统一的设计变量系统
- 响应式布局适配
- 组件化样式设计

### 数据管理
- 本地存储集成
- 数据持久化
- 状态同步
- 错误处理

### 用户体验
- 加载状态提示
- 操作反馈
- 错误提示
- 确认对话框

## 🔧 使用方式

### 路由跳转
```javascript
// 跳转到个人中心
uni.navigateTo({
  url: '/pages_user/index/index'
})

// 跳转到个人资料
uni.navigateTo({
  url: '/pages_user/profile/index'
})

// 跳转到设置页面
uni.navigateTo({
  url: '/pages_user/settings/index'
})

// 跳转到关于页面
uni.navigateTo({
  url: '/pages_user/about/index'
})
```

### 数据存储
```javascript
// 保存用户信息
uni.setStorageSync('userInfo', {
  nickname: '用户昵称',
  signature: '个性签名',
  avatar: '头像路径'
})

// 保存应用设置
uni.setStorageSync('appSettings', {
  pushNotification: true,
  theme: 'auto',
  fontSize: 'medium'
})
```

## 🎨 样式变量

主要设计变量定义在 `styles/common.scss` 中：

```scss
// 主题色彩
$primary-color: #2979ff;
$background-color: #ffffff;
$text-primary: #1a1a1a;

// 间距系统
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;

// 字体大小
$font-size-sm: 26rpx;
$font-size-md: 30rpx;
$font-size-lg: 34rpx;

// 圆角和阴影
$border-radius-lg: 16rpx;
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
```

## 🚀 扩展建议

1. **增加更多设置项**：语言切换、字体选择等
2. **完善数据同步**：云端备份和恢复功能
3. **增强安全性**：密码设置、指纹解锁等
4. **社交功能**：好友系统、分享功能等
5. **个性化定制**：主题色彩、布局选择等

## 📝 注意事项

1. 所有页面都已注册到 `pages.json` 中
2. 使用了 uview-ui 组件库的部分组件
3. 退出登录会清除本地存储并跳转到登录页
4. 图片资源需要放置在对应的静态资源目录中
5. 部分功能（如隐私政策）目前显示"功能开发中"

## 🎉 完成状态

- ✅ 页面结构搭建完成
- ✅ 基础功能实现完成
- ✅ 样式设计完成
- ✅ 路由配置完成
- ✅ 数据存储逻辑完成
- ⏳ 图片资源待补充
- ⏳ 部分功能待完善
