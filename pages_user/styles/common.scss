/**
 * @Description: 个人中心通用样式 - 简约大气设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 */

// 主题色彩变量 - 简约大气配色
$primary-color: #2979ff;
$primary-light: #e3f2fd;
$primary-dark: #1565c0;
$background-color: #ffffff;      // 白色背景
$card-background: #ffffff;
$text-primary: #1a1a1a;         // 更深的主文字色
$text-secondary: #666666;
$text-light: #999999;
$text-placeholder: #cccccc;
$border-color: #f0f0f0;         // 更淡的边框色
$divider-color: #f5f5f5;        // 分割线颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$purple-color: #722ed1;
$shadow-color: rgba(0, 0, 0, 0.04);  // 淡阴影
$white: #ffffff;

// 间距变量 - 精确控制
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-xxl: 48rpx;

// 字体大小
$font-size-xs: 22rpx;
$font-size-sm: 26rpx;
$font-size-md: 30rpx;
$font-size-lg: 34rpx;
$font-size-xl: 38rpx;
$font-size-xxl: 42rpx;

// 圆角
$border-radius-sm: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 20rpx;

// 阴影
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
$shadow-nav: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);

// 过渡动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 通用容器样式
.user-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: 0;
}

.user-page-container {
  background-color: $background-color;
  min-height: 100vh;
  padding: $spacing-md;
}

// 卡片样式
.user-card {
  background: $card-background;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-sm;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all $transition-normal;
  position: relative;
  overflow: hidden;
  margin-bottom: $spacing-md;

  &:active {
    transform: translateY(1rpx) scale(0.995);
    box-shadow: $shadow-md;
  }

  &.card-hover {
    &:hover {
      box-shadow: $shadow-md;
      transform: translateY(-2rpx);
    }
  }
}

// 头部区域样式
.user-header {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  padding: 0 $spacing-lg $spacing-xxl;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
  }

  .status-bar {
    height: var(--status-bar-height);
  }

  .nav-bar {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;

    .nav-title {
      color: $white;
      font-size: $font-size-xl;
      font-weight: 600;
    }

    .nav-action {
      color: $white;
      font-size: $font-size-lg;
      padding: $spacing-sm;
    }
  }
}

// 用户信息卡片
.user-info-card {
  background: $white;
  border-radius: $border-radius-xl;
  padding: $spacing-xl;
  margin: -60rpx $spacing-lg $spacing-lg;
  box-shadow: $shadow-md;
  position: relative;
  z-index: 3;

  .user-avatar-section {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-lg;

    .avatar-container {
      position: relative;
      margin-right: $spacing-lg;

      .user-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid $white;
        box-shadow: $shadow-sm;
      }

      .online-status {
        position: absolute;
        bottom: 8rpx;
        right: 8rpx;
        width: 24rpx;
        height: 24rpx;
        background: $success-color;
        border-radius: 50%;
        border: 3rpx solid $white;
      }
    }

    .user-basic-info {
      flex: 1;

      .user-name {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .user-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.4;
      }
    }

    .edit-btn {
      padding: $spacing-sm $spacing-md;
      background: $primary-light;
      color: $primary-color;
      border-radius: $border-radius-md;
      font-size: $font-size-sm;
    }
  }

  .user-stats {
    display: flex;
    justify-content: space-around;
    padding-top: $spacing-lg;
    border-top: 1rpx solid $divider-color;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .stat-label {
        font-size: $font-size-xs;
        color: $text-light;
      }
    }
  }
}

// 菜单列表样式
.menu-section {
  margin: 40rpx 30rpx;

  .section-title {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 20rpx;
    padding: 0 10rpx;
    font-weight: 500;
  }

  .menu-list {
    background: white;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .menu-item {
      display: flex;
      align-items: center;
      padding: 32rpx 30rpx;
      border-bottom: 1rpx solid #f5f5f5;
      transition: all 0.3s ease;
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #f8f9fa;
        transform: scale(0.98);
      }

      .menu-icon {
        width: 88rpx;
        height: 88rpx;
        margin-right: $spacing-lg;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 44rpx;

        &.icon-profile {
          background: #e3f2fd;
          color: #2196f3;
        }
        &.icon-settings {
          background: #e8f5e8;
          color: #4caf50;
        }
        &.icon-help {
          background: #fff3e0;
          color: #ff9800;
        }
        &.icon-about {
          background: #fce4ec;
          color: #e91e63;
        }
        &.icon-feedback {
          background: #f3e5f5;
          color: #9c27b0;
        }
      }

      .menu-content {
        flex: 1;

        .menu-title {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 8rpx;
          font-weight: 500;
        }

        .menu-desc {
          font-size: 26rpx;
          color: #999;
          line-height: 1.4;
        }
      }

      .menu-arrow {
        color: #ccc;
        font-size: 36rpx;
        margin-left: 20rpx;
        font-weight: 300;
      }

      .menu-badge {
        background: #ff4757;
        color: white;
        font-size: 20rpx;
        padding: 6rpx 12rpx;
        border-radius: 20rpx;
        margin-left: 20rpx;
        margin-right: 10rpx;
        font-weight: 500;
      }
    }
  }
}

// 退出登录按钮
.logout-section {
  margin: 60rpx 30rpx 40rpx;

  .logout-btn {
    width: 100%;
    background: white;
    color: #ff4757;
    border: 2rpx solid #ff4757;
    border-radius: 20rpx;
    padding: 28rpx;
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    line-height: 1.4;

    &:active {
      background: #ff4757;
      color: white;
      transform: scale(0.98);
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .user-info-card {
    margin: -40rpx $spacing-md $spacing-md;
    padding: $spacing-lg;

    .user-avatar-section {
      .avatar-container .user-avatar {
        width: 100rpx;
        height: 100rpx;
      }
    }
  }

  .menu-section {
    margin: $spacing-md;
  }
}
