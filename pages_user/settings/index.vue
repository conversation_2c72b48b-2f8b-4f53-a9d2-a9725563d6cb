<!--
 * @Description: 设置页面 - 应用设置与偏好配置
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 分组设置列表，包含通知、隐私、数据管理等
-->
<template>
  <view class="user-page-container">
    <!-- 通知设置 -->
    <view class="user-card">
      <view class="card-title">通知设置</view>
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">推送通知</view>
            <view class="setting-desc">接收应用推送消息</view>
          </view>
          <u-switch
            v-model="settings.pushNotification"
            @change="onSettingChange('pushNotification', $event)"
            active-color="#2979ff"
          />
        </view>

        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">任务提醒</view>
            <view class="setting-desc">待办任务到期提醒</view>
          </view>
          <u-switch
            v-model="settings.taskReminder"
            @change="onSettingChange('taskReminder', $event)"
            active-color="#2979ff"
          />
        </view>

        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">声音提醒</view>
            <view class="setting-desc">播放提醒音效</view>
          </view>
          <u-switch
            v-model="settings.soundAlert"
            @change="onSettingChange('soundAlert', $event)"
            active-color="#2979ff"
          />
        </view>

        <view class="setting-item" @click="goToNotificationTime">
          <view class="setting-content">
            <view class="setting-title">免打扰时间</view>
            <view class="setting-desc">{{ settings.quietHours || '未设置' }}</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 隐私设置 -->
    <view class="user-card">
      <view class="card-title">隐私设置</view>
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">数据同步</view>
            <view class="setting-desc">自动同步数据到云端</view>
          </view>
          <u-switch
            v-model="settings.dataSync"
            @change="onSettingChange('dataSync', $event)"
            active-color="#2979ff"
          />
        </view>

        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">使用统计</view>
            <view class="setting-desc">帮助改进应用体验</view>
          </view>
          <u-switch
            v-model="settings.analytics"
            @change="onSettingChange('analytics', $event)"
            active-color="#2979ff"
          />
        </view>

        <view class="setting-item" @click="goToPrivacyPolicy">
          <view class="setting-content">
            <view class="setting-title">隐私政策</view>
            <view class="setting-desc">查看隐私保护条款</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 显示设置 -->
    <view class="user-card">
      <view class="card-title">显示设置</view>
      <view class="settings-list">
        <view class="setting-item" @click="showThemeSelector">
          <view class="setting-content">
            <view class="setting-title">主题模式</view>
            <view class="setting-desc">{{ getThemeText(settings.theme) }}</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>

        <view class="setting-item" @click="showFontSizeSelector">
          <view class="setting-content">
            <view class="setting-title">字体大小</view>
            <view class="setting-desc">{{ getFontSizeText(settings.fontSize) }}</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>

        <view class="setting-item">
          <view class="setting-content">
            <view class="setting-title">动画效果</view>
            <view class="setting-desc">界面过渡动画</view>
          </view>
          <u-switch
            v-model="settings.animation"
            @change="onSettingChange('animation', $event)"
            active-color="#2979ff"
          />
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="user-card">
      <view class="card-title">数据管理</view>
      <view class="settings-list">
        <view class="setting-item" @click="exportData">
          <view class="setting-content">
            <view class="setting-title">导出数据</view>
            <view class="setting-desc">备份所有个人数据</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>

        <view class="setting-item" @click="importData">
          <view class="setting-content">
            <view class="setting-title">导入数据</view>
            <view class="setting-desc">从备份文件恢复数据</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>

        <view class="setting-item" @click="clearCache">
          <view class="setting-content">
            <view class="setting-title">清理缓存</view>
            <view class="setting-desc">释放存储空间</view>
          </view>
          <view class="setting-value">{{ cacheSize }}</view>
        </view>

        <view class="setting-item danger" @click="clearAllData">
          <view class="setting-content">
            <view class="setting-title">清空所有数据</view>
            <view class="setting-desc">不可恢复，请谨慎操作</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 主题选择弹窗 -->
    <u-popup v-model="showThemePopup" mode="bottom" border-radius="20">
      <view class="selector-popup">
        <view class="popup-header">
          <view class="popup-title">选择主题</view>
          <view class="popup-close" @click="showThemePopup = false">✕</view>
        </view>
        <view class="selector-list">
          <view
            class="selector-item"
            :class="{ active: settings.theme === 'auto' }"
            @click="selectTheme('auto')"
          >
            <text class="selector-icon">🌓</text>
            <text class="selector-text">跟随系统</text>
            <text class="selector-check" v-if="settings.theme === 'auto'">✓</text>
          </view>
          <view
            class="selector-item"
            :class="{ active: settings.theme === 'light' }"
            @click="selectTheme('light')"
          >
            <text class="selector-icon">☀️</text>
            <text class="selector-text">浅色模式</text>
            <text class="selector-check" v-if="settings.theme === 'light'">✓</text>
          </view>
          <view
            class="selector-item"
            :class="{ active: settings.theme === 'dark' }"
            @click="selectTheme('dark')"
          >
            <text class="selector-icon">🌙</text>
            <text class="selector-text">深色模式</text>
            <text class="selector-check" v-if="settings.theme === 'dark'">✓</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 字体大小选择弹窗 -->
    <u-popup v-model="showFontSizePopup" mode="bottom" border-radius="20">
      <view class="selector-popup">
        <view class="popup-header">
          <view class="popup-title">字体大小</view>
          <view class="popup-close" @click="showFontSizePopup = false">✕</view>
        </view>
        <view class="selector-list">
          <view
            class="selector-item"
            :class="{ active: settings.fontSize === 'small' }"
            @click="selectFontSize('small')"
          >
            <text class="selector-text small-font">小号字体</text>
            <text class="selector-check" v-if="settings.fontSize === 'small'">✓</text>
          </view>
          <view
            class="selector-item"
            :class="{ active: settings.fontSize === 'medium' }"
            @click="selectFontSize('medium')"
          >
            <text class="selector-text medium-font">标准字体</text>
            <text class="selector-check" v-if="settings.fontSize === 'medium'">✓</text>
          </view>
          <view
            class="selector-item"
            :class="{ active: settings.fontSize === 'large' }"
            @click="selectFontSize('large')"
          >
            <text class="selector-text large-font">大号字体</text>
            <text class="selector-check" v-if="settings.fontSize === 'large'">✓</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'UserSettings',
  data() {
    return {
      showThemePopup: false,
      showFontSizePopup: false,
      cacheSize: '0 MB',
      settings: {
        pushNotification: true,
        taskReminder: true,
        soundAlert: true,
        quietHours: '',
        dataSync: true,
        analytics: false,
        theme: 'auto',
        fontSize: 'medium',
        animation: true
      }
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.loadSettings()
      this.calculateCacheSize()
    },

    /**
     * 加载设置
     */
    loadSettings() {
      try {
        const settings = uni.getStorageSync('appSettings')
        if (settings) {
          this.settings = { ...this.settings, ...settings }
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    /**
     * 保存设置
     */
    saveSettings() {
      try {
        uni.setStorageSync('appSettings', this.settings)
      } catch (error) {
        console.error('保存设置失败:', error)
      }
    },

    /**
     * 设置项变化处理
     */
    onSettingChange(key, value) {
      this.settings[key] = value
      this.saveSettings()

      // 特殊处理某些设置
      if (key === 'theme') {
        this.applyTheme(value)
      }
    },

    /**
     * 显示主题选择器
     */
    showThemeSelector() {
      this.showThemePopup = true
    },

    /**
     * 选择主题
     */
    selectTheme(theme) {
      this.onSettingChange('theme', theme)
      this.showThemePopup = false
    },

    /**
     * 显示字体大小选择器
     */
    showFontSizeSelector() {
      this.showFontSizePopup = true
    },

    /**
     * 选择字体大小
     */
    selectFontSize(fontSize) {
      this.onSettingChange('fontSize', fontSize)
      this.showFontSizePopup = false
    },

    /**
     * 获取主题文本
     */
    getThemeText(theme) {
      const themeMap = {
        auto: '跟随系统',
        light: '浅色模式',
        dark: '深色模式'
      }
      return themeMap[theme] || '跟随系统'
    },

    /**
     * 获取字体大小文本
     */
    getFontSizeText(fontSize) {
      const fontSizeMap = {
        small: '小号',
        medium: '标准',
        large: '大号'
      }
      return fontSizeMap[fontSize] || '标准'
    },

    /**
     * 应用主题
     */
    applyTheme(theme) {
      // 这里可以实现主题切换逻辑
      console.log('应用主题:', theme)
    },

    /**
     * 计算缓存大小
     */
    calculateCacheSize() {
      // 模拟计算缓存大小
      const size = Math.floor(Math.random() * 50) + 10
      this.cacheSize = `${size} MB`
    },

    /**
     * 跳转到免打扰时间设置
     */
    goToNotificationTime() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 跳转到隐私政策
     */
    goToPrivacyPolicy() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 导出数据
     */
    exportData() {
      uni.showLoading({
        title: '导出中...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      }, 2000)
    },

    /**
     * 导入数据
     */
    importData() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 清理缓存
     */
    clearCache() {
      uni.showModal({
        title: '提示',
        content: '确定要清理缓存吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '清理中...'
            })

            setTimeout(() => {
              uni.hideLoading()
              this.cacheSize = '0 MB'
              uni.showToast({
                title: '清理完成',
                icon: 'success'
              })
            }, 1500)
          }
        }
      })
    },

    /**
     * 清空所有数据
     */
    clearAllData() {
      uni.showModal({
        title: '危险操作',
        content: '此操作将清空所有数据且不可恢复，确定继续吗？',
        confirmText: '确定清空',
        cancelText: '取消',
        confirmColor: '#ff4d4f',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '清理中...'
            })

            setTimeout(() => {
              uni.hideLoading()
              uni.showToast({
                title: '数据已清空',
                icon: 'success'
              })
            }, 2000)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.card-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: $spacing-lg;
}

.settings-list {
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;
    transition: background-color $transition-fast;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &.danger {
      .setting-title {
        color: $error-color;
      }
    }

    .setting-content {
      flex: 1;

      .setting-title {
        font-size: $font-size-md;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .setting-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }

    .setting-arrow {
      color: $text-light;
      font-size: $font-size-lg;
    }

    .setting-value {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-right: $spacing-sm;
    }
  }
}

.selector-popup {
  background: $white;
  border-radius: 20rpx 20rpx 0 0;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .popup-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }

    .popup-close {
      font-size: $font-size-lg;
      color: $text-light;
      padding: $spacing-sm;
    }
  }

  .selector-list {
    .selector-item {
      display: flex;
      align-items: center;
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;
      transition: background-color $transition-fast;

      &:last-child {
        border-bottom: none;
      }

      &.active {
        background-color: $primary-light;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.02);
      }

      .selector-icon {
        font-size: $font-size-lg;
        margin-right: $spacing-md;
      }

      .selector-text {
        flex: 1;
        font-size: $font-size-md;
        color: $text-primary;

        &.small-font {
          font-size: $font-size-sm;
        }

        &.medium-font {
          font-size: $font-size-md;
        }

        &.large-font {
          font-size: $font-size-lg;
        }
      }

      .selector-check {
        font-size: $font-size-lg;
        color: $primary-color;
      }
    }
  }
}
</style>
