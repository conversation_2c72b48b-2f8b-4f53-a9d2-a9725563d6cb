<!--
 * @Description: 个人资料页面 - 简洁版用户信息编辑
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 简洁表单布局，仅包含头像、昵称、性别、生日
-->
<template>
  <view class="profile-container">
    <!-- 头像设置 -->
    <view class="avatar-section">
      <view class="section-title">头像设置</view>
      <view class="avatar-wrapper" @click="chooseAvatar">
        <image
          class="avatar-image"
          :src="userInfo.avatar || defaultAvatar"
          mode="aspectFill"
        />
        <view class="avatar-mask" :class="{ uploading: avatarUploading }">
          <text v-if="!avatarUploading" class="camera-icon">📷</text>
          <u-loading-icon v-if="avatarUploading" size="24" color="#ffffff"></u-loading-icon>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>

      <!-- 昵称 -->
      <view class="info-item">
        <view class="item-label">昵称</view>
        <input
          class="item-input"
          v-model="userInfo.user_nick"
          placeholder="请输入昵称"
          maxlength="20"
        />
      </view>

      <!-- 性别 -->
      <view class="info-item">
        <view class="item-label">性别</view>
        <view class="gender-options">
          <view
            class="gender-item"
            :class="{ selected: userInfo.sex === 1 }"
            @click="selectGender(1)"
          >
            <text class="gender-emoji">👨</text>
            <text class="gender-label">男</text>
          </view>
          <view
            class="gender-item"
            :class="{ selected: userInfo.sex === 0 }"
            @click="selectGender(0)"
          >
            <text class="gender-emoji">👩</text>
            <text class="gender-label">女</text>
          </view>
        </view>
      </view>

      <!-- 生日 -->
      <view class="info-item">
        <view class="item-label">生日</view>
        <view class="date-selector" @click="openDatePicker">
          <text class="date-value">{{ userInfo.birthday || '请选择生日' }}</text>
          <text class="arrow-right">›</text>
        </view>
      </view>
    </view>

    <!-- 固定底部保存按钮 -->
    <view class="fixed-bottom">
      <button class="save-button" @click="saveProfile" :disabled="saving">
        {{ saving ? '保存中...' : '保存' }}
      </button>
    </view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      v-model="dateValue"
      mode="date"
      :max-date="maxDate"
      :min-date="minDate"
      title="选择生日"
      @confirm="onDateConfirm"
      @cancel="onDateCancel"
      @close="onDateCancel"
    ></u-datetime-picker>
  </view>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      saving: false,
      defaultAvatar: '/static/placeholder.png',
      showDatePicker: false,
      dateValue: '',
      maxDate: '',
      minDate: '',
      avatarUploading: false, // 头像上传状态
      userInfo: {
        user_nick: '',
        avatar: '',
        sex: '', // 1=男, 0=女
        birthday: ''
      }
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.loadUserInfo()
      this.initDateRange()
    },

    /**
     * 加载用户信息
     */
    loadUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = { ...this.userInfo, ...userInfo }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 初始化日期范围（今天往前推80年）
     */
    initDateRange() {
      const today = new Date()

      // 最大日期：今天（时间戳毫秒）
      this.maxDate = today.getTime()

      // 最小日期：80年前（时间戳毫秒）
      const minDate = new Date()
      minDate.setFullYear(today.getFullYear() - 80)
      this.minDate = minDate.getTime()

      // 如果没有生日数据，设置默认值为30年前
      if (!this.userInfo.birthday) {
        const defaultDate = new Date()
        defaultDate.setFullYear(today.getFullYear() - 30)
        this.dateValue = defaultDate.getTime()
      } else {
        // 将生日字符串转换为时间戳
        this.dateValue = new Date(this.userInfo.birthday).getTime()
      }
    },

    /**
     * 选择头像
     */
    chooseAvatar() {
      if (this.avatarUploading) return

      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.handleAvatarChoose(res.tempFilePaths[0])
        },
        fail: (error) => {
          console.error('选择头像失败:', error)
          uni.showToast({
            title: '选择头像失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 处理头像选择结果
     * @param {String} tempPath 临时文件路径
     */
    async handleAvatarChoose(tempPath) {
      try {
        // 检查文件大小
        const fileInfo = await this.getFileInfo(tempPath)
        if (fileInfo.size > 10 * 1024 * 1024) { // 10MB限制
          uni.showToast({
            title: '图片大小不能超过10MB',
            icon: 'none'
          })
          return
        }

        this.avatarUploading = true
        uni.showLoading({ title: '处理中...' })

        // 处理图片（压缩/转换格式）
        const processedPath = await this.processAvatar(tempPath, fileInfo.size)

        // 上传图片
        const uploadedUrl = await this.uploadAvatar(processedPath)

        // 更新头像
        this.userInfo.avatar = uploadedUrl

        uni.hideLoading()
        uni.showToast({
          title: '头像更新成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('头像处理失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '头像更新失败',
          icon: 'error'
        })
      } finally {
        this.avatarUploading = false
      }
    },

    /**
     * 获取文件信息
     * @param {String} filePath 文件路径
     * @returns {Promise} 文件信息
     */
    getFileInfo(filePath) {
      return new Promise((resolve, reject) => {
        uni.getFileInfo({
          filePath: filePath,
          success: resolve,
          fail: reject
        })
      })
    },

    /**
     * 处理头像（压缩和格式转换）
     * @param {String} imagePath 图片路径
     * @param {Number} originalSize 原始文件大小
     * @returns {Promise} 处理后的图片路径
     */
    async processAvatar(imagePath, originalSize) {
      try {
        // 获取图片信息
        const imageInfo = await this.getImageInfo(imagePath)
        console.log('头像图片信息:', imageInfo)

        const needCompress = originalSize > 1024 * 1024 || imageInfo.width > 800

        if (needCompress) {
          // 需要压缩：计算压缩参数（头像建议800px以内）
          const compressOptions = this.calculateAvatarCompressOptions(imageInfo, originalSize)
          console.log('头像压缩参数:', compressOptions)

          // 执行压缩
          const compressedPath = await this.compressImage(imagePath, compressOptions)
          return compressedPath
        } else {
          // 不需要压缩但转换为JPG格式
          const convertedPath = await this.convertToJpg(imagePath)
          return convertedPath
        }
      } catch (error) {
        console.error('处理头像失败:', error)
        throw error
      }
    },

    /**
     * 获取图片信息
     * @param {String} src 图片路径
     * @returns {Promise} 图片信息
     */
    getImageInfo(src) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: src,
          success: resolve,
          fail: reject
        })
      })
    },

    /**
     * 计算头像压缩参数
     * @param {Object} imageInfo 图片信息
     * @param {Number} originalSize 原始文件大小
     * @returns {Object} 压缩参数
     */
    calculateAvatarCompressOptions(imageInfo, originalSize) {
      const { width, height } = imageInfo
      let targetWidth = width
      let targetHeight = height
      let quality = 0.8

      // 头像建议最大800px
      if (width > 800 || height > 800) {
        if (width > height) {
          targetWidth = 800
          targetHeight = Math.round((height * 800) / width)
        } else {
          targetHeight = 800
          targetWidth = Math.round((width * 800) / height)
        }
      }

      // 根据文件大小调整质量
      if (originalSize > 1024 * 1024) {
        if (originalSize > 5 * 1024 * 1024) {
          quality = 0.6
        } else if (originalSize > 2 * 1024 * 1024) {
          quality = 0.7
        } else {
          quality = 0.8
        }
      }

      return {
        src: imageInfo.path,
        width: targetWidth,
        height: targetHeight,
        quality: quality
      }
    },

    /**
     * 压缩图片
     * @param {String} src 原图路径
     * @param {Object} options 压缩选项
     * @returns {Promise} 压缩后的图片路径
     */
    compressImage(src, options) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        this.compressImageH5(src, options).then(resolve).catch(reject)
        // #endif

        // #ifndef H5
        // 小程序环境使用uni.compressImage
        uni.compressImage({
          src: src,
          quality: Math.round(options.quality * 100),
          width: options.width,
          height: options.height,
          success: (res) => {
            console.log('头像压缩成功:', res.tempFilePath)
            resolve(res.tempFilePath)
          },
          fail: (error) => {
            console.error('头像压缩失败:', error)
            reject(error)
          }
        })
        // #endif
      })
    },

    /**
     * H5环境下使用Canvas压缩图片
     * @param {String} src 原图路径
     * @param {Object} options 压缩选项
     * @returns {Promise} 压缩后的图片DataURL
     */
    compressImageH5(src, options) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'

        img.onload = () => {
          try {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            canvas.width = options.width
            canvas.height = options.height

            // 绘制压缩后的图片
            ctx.drawImage(img, 0, 0, options.width, options.height)

            // 转换为JPG格式的DataURL
            const compressedDataURL = canvas.toDataURL('image/jpeg', options.quality)
            resolve(compressedDataURL)
          } catch (error) {
            console.error('Canvas压缩失败:', error)
            reject(error)
          }
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }

        img.src = src
      })
    },

    /**
     * 转换图片为JPG格式（不压缩尺寸）
     * @param {String} src 原图路径
     * @returns {Promise} 转换后的图片路径
     */
    convertToJpg(src) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: src,
          success: (imageInfo) => {
            // #ifdef H5
            this.compressImageH5(src, {
              width: imageInfo.width,
              height: imageInfo.height,
              quality: 0.9
            }).then(resolve).catch(reject)
            // #endif

            // #ifndef H5
            uni.compressImage({
              src: src,
              quality: 90,
              width: imageInfo.width,
              height: imageInfo.height,
              success: (res) => {
                resolve(res.tempFilePath)
              },
              fail: reject
            })
            // #endif
          },
          fail: reject
        })
      })
    },

    /**
     * 上传头像
     * @param {String} filePath 文件路径
     * @returns {Promise} 上传后的URL
     */
    uploadAvatar(filePath) {
      return new Promise((resolve, reject) => {
        const uploadUrl = "http://files.qcloud.rhecs.com/ueditor/php/controller.php?action=uploadimage&encode=utf-8"

        // 构建请求头
        const headers = {}
        const token = uni.getStorageSync('token') || uni.getStorageSync('userToken')
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }

        console.log('开始上传头像:', {
          url: uploadUrl,
          filePath: filePath,
          name: 'upfile'
        })

        // #ifdef H5
        // H5环境下，如果是DataURL需要特殊处理
        if (filePath.startsWith('data:')) {
          this.uploadAvatarH5(filePath, headers).then(resolve).catch(reject)
          return
        }
        // #endif

        // 使用uni.uploadFile上传
        uni.uploadFile({
          url: uploadUrl,
          filePath: filePath,
          name: 'upfile',
          header: headers,
          success: (res) => {
            this.handleUploadSuccess(res, resolve, reject)
          },
          fail: (error) => {
            console.error('头像上传失败:', error)
            reject(error)
          }
        })
      })
    },

    /**
     * H5环境下上传DataURL格式的头像
     * @param {String} dataURL DataURL字符串
     * @param {Object} headers 请求头
     * @returns {Promise} 上传结果
     */
    uploadAvatarH5(dataURL, headers) {
      return new Promise((resolve, reject) => {
        try {
          // 将DataURL转换为Blob
          const blob = this.dataURLToBlob(dataURL)

          // 创建FormData
          const formData = new FormData()
          formData.append('upfile', blob, 'avatar.jpg')

          // 使用fetch上传
          fetch("http://files.qcloud.rhecs.com/ueditor/php/controller.php?action=uploadimage&encode=utf-8", {
            method: 'POST',
            headers: headers,
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            this.handleUploadSuccess({ data: JSON.stringify(data) }, resolve, reject)
          })
          .catch(error => {
            console.error('H5头像上传失败:', error)
            reject(error)
          })
        } catch (error) {
          reject(error)
        }
      })
    },

    /**
     * 将DataURL转换为Blob
     * @param {String} dataURL DataURL字符串
     * @returns {Blob} Blob对象
     */
    dataURLToBlob(dataURL) {
      const arr = dataURL.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },

    /**
     * 处理上传成功
     * @param {Object} res 响应结果
     * @param {Function} resolve Promise resolve
     * @param {Function} reject Promise reject
     */
    handleUploadSuccess(res, resolve, reject) {
      try {
        const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
        console.log('头像上传响应:', data)

        if (data.state === 'SUCCESS' && data.url) {
          resolve(data.url)
        } else {
          reject(new Error(data.state || '上传失败'))
        }
      } catch (error) {
        console.error('解析上传结果失败:', error)
        reject(error)
      }
    },

    /**
     * 选择性别
     */
    selectGender(sex) {
      this.userInfo.sex = sex
    },

    /**
     * 打开日期选择器
     */
    openDatePicker() {
      this.showDatePicker = true
    },

    /**
     * 确认日期选择
     */
    onDateConfirm(e) {
      // e.value 是时间戳，需要转换为日期字符串
      const date = new Date(e.value)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')

      this.userInfo.birthday = `${year}-${month}-${day}`
      this.dateValue = e.value
      this.showDatePicker = false
    },

    /**
     * 取消日期选择
     */
    onDateCancel() {
      this.showDatePicker = false
    },

    /**
     * 保存个人资料
     */
    async saveProfile() {
      if (this.saving) return

      // 基本验证
      if (!this.userInfo.user_nick.trim()) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      this.saving = true

      try {
        // 保存到本地存储
        uni.setStorageSync('userInfo', this.userInfo)

        // 模拟网络请求延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx; /* 为固定底部按钮留出空间 */
}

.avatar-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 40rpx;
  }

  .avatar-wrapper {
    display: flex;
    justify-content: center;
    position: relative;

    .avatar-image {
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      border: 6rpx solid #f0f0f0;
    }

    .avatar-mask {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .camera-icon {
        font-size: 48rpx;
        color: white;
      }

      &.uploading {
        opacity: 1;
        background: rgba(0, 123, 255, 0.6);
      }
    }

    &:active .avatar-mask {
      opacity: 1;
    }
  }
}

.info-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 40rpx;
  }
}

.info-item {
  margin-bottom: 40rpx;

  .item-label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
  }

  .item-input {
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #333;
    background: #fafafa;
    transition: all 0.3s ease;

    &:focus {
      border-color: #07c160;
      background: white;
    }
  }

  .gender-options {
    display: flex;
    gap: 20rpx;

    .gender-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx 16rpx;
      border: 2rpx solid #e8e8e8;
      border-radius: 12rpx;
      background: #fafafa;
      transition: all 0.3s ease;

      &.selected {
        border-color: #07c160;
        background: #f0f9f4;
      }

      .gender-emoji {
        font-size: 40rpx;
        margin-bottom: 8rpx;
      }

      .gender-label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .date-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 24rpx;
    border: 2rpx solid #e8e8e8;
    border-radius: 12rpx;
    background: #fafafa;
    transition: all 0.3s ease;

    &:active {
      background: white;
      border-color: #07c160;
    }

    .date-value {
      font-size: 28rpx;
      color: #333;
    }

    .arrow-right {
      font-size: 32rpx;
      color: #999;
      font-weight: bold;
    }
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .save-button {
    width: 100%;
    height: 88rpx;
    background: #07c160;
    color: white;
    border: none;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &:disabled {
      background: #ccc;
      opacity: 0.6;
    }

    &:not(:disabled):active {
      background: #06ad56;
      transform: scale(0.98);
    }
  }
}


</style>
