<!--
 * @Description: 关于我们页面 - 简洁的应用介绍
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 简单的应用介绍，白色背景
-->
<template>
  <view class="about-page">
    <!-- 应用介绍 -->
    <view class="app-intro">
      <image class="app-logo" src="/static/placeholder.png" mode="aspectFit" />
      <view class="app-name">小助理</view>
      <view class="app-description">
        一款简洁实用的生活助手应用，帮助您更好地管理日常生活。
      </view>
    </view>

    <!-- 主要功能 -->
    <view class="features-section">
      <view class="section-title">主要功能</view>
      <view class="feature-item" v-for="(feature, index) in features" :key="index">
        <view class="feature-icon">{{ feature.icon }}</view>
        <view class="feature-text">{{ feature.name }}</view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="section-title">联系我们</view>
      <view class="contact-info">
        <text>如有问题或建议，欢迎联系我们</text>
        <text class="contact-email"><EMAIL></text>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright">
      <text>© 2025 小助理</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserAbout',
  data() {
    return {
      features: [
        {
          icon: '✅',
          name: '待办任务'
        },
        {
          icon: '🧠',
          name: '记忆管理'
        },
        {
          icon: '💰',
          name: '记账理财'
        },
        {
          icon: '💬',
          name: 'AI对话'
        },
        {
          icon: '📚',
          name: '知识库'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 40rpx 30rpx;
}

.app-intro {
  text-align: center;
  padding: 60rpx 0 80rpx;

  .app-logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
    border-radius: 20rpx;
  }

  .app-name {
    font-size: 48rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .app-description {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.6;
    padding: 0 20rpx;
  }
}

.features-section {
  margin-bottom: 60rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
  }

  .feature-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .feature-icon {
      font-size: 32rpx;
      margin-right: 20rpx;
    }

    .feature-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

.contact-section {
  margin-bottom: 60rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
  }

  .contact-info {
    text-align: center;

    text {
      display: block;
      font-size: 26rpx;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 10rpx;
    }

    .contact-email {
      color: #007AFF;
      font-weight: 500;
    }
  }
}

.copyright {
  text-align: center;
  padding-top: 40rpx;
  border-top: 1rpx solid #f0f0f0;

  text {
    font-size: 24rpx;
    color: #999999;
  }
}
</style>
