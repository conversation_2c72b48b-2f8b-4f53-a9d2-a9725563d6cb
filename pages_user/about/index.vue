<!--
 * @Description: 关于我们页面 - 应用信息与团队介绍
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 应用logo + 版本信息 + 功能介绍 + 团队信息
-->
<template>
  <view class="user-page-container">
    <!-- 应用信息卡片 -->
    <view class="user-card app-info-card">
      <view class="app-logo-section">
        <image class="app-logo" src="/static/placeholder.png" mode="aspectFit" />
        <view class="app-name">小助理</view>
        <view class="app-slogan">让生活更有条理，让记忆更珍贵</view>
      </view>

      <view class="version-info">
        <view class="version-item">
          <text class="version-label">当前版本</text>
          <text class="version-value">{{ appInfo.version }}</text>
        </view>
        <view class="version-item">
          <text class="version-label">构建时间</text>
          <text class="version-value">{{ appInfo.buildTime }}</text>
        </view>
        <view class="version-item" v-if="appInfo.hasUpdate">
          <text class="version-label">最新版本</text>
          <text class="version-value update-available">{{ appInfo.latestVersion }}</text>
        </view>
      </view>

      <view class="update-section" v-if="appInfo.hasUpdate">
        <button class="update-btn" @click="checkUpdate">
          <text>检查更新</text>
        </button>
      </view>
    </view>

    <!-- 功能介绍 -->
    <view class="user-card">
      <view class="card-title">核心功能</view>
      <view class="feature-list">
        <view class="feature-item" v-for="(feature, index) in features" :key="index">
          <view class="feature-icon">{{ feature.icon }}</view>
          <view class="feature-content">
            <view class="feature-name">{{ feature.name }}</view>
            <view class="feature-desc">{{ feature.desc }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 开发团队 -->
    <view class="user-card">
      <view class="card-title">开发团队</view>
      <view class="team-section">
        <view class="team-member" v-for="(member, index) in teamMembers" :key="index">
          <image class="member-avatar" :src="member.avatar" mode="aspectFill" />
          <view class="member-info">
            <view class="member-name">{{ member.name }}</view>
            <view class="member-role">{{ member.role }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="user-card">
      <view class="card-title">联系我们</view>
      <view class="contact-list">
        <view class="contact-item" @click="copyContact('email')">
          <view class="contact-icon">📧</view>
          <view class="contact-content">
            <view class="contact-label">邮箱</view>
            <view class="contact-value"><EMAIL></view>
          </view>
          <view class="contact-action">复制</view>
        </view>

        <view class="contact-item" @click="copyContact('qq')">
          <view class="contact-icon">💬</view>
          <view class="contact-content">
            <view class="contact-label">QQ群</view>
            <view class="contact-value">123456789</view>
          </view>
          <view class="contact-action">复制</view>
        </view>

        <view class="contact-item" @click="copyContact('wechat')">
          <view class="contact-icon">💚</view>
          <view class="contact-content">
            <view class="contact-label">微信群</view>
            <view class="contact-value">扫码加入</view>
          </view>
          <view class="contact-action">›</view>
        </view>
      </view>
    </view>

    <!-- 法律信息 -->
    <view class="user-card">
      <view class="card-title">法律信息</view>
      <view class="legal-list">
        <view class="legal-item" @click="goToPrivacyPolicy">
          <text class="legal-text">隐私政策</text>
          <view class="legal-arrow">›</view>
        </view>
        <view class="legal-item" @click="goToTermsOfService">
          <text class="legal-text">服务条款</text>
          <view class="legal-arrow">›</view>
        </view>
        <view class="legal-item" @click="goToOpenSource">
          <text class="legal-text">开源许可</text>
          <view class="legal-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright-section">
      <text class="copyright-text">© 2025 小助理团队</text>
      <text class="copyright-text">All rights reserved</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserAbout',
  data() {
    return {
      appInfo: {
        version: '1.0.0',
        buildTime: '2025-08-01',
        hasUpdate: false,
        latestVersion: '1.0.1'
      },
      features: [
        {
          icon: '✅',
          name: '待办任务',
          desc: '高效管理日常任务，提升工作效率'
        },
        {
          icon: '🧠',
          name: '记忆管理',
          desc: '记录重要信息，构建个人知识库'
        },
        {
          icon: '💰',
          name: '记账理财',
          desc: '轻松记录收支，掌控财务状况'
        },
        {
          icon: '💬',
          name: 'AI对话',
          desc: '智能助手陪伴，解答疑问困惑'
        },
        {
          icon: '📚',
          name: '知识库',
          desc: '整理学习资料，积累专业知识'
        }
      ],
      teamMembers: [
        {
          name: '徐静',
          role: '产品经理 & 全栈开发',
          avatar: '/static/placeholder.png'
        },
        {
          name: '小助理',
          role: 'AI助手 & 用户体验',
          avatar: '/static/placeholder.png'
        }
      ]
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.checkAppUpdate()
    },

    /**
     * 检查应用更新
     */
    checkAppUpdate() {
      // 模拟检查更新
      setTimeout(() => {
        this.appInfo.hasUpdate = Math.random() > 0.7 // 30%概率有更新
      }, 1000)
    },

    /**
     * 检查更新
     */
    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })

      setTimeout(() => {
        uni.hideLoading()
        if (this.appInfo.hasUpdate) {
          uni.showModal({
            title: '版本更新',
            content: '发现新版本，是否立即更新？',
            confirmText: '立即更新',
            cancelText: '稍后再说',
            success: (res) => {
              if (res.confirm) {
                this.downloadUpdate()
              }
            }
          })
        } else {
          uni.showToast({
            title: '已是最新版本',
            icon: 'success'
          })
        }
      }, 2000)
    },

    /**
     * 下载更新
     */
    downloadUpdate() {
      uni.showLoading({
        title: '下载中...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '下载完成',
          icon: 'success'
        })
      }, 3000)
    },

    /**
     * 复制联系方式
     */
    copyContact(type) {
      const contactMap = {
        email: '<EMAIL>',
        qq: '123456789',
        wechat: '微信群二维码'
      }

      const text = contactMap[type]
      if (text) {
        uni.setClipboardData({
          data: text,
          success: () => {
            uni.showToast({
              title: '已复制到剪贴板',
              icon: 'success'
            })
          }
        })
      }
    },

    /**
     * 跳转到隐私政策
     */
    goToPrivacyPolicy() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 跳转到服务条款
     */
    goToTermsOfService() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 跳转到开源许可
     */
    goToOpenSource() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.card-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: $spacing-lg;
}

.app-info-card {
  text-align: center;

  .app-logo-section {
    padding: $spacing-xl 0;

    .app-logo {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: $spacing-lg;
      border-radius: $border-radius-xl;
      box-shadow: $shadow-md;
    }

    .app-name {
      font-size: $font-size-xxl;
      font-weight: 700;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }

    .app-slogan {
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.5;
    }
  }

  .version-info {
    padding: $spacing-lg 0;
    border-top: 1rpx solid $divider-color;
    border-bottom: 1rpx solid $divider-color;

    .version-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;

      &:last-child {
        margin-bottom: 0;
      }

      .version-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }

      .version-value {
        font-size: $font-size-sm;
        color: $text-primary;

        &.update-available {
          color: $primary-color;
          font-weight: 600;
        }
      }
    }
  }

  .update-section {
    padding-top: $spacing-lg;

    .update-btn {
      background: $primary-color;
      color: $white;
      border: none;
      border-radius: $border-radius-md;
      padding: $spacing-md $spacing-xl;
      font-size: $font-size-sm;
    }
  }
}

.feature-list {
  .feature-item {
    display: flex;
    align-items: center;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;

    &:last-child {
      border-bottom: none;
    }

    .feature-icon {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: $font-size-xl;
      background: $primary-light;
      border-radius: $border-radius-md;
      margin-right: $spacing-lg;
    }

    .feature-content {
      flex: 1;

      .feature-name {
        font-size: $font-size-md;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .feature-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.4;
      }
    }
  }
}

.team-section {
  .team-member {
    display: flex;
    align-items: center;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;

    &:last-child {
      border-bottom: none;
    }

    .member-avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: $spacing-lg;
      border: 3rpx solid $divider-color;
    }

    .member-info {
      flex: 1;

      .member-name {
        font-size: $font-size-md;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .member-role {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.contact-list {
  .contact-item {
    display: flex;
    align-items: center;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;
    transition: background-color $transition-fast;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .contact-icon {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: $font-size-xl;
      background: $primary-light;
      border-radius: $border-radius-md;
      margin-right: $spacing-lg;
    }

    .contact-content {
      flex: 1;

      .contact-label {
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }

      .contact-value {
        font-size: $font-size-md;
        color: $text-primary;
      }
    }

    .contact-action {
      font-size: $font-size-sm;
      color: $primary-color;
    }
  }
}

.legal-list {
  .legal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;
    transition: background-color $transition-fast;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .legal-text {
      font-size: $font-size-md;
      color: $text-primary;
    }

    .legal-arrow {
      font-size: $font-size-lg;
      color: $text-light;
    }
  }
}

.copyright-section {
  text-align: center;
  padding: $spacing-xxl 0;
  margin-top: $spacing-xl;

  .copyright-text {
    display: block;
    font-size: $font-size-xs;
    color: $text-light;
    line-height: 1.6;
  }
}
</style>
