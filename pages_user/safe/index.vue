<!--
 * @Description: 安全设置主页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 安全设置选项列表
-->
<template>
  <view class="safe-page">

    <!-- 安全设置列表 -->
    <view class="safe-list">
      <view class="safe-item" @click="goToPassword">
        <view class="item-icon">🔐</view>
        <view class="item-content">
          <view class="item-title">修改密码</view>
          <view class="item-desc">定期修改密码，保护账户安全</view>
        </view>
        <view class="item-arrow">›</view>
      </view>

      <view class="safe-item" @click="goToMobile">
        <view class="item-icon">📱</view>
        <view class="item-content">
          <view class="item-title">绑定手机</view>
          <view class="item-desc">{{ mobileDesc }}</view>
        </view>
        <view class="item-arrow">›</view>
      </view>

      <view class="safe-item" @click="goToEmail">
        <view class="item-icon">📧</view>
        <view class="item-content">
          <view class="item-title">绑定邮箱</view>
          <view class="item-desc">{{ emailDesc }}</view>
        </view>
        <view class="item-arrow">›</view>
      </view>
    </view>

    <!-- 安全提示 -->
    <view class="security-tips">
      <view class="tips-title">安全提示</view>
      <view class="tips-content">
        <view class="tip-item">• 定期修改密码，建议使用强密码</view>
        <view class="tip-item">• 绑定手机和邮箱，便于找回账户</view>
        <view class="tip-item">• 不要在公共设备上保存登录信息</view>
        <view class="tip-item">• 发现异常登录及时修改密码</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SafeIndex',
  data() {
    return {
      userInfo: {
        mobile: '',
        email: ''
      }
    }
  },

  computed: {
    /**
     * 手机号描述
     */
    mobileDesc() {
      if (this.userInfo.mobile && this.userInfo.mobile.trim()) {
        // 脱敏显示手机号
        const mobile = this.userInfo.mobile
        return `${mobile.substring(0, 3)}****${mobile.substring(7)}`
      }
      return '未设置，建议绑定手机号'
    },

    /**
     * 邮箱描述
     */
    emailDesc() {
      if (this.userInfo.email && this.userInfo.email.trim()) {
        // 脱敏显示邮箱
        const email = this.userInfo.email
        const [username, domain] = email.split('@')
        if (username.length > 3) {
          return `${username.substring(0, 3)}***@${domain}`
        }
        return `${username.substring(0, 1)}***@${domain}`
      }
      return '未设置，建议绑定邮箱'
    }
  },

  onLoad() {
    this.loadUserInfo()
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 加载用户信息
     */
    loadUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = {
            mobile: userInfo.mobile || '',
            email: userInfo.email || ''
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 跳转到修改密码
     */
    goToPassword() {
      uni.navigateTo({
        url: '/pages_user/safe/password'
      })
    },

    /**
     * 跳转到绑定手机
     */
    goToMobile() {
      uni.navigateTo({
        url: '/pages_user/safe/mobile'
      })
    },

    /**
     * 跳转到绑定邮箱
     */
    goToEmail() {
      uni.navigateTo({
        url: '/pages_user/safe/email'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.safe-page {
  min-height: 100vh;
  background: #fff;
  padding-top: 20rpx;
}

/* 顶部导航 */
.nav-header {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

/* 安全设置列表 */
.safe-list {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.safe-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.safe-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  margin-right: 24rpx;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
}

.item-arrow {
  font-size: 40rpx;
  color: #aaa;
}

/* 安全提示 */
.security-tips {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}
</style>
