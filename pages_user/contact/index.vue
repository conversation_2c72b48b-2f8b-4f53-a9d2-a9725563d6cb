<!--
 * @Description: 联系我们页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 联系方式和反馈表单
-->
<template>
  <view class="contact-page">
    <!-- 顶部导航 -->
    <view class="nav-header">
      <view class="nav-back" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-title">联系我们</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 联系方式 -->
    <view class="contact-methods">
      <view class="method-item" @click="callPhone">
        <view class="method-icon">📞</view>
        <view class="method-content">
          <view class="method-title">客服电话</view>
          <view class="method-desc">************</view>
          <view class="method-time">工作时间：9:00-18:00</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="sendEmail">
        <view class="method-icon">📧</view>
        <view class="method-content">
          <view class="method-title">邮箱联系</view>
          <view class="method-desc"><EMAIL></view>
          <view class="method-time">24小时内回复</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="openWechat">
        <view class="method-icon">💬</view>
        <view class="method-content">
          <view class="method-title">微信客服</view>
          <view class="method-desc">uni_assistant_service</view>
          <view class="method-time">在线时间：9:00-22:00</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="joinQQ">
        <view class="method-icon">👥</view>
        <view class="method-content">
          <view class="method-title">QQ群</view>
          <view class="method-desc">123456789</view>
          <view class="method-time">用户交流群</view>
        </view>
        <view class="method-arrow">›</view>
      </view>
    </view>

    <!-- 意见反馈 -->
    <view class="feedback-section">
      <view class="section-title">意见反馈</view>
      <view class="feedback-form">
        <view class="form-item">
          <view class="form-label">反馈类型</view>
          <picker 
            :value="feedbackTypeIndex" 
            :range="feedbackTypes" 
            @change="onFeedbackTypeChange"
          >
            <view class="picker-content">
              {{ feedbackTypes[feedbackTypeIndex] }}
              <text class="picker-arrow">›</text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">联系方式（可选）</view>
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入手机号或邮箱，便于我们联系您"
            v-model="feedbackData.contact"
          />
        </view>

        <view class="form-item">
          <view class="form-label">反馈内容</view>
          <textarea 
            class="form-textarea" 
            placeholder="请详细描述您遇到的问题或建议，我们会认真处理"
            v-model="feedbackData.content"
            maxlength="500"
          ></textarea>
          <view class="char-count">{{ feedbackData.content.length }}/500</view>
        </view>

        <button 
          class="submit-btn" 
          :class="{ 'disabled': !canSubmitFeedback }"
          @click="submitFeedback"
          :disabled="!canSubmitFeedback"
        >
          提交反馈
        </button>
      </view>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">常见问题</view>
      <view class="faq-list">
        <view 
          class="faq-item" 
          v-for="(faq, index) in faqList" 
          :key="index"
          @click="toggleFaq(index)"
        >
          <view class="faq-question">
            <text class="question-text">{{ faq.question }}</text>
            <text class="question-icon" :class="{ 'expanded': faq.expanded }">›</text>
          </view>
          <view class="faq-answer" v-if="faq.expanded">
            {{ faq.answer }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ContactIndex',
  data() {
    return {
      feedbackTypeIndex: 0,
      feedbackTypes: ['功能建议', '问题反馈', '使用咨询', '其他'],
      feedbackData: {
        type: '功能建议',
        contact: '',
        content: ''
      },
      faqList: [
        {
          question: '如何重置密码？',
          answer: '您可以在登录页面点击"忘记密码"，通过绑定的手机号或邮箱重置密码。',
          expanded: false
        },
        {
          question: '如何开通VIP会员？',
          answer: '在个人中心页面点击VIP卡片，选择合适的套餐进行开通。',
          expanded: false
        },
        {
          question: '模型次数如何计算？',
          answer: '每次使用高速旗舰模型会消耗1次，VIP用户享有无限次数。',
          expanded: false
        },
        {
          question: '如何联系客服？',
          answer: '您可以通过客服电话、邮箱、微信或QQ群联系我们，我们会及时为您解答。',
          expanded: false
        }
      ]
    }
  },

  computed: {
    /**
     * 是否可以提交反馈
     */
    canSubmitFeedback() {
      return this.feedbackData.content.trim().length > 0
    }
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 拨打电话
     */
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },

    /**
     * 发送邮件
     */
    sendEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱地址已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 打开微信
     */
    openWechat() {
      uni.setClipboardData({
        data: 'uni_assistant_service',
        success: () => {
          uni.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 加入QQ群
     */
    joinQQ() {
      uni.setClipboardData({
        data: '123456789',
        success: () => {
          uni.showToast({
            title: 'QQ群号已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 反馈类型改变
     */
    onFeedbackTypeChange(e) {
      this.feedbackTypeIndex = e.detail.value
      this.feedbackData.type = this.feedbackTypes[this.feedbackTypeIndex]
    },

    /**
     * 提交反馈
     */
    async submitFeedback() {
      if (!this.canSubmitFeedback) return

      try {
        uni.showLoading({
          title: '提交中...'
        })

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))

        uni.hideLoading()
        uni.showToast({
          title: '反馈提交成功',
          icon: 'success'
        })

        // 清空表单
        this.feedbackData = {
          type: '功能建议',
          contact: '',
          content: ''
        }
        this.feedbackTypeIndex = 0

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        })
      }
    },

    /**
     * 切换FAQ展开状态
     */
    toggleFaq(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-page {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部导航 */
.nav-header {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

/* 联系方式 */
.contact-methods {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.method-item:last-child {
  border-bottom: none;
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  margin-right: 24rpx;
}

.method-content {
  flex: 1;
}

.method-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 28rpx;
  color: #007aff;
  margin-bottom: 4rpx;
}

.method-time {
  font-size: 24rpx;
  color: #666;
}

.method-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 通用区域样式 */
.feedback-section,
.faq-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 反馈表单 */
.feedback-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input,
.form-textarea {
  width: 100%;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.form-textarea {
  height: 200rpx;
  resize: none;
}

.picker-content {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  color: #ccc;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 30rpx;
}

.submit-btn.disabled {
  background: #e9ecef;
  color: #adb5bd;
}

/* FAQ列表 */
.faq-list {
  padding: 20rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
}

.faq-item:last-child {
  margin-bottom: 0;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.question-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.question-icon.expanded {
  transform: rotate(90deg);
}

.faq-answer {
  padding: 0 24rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #fff;
}
</style>
