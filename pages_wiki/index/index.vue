<!--
 * @Description: 知识库文件模式
 * @Author: 徐静(parkhansung)
 * @Date: 2025-01-22
-->
<template>
  <view class="file-mode mode-content">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-container">
        <view class="search-input-wrapper">
          <text class="search-icon">🔍</text>
          <input
            v-model="searchKeyword"
            class="search-input"
            placeholder="搜索资料库..."
            @input="onSearchInput"
            @confirm="onSearch"
          />
          <view
            v-if="searchKeyword"
            class="clear-btn"
            @click="clearSearch"
          >
            <text>✕</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <scroll-view class="tabs-container" scroll-x>
        <view
          v-for="category in categories"
          :key="category.key"
          class="tab-item"
          :class="{ active: currentCategory === category.key }"
          @click="switchCategory(category.key)"
        >
          <text>{{ category.name }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 文件列表 -->
    <scroll-view class="file-list" scroll-y>
      <view class="file-container">
        <!-- 空状态 -->
        <view v-if="filteredFiles.length === 0" class="empty-state">
          <text class="empty-icon">📂</text>
          <text class="empty-title">暂无文件</text>
          <text class="empty-desc">当前分类下暂无相关文件</text>
        </view>

        <!-- 文件网格 -->
        <view v-else class="file-grid">
          <view
            v-for="file in filteredFiles"
            :key="file.id"
            class="file-item"
            @click="handleFileClick(file)"
          >
            <view class="file-icon-wrapper">
              <text class="file-icon">{{ getFileIcon(file.type) }}</text>
              <view v-if="file.type === 'folder'" class="file-count">
                {{ file.count || 0 }}
              </view>
            </view>
            <view class="file-info">
              <text class="file-name">{{ file.name }}</text>
              <view class="file-meta">
                <text class="file-size">{{ getFileSize(file) }}</text>
                <text class="file-date">{{ formatDate(file.updateTime) }}</text>
              </view>
            </view>
            <view class="file-actions">
              <view class="action-btn" @click.stop="shareFile(file)">
                <text>📤</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <view class="fab" @click="showUploadOptions">
        <text class="fab-icon">➕</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FileMode',
  data() {
    return {
      searchKeyword: '',
      currentCategory: 'all',
      categories: [
        { key: 'all', name: '全部' },
        { key: 'aigc', name: 'AIGC人工智能' },
        { key: 'frontend', name: '前端开发' },
        { key: 'backend', name: '后端技术' },
        { key: 'python', name: 'Python' },
        { key: 'tools', name: '工具资源' },
        { key: 'docs', name: '文档资料' }
      ],
      files: [
        // 模拟数据 - 参考PC端界面的文件结构
        {
          id: 1,
          name: 'AIGC概念',
          type: 'folder',
          category: 'aigc',
          count: 15,
          updateTime: '2025-01-20',
          size: null
        },
        {
          id: 2,
          name: 'RAG入门到精通',
          type: 'folder',
          category: 'aigc',
          count: 8,
          updateTime: '2025-01-19',
          size: null
        },
        {
          id: 3,
          name: 'PostgreSQL的架构设计',
          type: 'document',
          category: 'backend',
          updateTime: '2025-01-18',
          size: 2048
        },
        {
          id: 4,
          name: '模型微调',
          type: 'folder',
          category: 'aigc',
          count: 12,
          updateTime: '2025-01-17',
          size: null
        },
        {
          id: 5,
          name: 'LangChain-chatglm',
          type: 'document',
          category: 'aigc',
          updateTime: '2025-01-16',
          size: 1536
        },
        {
          id: 6,
          name: '显卡硬件',
          type: 'folder',
          category: 'tools',
          count: 5,
          updateTime: '2025-01-15',
          size: null
        },
        {
          id: 7,
          name: 'pkill命令详解',
          type: 'document',
          category: 'tools',
          updateTime: '2025-01-14',
          size: 512
        },
        {
          id: 8,
          name: 'GraphRAG工具',
          type: 'folder',
          category: 'aigc',
          count: 3,
          updateTime: '2025-01-13',
          size: null
        }
      ]
    }
  },

  computed: {
    filteredFiles() {
      let result = this.files

      // 按分类过滤
      if (this.currentCategory !== 'all') {
        result = result.filter(file => file.category === this.currentCategory)
      }

      // 按搜索关键词过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase().trim()
        result = result.filter(file =>
          file.name.toLowerCase().includes(keyword)
        )
      }

      return result
    }
  },

  methods: {
    // 搜索输入处理
    onSearchInput(e) {
      this.searchKeyword = e.detail.value
    },

    // 执行搜索
    onSearch() {
      // 搜索逻辑已在computed中处理
    },

    // 清空搜索
    clearSearch() {
      this.searchKeyword = ''
    },

    // 切换分类
    switchCategory(category) {
      this.currentCategory = category
    },

    // 获取文件图标
    getFileIcon(type) {
      const icons = {
        folder: '📁',
        document: '📄',
        image: '🖼️',
        video: '🎥',
        audio: '🎵',
        archive: '📦'
      }
      return icons[type] || '📄'
    },

    // 获取文件大小显示
    getFileSize(file) {
      if (file.type === 'folder') {
        return `${file.count || 0} 项`
      }
      if (!file.size) return ''

      const size = file.size
      if (size < 1024) return `${size}B`
      if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
      return `${(size / (1024 * 1024)).toFixed(1)}MB`
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) return '今天'
      if (diffDays === 1) return '昨天'
      if (diffDays < 7) return `${diffDays}天前`

      return dateStr
    },

    // 处理文件点击
    handleFileClick(file) {
      if (file.type === 'folder') {
        // 进入文件夹
        uni.navigateTo({
          url: `/pages_wiki/folder/index?id=${file.id}&name=${file.name}`
        })
      } else {
        // 预览文档
        uni.navigateTo({
          url: `/pages_wiki/detail/index?id=${file.id}&name=${file.name}`
        })
      }
    },

    // 分享文件
    shareFile(file) {
      uni.showActionSheet({
        itemList: ['复制链接', '发送给好友', '保存到收藏'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              // 复制链接
              uni.setClipboardData({
                data: `知识库文件：${file.name}`,
                success: () => {
                  uni.showToast({ title: '链接已复制', icon: 'success' })
                }
              })
              break
            case 1:
              // 发送给好友
              uni.showToast({ title: '发送功能开发中', icon: 'none' })
              break
            case 2:
              // 保存到收藏
              uni.showToast({ title: '已添加到收藏', icon: 'success' })
              break
          }
        }
      })
    },

    // 显示上传选项
    showUploadOptions() {
      uni.showActionSheet({
        itemList: ['上传文档', '创建文件夹', '扫描识别'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.uploadDocument()
              break
            case 1:
              this.createFolder()
              break
            case 2:
              this.scanDocument()
              break
          }
        }
      })
    },

    // 上传文档
    uploadDocument() {
      uni.chooseFile({
        count: 1,
        type: 'file',
        success: (res) => {
          uni.showToast({ title: '文档上传功能开发中', icon: 'none' })
        }
      })
    },

    // 创建文件夹
    createFolder() {
      uni.showModal({
        title: '创建文件夹',
        editable: true,
        placeholderText: '请输入文件夹名称',
        success: (res) => {
          if (res.confirm && res.content) {
            uni.showToast({ title: '创建成功', icon: 'success' })
          }
        }
      })
    },

    // 扫描文档
    scanDocument() {
      uni.showToast({ title: '扫描功能开发中', icon: 'none' })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.file-mode {
  background-color: $background-color;
  height: calc(100vh - 88rpx);
  display: flex;
  flex-direction: column;
}

.search-bar {
  background-color: $card-background;
  padding: $spacing-md;
  border-bottom: $border-light;

  .search-container {
    .search-input-wrapper {
      display: flex;
      align-items: center;
      background-color: $background-color;
      border-radius: $border-radius-lg;
      padding: $spacing-sm $spacing-md;
      gap: $spacing-sm;

      .search-icon {
        color: $text-muted;
        font-size: $font-size-md;
      }

      .search-input {
        flex: 1;
        font-size: $font-size-md;
        color: $text-primary;
        border: none;
        outline: none;
        background: transparent;
      }

      .clear-btn {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        background-color: $text-muted;
        color: $white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
      }
    }
  }
}

.category-tabs {
  background-color: $card-background;
  border-bottom: $border-light;

  .tabs-container {
    display: flex;
    padding: $spacing-sm $spacing-md;
    white-space: nowrap;

    .tab-item {
      display: inline-block;
      padding: $spacing-sm $spacing-lg;
      margin-right: $spacing-sm;
      border-radius: $border-radius-lg;
      font-size: $font-size-sm;
      color: $text-secondary;
      background-color: $background-color;
      transition: all $transition-fast;
      white-space: nowrap;

      &.active {
        background-color: $primary-color;
        color: $white;
      }
    }
  }
}

.file-list {
  flex: 1;
  padding: $spacing-md;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  text-align: center;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: $spacing-md;
    opacity: 0.5;
  }

  .empty-title {
    font-size: $font-size-lg;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  .empty-desc {
    font-size: $font-size-md;
    color: $text-muted;
  }
}

.file-grid {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.file-item {
  background-color: $card-background;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  box-shadow: $shadow-light;
  transition: all $transition-fast;

  &:active {
    transform: scale(0.98);
    box-shadow: $shadow-normal;
  }

  .file-icon-wrapper {
    position: relative;
    width: 96rpx;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $background-color;
    border-radius: $border-radius-md;
    flex-shrink: 0;

    .file-icon {
      font-size: 48rpx;
    }

    .file-count {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      background-color: $primary-color;
      color: $white;
      font-size: 20rpx;
      padding: 4rpx 8rpx;
      border-radius: 20rpx;
      min-width: 32rpx;
      text-align: center;
    }
  }

  .file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;

    .file-name {
      font-size: $font-size-md;
      color: $text-primary;
      font-weight: 500;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .file-meta {
      display: flex;
      gap: $spacing-md;

      .file-size,
      .file-date {
        font-size: $font-size-xs;
        color: $text-muted;
      }
    }
  }

  .file-actions {
    .action-btn {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background-color: $background-color;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: $font-size-lg;
      transition: all $transition-fast;

      &:active {
        background-color: $border-color;
      }
    }
  }
}

.fab-container {
  position: fixed;
  bottom: calc(100rpx + env(safe-area-inset-bottom));
  right: $spacing-lg;
  z-index: 999;

  .fab {
    width: 112rpx;
    height: 112rpx;
    border-radius: 50%;
    background-color: $primary-color;
    box-shadow: $shadow-heavy;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all $transition-fast;

    .fab-icon {
      color: $white;
      font-size: $font-size-xl;
      font-weight: bold;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>