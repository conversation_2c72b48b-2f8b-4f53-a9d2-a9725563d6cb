// 知识库页面通用样式

// 主色系
$primary-color: #007aff;
$success-color: #4cd964;
$warning-color: #f0ad4e;
$error-color: #dd524d;

// 背景色
$background-color: #f8f9fa;
$card-background: #ffffff;

// 文字颜色
$text-primary: #333333;
$text-secondary: #666666;
$text-muted: #999999;
$white: #ffffff;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;

// 圆角
$border-radius-sm: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;

// 字体大小
$font-size-xs: 24rpx;
$font-size-sm: 26rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;

// 过渡动画
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;

// 阴影
$shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-normal: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
$shadow-heavy: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);

// 边框
$border-color: #e5e5e5;
$border-light: 1rpx solid $border-color;