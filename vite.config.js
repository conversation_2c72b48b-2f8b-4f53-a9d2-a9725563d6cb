/*
 * @Description:
 * @Author: 徐静(<EMAIL>)
 * @Date: 2022-11-28 16:44:57
 * @LastEditTime: 2022-11-28 16:44:58
 * @Copyright: (c) 2011-2021 http://www.winksoft.net All rights reserved.
 */
const path = require('path')
import { defineConfig } from 'vite'

export default defineConfig({
  base: '/',
  plugins: [vue(), vueJsx()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@api': path.resolve(__dirname, 'src/api'),
      '@views': path.resolve(__dirname, 'src/views'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@comp': path.resolve(__dirname, 'src/components'),
      '@assets': path.resolve(__dirname, 'src/assets'),
    }
  },
})