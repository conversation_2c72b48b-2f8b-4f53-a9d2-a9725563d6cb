<!--
 * @Description: 登录页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-29 16:49:39
-->
<template>
  <view class="page">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="circle circle1"></view>
      <view class="circle circle2"></view>
      <view class="circle circle3"></view>
      <view class="dot dot1"></view>
      <view class="dot dot2"></view>
      <view class="dot dot3"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-container">
          <view class="logo">
            <text class="logo-icon">🪼</text>
          </view>
          <view class="logo-glow"></view>
        </view>
        <text class="app-name">小蓝·AI小助理</text>
        <text class="app-subtitle">智能对话 · 高效助理</text>
      </view>

      <!-- 登录表单 -->
      <view class="form-section">
        <!-- 表单标题 -->
        <view class="form-title">请输入手机号/邮箱登录</view>

        <!-- 用户名输入 -->
        <view class="input-item">
          <view class="input-icon">👤</view>
          <input
            class="input"
            type="text"
            v-model="loginForm.account"
            placeholder="请输入手机号/邮箱"
            :maxlength="50"
          />
        </view>

        <!-- 密码输入 -->
        <view class="input-item">
          <view class="input-icon">🔒</view>
          <input
            class="input"
            type="password"
            v-model="loginForm.password"
            placeholder="请输入密码"
            :maxlength="20"
          />
        </view>

        <!-- 登录按钮 -->
        <button class="login-btn" @click="handleLogin">
          <text class="login-text">登录</text>
        </button>

        <!-- 忘记密码 -->
        <view class="forgot-password">
          <text @click="showRegisterPopup">注册/忘记密码？</text>
        </view>
      </view>
    </view>

    <!-- 注册组件 -->
    <RegisterComponent
      :show="showRegister"
      @close="showRegister = false"
      @success="handleRegisterSuccess"
    />
  </view>
</template>

<script>
import { getConfig } from '@/framework/config/config';
import RegisterComponent from './components/RegisterComponent.vue'

export default {
  components: {
    RegisterComponent
  },
  data() {
    return {
      loginForm: {
        account: '',
        password: ''
      },
      showRegister: false
    }
  },
  methods: {
    // 处理登录
    async handleLogin() {
      // 表单验证
      if (!this.loginForm.account.trim()) {
        uni.showToast({
          title: '请输入手机号/邮箱',
          icon: 'none'
        });
        return;
      }

      if (!this.loginForm.password.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }

      // 验证账号格式（手机号或邮箱）
      const account = this.loginForm.account.trim();
      const isEmail = account.includes('@');
      const isMobile = /^1[3-9]\d{9}$/.test(account);

      if (!isEmail && !isMobile) {
        uni.showToast({
          title: '请输入正确的手机号或邮箱格式',
          icon: 'none'
        });
        return;
      }


        // 构建请求参数
        const loginData = {
          user_pwd: this.loginForm.password,
          // 根据环境判断设备类型：生产环境是uni-h5，测试环境是uni-h5-demo
          device_type: process.env.NODE_ENV === 'development' ? 'uni-h5-demo' : 'uni-h5'
        };

        // 根据输入类型设置对应字段
        if (isEmail) {
          loginData.email = account;
        } else {
          loginData.mobile = account;
        }

        // 调用登录API
        const response = await this.$ajax.post('/login/userLogin', loginData);

        if (response && response.code == 200) {
          // 登录成功，保存token
          console.log("🚀 -> handleLogin -> response.data.token:", response.data.token)
          if (response.data && response.data.token) {
            uni.setStorageSync(getConfig().tokenName, response.data.token);
            this.$toast('登录成功');
            // 延迟跳转到首页
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/index/index'
              });
            }, 200);
          } else {
            uni.showToast({
              title: '登录失败，未获取到token',
              icon: 'none'
            });
          }
        } else {
          // 登录失败
          uni.showToast({
            title: response.msg || '登录失败，请检查账号密码',
            icon: 'none'
          });
        }

    },

    // 显示注册弹窗
    showRegisterPopup() {
      this.showRegister = true;
    },

    // 注册成功回调
    handleRegisterSuccess(accountInfo) {
      this.showRegister = false;

      // 如果传递了账号信息，自动填充到登录表单
      if (accountInfo && accountInfo.account) {
        this.loginForm.account = accountInfo.account;

        uni.showToast({
          title: '注册成功，请输入密码登录',
          icon: 'success',
          duration: 2000
        });
      } else {
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        });
      }
    }
  }
}
</script>

<style scoped>
/* 页面容器 */
.page {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 装饰圆圈 */
.circle {
  position: absolute;
  border: 2rpx solid rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.circle1 {
  width: 300rpx;
  height: 300rpx;
  top: -150rpx;
  right: -150rpx;
  animation-delay: 0s;
}

.circle2 {
  width: 200rpx;
  height: 200rpx;
  bottom: -100rpx;
  left: -100rpx;
  animation-delay: 2s;
}

.circle3 {
  width: 150rpx;
  height: 150rpx;
  top: 30%;
  left: -75rpx;
  animation-delay: 4s;
}

/* 装饰点 */
.dot {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: rgba(0, 123, 255, 0.3);
  border-radius: 50%;
  animation: twinkle 3s ease-in-out infinite;
}

.dot1 {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.dot2 {
  top: 60%;
  left: 15%;
  animation-delay: 1s;
}

.dot3 {
  bottom: 25%;
  right: 30%;
  animation-delay: 2s;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
  box-sizing: border-box;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}

.logo-container {
  position: relative;
  margin-bottom: 40rpx;
}

.logo {
  width: 140rpx;
  height: 140rpx;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15rpx 40rpx rgba(0, 123, 255, 0.3);
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 160rpx;
  height: 160rpx;
  background: radial-gradient(circle, rgba(0, 123, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
  z-index: 1;
}

.logo-icon {
  font-size: 80rpx;
  color: #ffffff;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #333333;
  text-align: center;
  margin-bottom: 16rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
}

/* 表单标题 */
.form-title {
  font-size: 26rpx;
  color: #999999;
  text-align: left;
  margin-bottom: 20rpx;
}

/* 输入项 */
.input-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
  height: 100rpx;
  transition: all 0.3s ease;
}

.input-item:focus-within {
  border-color: #007bff;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(0, 123, 255, 0.1);
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #007bff;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  border: none;
  background: transparent;
  line-height: 1.4;
}

.input::placeholder {
  color: #999999;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 50rpx;
  margin-top: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.login-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 忘记密码 */
.forgot-password {
  text-align: right;
  margin-top: 30rpx;
}

.forgot-password text {
  font-size: 26rpx;
  color: #007bff;
  text-decoration: underline;
}



/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
</style>
