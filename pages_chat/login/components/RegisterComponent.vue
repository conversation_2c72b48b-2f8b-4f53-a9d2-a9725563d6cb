<!--
 * @Description: 注册组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-29 17:00:00
-->
<template>
  <u-popup
    :show="show"
    mode="bottom"
    :round="16"
    :closeable="true"
    @close="handleClose"
    :customStyle="{ height: '60vh' }"
  >
    <view class="register-popup">
      <!-- 弹窗标题 -->
      <view class="popup-header">
        <text class="popup-title">用户注册/找回密码</text>
      </view>

      <!-- 步骤1: 验证手机/邮箱 -->
      <view v-if="currentStep === 1" class="step-content">

        <!-- 切换标签 -->
        <view class="tab-switcher">
          <view
            class="tab-item"
            :class="{ active: currentTab === 'phone' }"
            @click="switchTab('phone')"
          >
            手机注册
          </view>
          <view
            class="tab-item"
            :class="{ active: currentTab === 'email' }"
            @click="switchTab('email')"
          >
            邮箱注册
          </view>
        </view>

        <!-- 步骤进度条 -->
        <view class="step-progress">
          <text class="step-label" :class="{ active: currentStep === 1 }">
            {{ currentTab === 'phone' ? '验证手机' : '验证邮箱' }}
          </text>
          <view class="step-line-container">
            <view class="step-circle" :class="{ active: currentStep === 1 }">1</view>
            <view class="step-line" :class="{ active: currentStep === 2 }"></view>
            <view class="step-circle" :class="{ active: currentStep === 2 }">2</view>
          </view>
          <text class="step-label" :class="{ active: currentStep === 2 }">设置密码</text>
        </view>

        <!-- 手机号/邮箱输入 -->
        <view class="input-group">
          <view class="input-label">
            {{ currentTab === 'phone' ? '登录手机号' : '登录邮箱' }}
          </view>
          <input
            class="input-field"
            :type="currentTab === 'phone' ? 'number' : 'text'"
            v-model="registerForm.account"
            :placeholder="currentTab === 'phone' ? '请输入登录手机号' : '请输入登录邮箱'"
            :maxlength="currentTab === 'phone' ? 11 : 50"
          />
        </view>

        <!-- 验证码输入 -->
        <view class="input-group">
          <view class="input-label">验证码</view>
          <view class="verify-container">
            <input
              class="verify-input"
              type="text"
              v-model="registerForm.verifyCode"
              placeholder="请输入验证码"
              :maxlength="6"
            />
            <button
              class="get-code-btn"
              :class="{ disabled: codeCountdown > 0 }"
              @click="getVerifyCode"
              :disabled="codeCountdown > 0"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
            </button>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="next-btn" @click="nextStep">
            ✈ 下一步
          </button>
          <button class="cancel-btn" @click="handleClose">
            取消
          </button>
        </view>
      </view>

      <!-- 步骤2: 设置密码 -->
      <view v-if="currentStep === 2" class="step-content">

        <!-- 步骤进度条 -->
        <view class="step-progress">
          <text class="step-label completed">
            {{ currentTab === 'phone' ? '验证手机' : '验证邮箱' }}
          </text>
          <view class="step-line-container">
            <view class="step-circle completed">1</view>
            <view class="step-line active"></view>
            <view class="step-circle active">2</view>
          </view>
          <text class="step-label active">设置密码</text>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-label">登录密码</view>
          <input
            class="input-field"
            type="password"
            v-model="registerForm.password"
            placeholder="请输入登录密码"
            :maxlength="20"
          />
        </view>

        <!-- 确认密码输入 -->
        <view class="input-group">
          <view class="input-label">确认密码</view>
          <input
            class="input-field"
            type="password"
            v-model="registerForm.confirmPassword"
            placeholder="请再次输入密码"
            :maxlength="20"
          />
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="confirm-btn" @click="handleRegister">
            ✓ 确定
          </button>
          <button class="cancel-btn" @click="handleClose">
            取消
          </button>
        </view>
      </view>
    </view>

    <!-- 成功提示 -->
    <view v-if="showSuccess" class="success-toast">
      <view class="success-icon">✓</view>
      <text class="success-text">验证码校验通过</text>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentStep: 1, // 当前步骤 1-验证手机 2-设置密码
      currentTab: 'phone', // 当前标签 phone-手机 email-邮箱
      codeCountdown: 0, // 验证码倒计时
      showSuccess: false, // 显示成功提示
      registerForm: {
        account: '', // 手机号或邮箱
        verifyCode: '', // 验证码
        password: '', // 密码
        confirmPassword: '' // 确认密码
      }
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm();
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.currentStep = 1;
      this.currentTab = 'phone';
      this.codeCountdown = 0;
      this.showSuccess = false;
      this.registerForm = {
        account: '',
        verifyCode: '',
        password: '',
        confirmPassword: ''
      };
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close');
    },

    // 切换标签
    switchTab(tab) {
      this.currentTab = tab;
      this.registerForm.account = '';
      this.registerForm.verifyCode = '';
    },

    // 获取验证码
    async getVerifyCode() {
      // 验证手机号或邮箱格式
      if (!this.registerForm.account.trim()) {
        uni.showToast({
          title: this.currentTab === 'phone' ? '请输入手机号' : '请输入邮箱',
          icon: 'none'
        });
        return;
      }

      if (this.currentTab === 'phone') {
        const phoneReg = /^1[3-9]\d{9}$/;
        if (!phoneReg.test(this.registerForm.account)) {
          uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
          });
          return;
        }
      } else {
        const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailReg.test(this.registerForm.account)) {
          uni.showToast({
            title: '请输入正确的邮箱格式',
            icon: 'none'
          });
          return;
        }
      }

      try {
        // 构建请求参数
        const requestData = {};
        if (this.currentTab === 'phone') {
          requestData.mobile = this.registerForm.account;
        } else {
          requestData.email = this.registerForm.account;
        }

        // 调用发送验证码API
        const apiUrl = this.currentTab === 'phone' ? '/login/sendCode' : '/login/sendEmailCode';
        const response = await this.$api.post(apiUrl, requestData);

        if (response && response.back === 1) {
          // 发送成功，开始倒计时
          this.startCountdown();

          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          });
        } else {
          // 发送失败
          uni.showToast({
            title: response.msg || '验证码发送失败，请稍后重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('发送验证码错误:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      }
    },

    // 开始倒计时
    startCountdown() {
      this.codeCountdown = 60;
      const timer = setInterval(() => {
        this.codeCountdown--;
        if (this.codeCountdown <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    },

    // 下一步
    async nextStep() {
      // 验证表单
      if (!this.registerForm.account.trim()) {
        uni.showToast({
          title: this.currentTab === 'phone' ? '请输入手机号' : '请输入邮箱',
          icon: 'none'
        });
        return;
      }

      if (!this.registerForm.verifyCode.trim()) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        });
        return;
      }

      if (this.registerForm.verifyCode.length !== 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        });
        return;
      }

      try {
        // 构建请求参数
        const requestData = {
          code: this.registerForm.verifyCode
        };

        if (this.currentTab === 'phone') {
          requestData.mobile = this.registerForm.account;
        } else {
          requestData.email = this.registerForm.account;
        }

        // 调用验证码校验API
        const response = await this.$api.post('/login/checkCode', requestData);

        if (response && response.code === 200) {
          // 验证成功，显示成功提示
          this.showSuccessToast();

          // 延迟跳转到下一步
          setTimeout(() => {
            this.currentStep = 2;
          }, 200);
        } else {
          // 验证失败
          uni.showToast({
            title: response.msg || '验证码错误，请重新输入',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('验证码校验错误:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      }
    },

    // 显示成功提示
    showSuccessToast() {
      this.showSuccess = true;
      setTimeout(() => {
        this.showSuccess = false;
      }, 200);
    },

    // 处理注册
    async handleRegister() {
      // 验证密码
      if (!this.registerForm.password.trim()) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }

      if (this.registerForm.password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6位',
          icon: 'none'
        });
        return;
      }

      if (!this.registerForm.confirmPassword.trim()) {
        uni.showToast({
          title: '请确认密码',
          icon: 'none'
        });
        return;
      }

      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        });
        return;
      }

      try {
        // 构建请求参数
        const requestData = {
          user_pwd: this.registerForm.password,
          check_pwd: this.registerForm.confirmPassword,
          code: this.registerForm.verifyCode
        };

        if (this.currentTab === 'phone') {
          requestData.mobile = this.registerForm.account;
        } else {
          requestData.email = this.registerForm.account;
        }

        // 调用注册API
        const response = await this.$api.post('/reg/userReg', requestData);

        if (response && response.code === 200) {
          // 注册成功
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          });

          // 触发成功事件，并传递账号信息用于自动填充登录表单
          this.$emit('success', {
            account: this.registerForm.account,
            type: this.currentTab
          });
        } else {
          // 注册失败
          uni.showToast({
            title: response.msg || '注册失败，请稍后重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('注册错误:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style scoped>
/* 注册弹窗 */
.register-popup {
  padding: 40rpx 30rpx;
  height: 60vh;
  overflow-y: auto;
}

/* 弹窗标题 */
.popup-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1.8;
  color: #333333;
}

/* 步骤内容 */
.step-content {
  width: 100%;
}



/* 切换标签 */
.tab-switcher {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666666;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #007bff;
  color: #ffffff;
}

/* 步骤进度条 */
.step-progress {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  height: 60rpx;
}

.step-label {
  flex: 0 0 25%;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}

.step-label.active {
  color: #007bff;
}

.step-label.completed {
  color: #007bff;
}

.step-line-container {
  flex: 0 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.step-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  color: #999999;
}

.step-circle.active {
  background: #007bff;
  color: #ffffff;
}

.step-circle.completed {
  background: #007bff;
  color: #ffffff;
}

.step-line {
  flex: 1;
  height: 4rpx;
  background: #e9ecef;
}

.step-line.active {
  background: #007bff;
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

/* 输入标签 */
.input-label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

/* 输入框 */
.input-field {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #007bff;
  background: #ffffff;
}

/* 验证码容器 */
.verify-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.verify-input {
  flex: 1;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.verify-input:focus {
  border-color: #007bff;
  background: #ffffff;
}

/* 获取验证码按钮 */
.get-code-btn {
  width: 180rpx;
  height: 80rpx;
  background: #007bff;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.get-code-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.get-code-btn:not(.disabled):active {
  background: #0056b3;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.next-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.next-btn:active,
.confirm-btn:active {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  background: #e9ecef;
}

/* 成功提示 */
.success-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(40, 167, 69, 0.9);
  color: #ffffff;
  padding: 30rpx 40rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 9999;
  animation: fadeInOut 1.5s ease-in-out;
}

.success-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.success-text {
  font-size: 28rpx;
}

/* 动画效果 */
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  20%, 80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}
</style>
