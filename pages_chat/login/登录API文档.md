# 登录模块API文档

## 概述

本文档详细描述了小蓝的AI小助理登录模块的所有API接口，包括用户登录、注册、验证码发送等功能。

## 基础配置

### API基础地址
- **开发环境**: `http://localhost:8808`
- **生产环境**: `/`

### 请求头配置
- **Content-Type**: `application/json`
- **Authorization**: `Bearer {token}` (登录后需要)
- **User-Origin**: 用户来源标识

### Token配置
- **存储位置**: localStorage
- **存储键名**: `assistantToken`
- **失效状态码**: 401

## API接口列表

### 1. 用户登录

**接口地址**: `POST /login/userLogin`

**功能描述**: 用户通过手机号或邮箱进行登录

**请求参数**:
```json
{
  "mobile": "string",     // 手机号（与email二选一）
  "email": "string",      // 邮箱（与mobile二选一）
  "user_pwd": "string"    // 登录密码
}
```

**参数说明**:
- `mobile`: 手机号码，格式需符合中国大陆手机号规范
- `email`: 邮箱地址，需符合邮箱格式规范
- `user_pwd`: 用户密码，需符合密码强度要求
- 注意：mobile和email字段二选一，系统会根据输入内容自动判断

**响应参数**:
```json
{
  "back": 1,              // 状态码，1表示成功
  "msg": "string",        // 响应消息
  "data": {
    "token": "string"     // 登录凭证
  }
}
```

**响应说明**:
- `back`: 1表示登录成功，其他值表示失败
- `token`: 登录成功后返回的访问令牌，需存储到localStorage中
- 登录成功后会自动跳转到首页

**错误处理**:
- 手机号/邮箱格式错误
- 密码错误
- 账号不存在
- 服务器内部错误

### 2. 发送手机验证码

**接口地址**: `POST /login/sendCode`

**功能描述**: 向指定手机号发送短信验证码，用于注册或找回密码

**请求参数**:
```json
{
  "mobile": "string"      // 手机号码
}
```

**参数说明**:
- `mobile`: 中国大陆手机号码，11位数字

**响应参数**:
```json
{
  "back": 1,              // 状态码，1表示成功
  "msg": "string"         // 响应消息
}
```

**响应说明**:
- `back`: 1表示发送成功，其他值表示失败
- 验证码有效期为5分钟
- 同一手机号60秒内只能发送一次

**错误处理**:
- 手机号格式错误
- 发送频率过快
- 短信服务异常

### 3. 发送邮箱验证码

**接口地址**: `POST /login/sendEmailCode`

**功能描述**: 向指定邮箱发送验证码邮件，用于注册或找回密码

**请求参数**:
```json
{
  "email": "string"       // 邮箱地址
}
```

**参数说明**:
- `email`: 有效的邮箱地址

**响应参数**:
```json
{
  "back": 1,              // 状态码，1表示成功
  "msg": "string"         // 响应消息
}
```

**响应说明**:
- `back`: 1表示发送成功，其他值表示失败
- 验证码有效期为5分钟
- 同一邮箱60秒内只能发送一次
- 邮件可能被误拦截，建议检查垃圾邮箱

**错误处理**:
- 邮箱格式错误
- 发送频率过快
- 邮件服务异常

### 4. 验证码校验

**接口地址**: `POST /login/checkCode`

**功能描述**: 校验手机或邮箱验证码的有效性

**请求参数**:
```json
{
  "mobile": "string",     // 手机号（与email二选一）
  "email": "string",      // 邮箱（与mobile二选一）
  "code": "string"        // 验证码
}
```

**参数说明**:
- `mobile`: 手机号码（手机验证时使用）
- `email`: 邮箱地址（邮箱验证时使用）
- `code`: 6位数字验证码

**响应参数**:
```json
{
  "code": 200,            // 状态码，200表示成功
  "msg": "string"         // 响应消息
}
```

**响应说明**:
- `code`: 200表示验证成功，其他值表示失败
- 验证成功后可进入下一步设置密码

**错误处理**:
- 验证码错误
- 验证码已过期
- 验证码已使用

### 5. 用户注册

**接口地址**: `POST /reg/userReg`

**功能描述**: 完成用户注册，设置登录密码

**请求参数**:
```json
{
  "mobile": "string",     // 手机号（与email二选一）
  "email": "string",      // 邮箱（与mobile二选一）
  "user_pwd": "string",   // 登录密码
  "check_pwd": "string",  // 确认密码
  "code": "string"        // 验证码
}
```

**参数说明**:
- `mobile`: 手机号码（手机注册时使用）
- `email`: 邮箱地址（邮箱注册时使用）
- `user_pwd`: 登录密码，需符合密码强度要求
- `check_pwd`: 确认密码，必须与user_pwd一致
- `code`: 已验证通过的验证码

**响应参数**:
```json
{
  "code": 200,            // 状态码，200表示成功
  "msg": "string"         // 响应消息
}
```

**响应说明**:
- `code`: 200表示注册成功，其他值表示失败
- 注册成功后会返回注册的账号信息

**错误处理**:
- 两次密码不一致
- 验证码无效
- 账号已存在
- 密码强度不够

### 6. 用户退出登录

**接口地址**: `GET /user/logout`

**功能描述**: 用户退出登录，清除服务端会话

**请求参数**: 无

**请求头**:
```
Authorization: Bearer {token}
```

**响应参数**:
```json
{
  "code": 200,            // 状态码，200表示成功
  "msg": "string"         // 响应消息
}
```

**响应说明**:
- `code`: 200表示退出成功
- 退出成功后会清除本地token并跳转到登录页

## 前端表单验证规则

### 登录表单验证
```javascript
{
  mobile: "required#手机号/邮箱",
  user_pwd: "required|password#密码"
}
```

### 手机注册表单验证
```javascript
{
  mobile: "required|mobile#手机号",
  code: "required#验证码"
}
```

### 邮箱注册表单验证
```javascript
{
  email: "required|email#邮箱",
  code: "required#验证码"
}
```

### 密码设置表单验证
```javascript
{
  user_pwd: "required|password#密码",
  check_pwd: "required|password#确认密码"
}
```

## 业务流程

### 登录流程
1. 用户输入手机号/邮箱和密码
2. 前端验证输入格式
3. 调用登录API
4. 成功后存储token到localStorage
5. 跳转到首页

### 注册流程
1. 选择注册方式（手机/邮箱）
2. 输入手机号或邮箱
3. 发送验证码
4. 输入验证码并校验
5. 设置登录密码
6. 完成注册
7. 返回登录页面并自动填充账号

### 找回密码流程
1. 在登录页面点击"找回密码"
2. 选择找回方式（手机/邮箱）
3. 输入手机号或邮箱
4. 发送验证码
5. 输入验证码并校验
6. 重新设置密码
7. 完成密码重置

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 1 | 成功（登录接口） |
| 200 | 成功（其他接口） |
| 401 | 未授权，需要重新登录 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. **安全性**：
   - 所有密码传输前应进行加密处理
   - Token需要安全存储，避免XSS攻击
   - 验证码有时效性，需及时使用

2. **用户体验**：
   - 验证码发送后有60秒冷却时间
   - 邮箱验证码可能被误拦截，提醒用户检查垃圾邮箱
   - 登录成功后有600ms延迟跳转，提升用户体验

3. **兼容性**：
   - 支持手机号和邮箱两种登录方式
   - 自动识别输入内容类型（包含@符号判断为邮箱）
   - 密码输入框支持回车键快速登录

4. **错误处理**：
   - 所有API调用都应包含错误处理逻辑
   - 网络异常时给出友好提示
   - 表单验证失败时高亮错误字段

## 开发环境配置

### 环境变量
```bash
# 开发环境
VUE_APP_SERVICE_URL = 'http://localhost:8203'
VUE_APP_BASE_API = 'http://localhost:8203'

# 生产环境
VUE_APP_SERVICE_URL = '/'
VUE_APP_BASE_API = '/admin-api'
```

### 代理配置
开发环境下，前端请求会通过webpack代理转发到后端服务器，避免跨域问题。

---

*文档版本：v1.0*  
*最后更新：2025-07-29*  
*维护者：开发团队*
