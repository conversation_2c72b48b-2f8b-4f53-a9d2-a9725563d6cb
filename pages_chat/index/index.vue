<!--
 * @Description: 对话主界面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27 11:31:12
-->

<template>
	<view class="chat-container">
		<!-- 聊天消息列表 -->
		<scroll-view
			class="chat-messages"
			scroll-y
			:scroll-top="scrollTop"
			:scroll-into-view="scrollIntoView"
			:scroll-with-animation="true"
		>
			<!-- 欢迎界面 -->
			<view v-if="chatMessages.length === 0" class="chat-welcome">
				<!-- 科技背景装饰 -->
				<view class="tech-decorations">
					<!-- 浮动粒子 -->
					<view class="floating-particles">
						<view class="particle particle-1"></view>
						<view class="particle particle-2"></view>
						<view class="particle particle-3"></view>
						<view class="particle particle-4"></view>
						<view class="particle particle-5"></view>
						<view class="particle particle-6"></view>
					</view>



					<!-- 光效装饰 -->
					<view class="light-effects">
						<view class="light-beam light-beam-1"></view>
						<view class="light-beam light-beam-2"></view>

					</view>
				</view>

				<!-- 主要内容区域 -->
				<view class="welcome-main">
					<!-- AI头像 -->
					<view class="welcome-avatar">
						<view class="avatar-container">
							<view class="avatar-bg"></view>
							<view class="avatar-icon">
								<text class="icon-text">AI</text>
							</view>
							<view class="avatar-pulse"></view>
						</view>
					</view>

					<!-- 欢迎文字 -->
					<view class="welcome-content">
						<view class="welcome-title">你好！我是私人小助理</view>
					</view>
				</view>

				<!-- 功能卡片 -->
				<view class="feature-cards">
					<view class="feature-card" @tap="handleFeatureCardTap('chat')">
						<view class="card-icon">💬</view>
						<view class="card-content">
							<view class="card-title">智能对话</view>
							<view class="card-desc">输入任何问题开始对话</view>
						</view>
					</view>

					<view class="feature-card" @tap="handleFeatureCardTap('voice')">
						<view class="card-icon">🎤</view>
						<view class="card-content">
							<view class="card-title">语音交流</view>
							<view class="card-desc">点击麦克风进行语音输入</view>
						</view>
					</view>
				</view>

				<!-- 开始提示 -->
				<view class="start-hint">
					<view class="hint-text">在下方输入框开始我们的对话吧</view>
					<view class="hint-arrow">↓</view>
				</view>
			</view>

			<!-- 消息列表 -->
			<view
				v-for="(message, index) in chatMessages"
				:key="message.id"
				class="message-wrapper"
			>
				<chat-item
					:ref="`chatItem_${message.id}`"
					:message="message"
					:show-time="shouldShowTime(message, index)"
					:user-avatar-text="userAvatarText"
					@edit="handleEditMessage"
					@resend="handleResendMessage"
					@edit-save="handleEditSave"
					@edit-resend="handleEditResend"
				/>
			</view>

			<!-- 底部锚点，用于精确滚动到底部 -->
			<view
				id="scroll-bottom-anchor"
				style="height: 5px; width: 100%"
			></view>
		</scroll-view>

		<!-- 底部输入组件 -->
		<chat-bottom-input
			:chat-mode="chatSettings.chatMode"
			@send-message="handleSendMessage"
			@voice-message="handleVoiceMessage"
			@voice-start="handleVoiceStart"
			@voice-cancel="handleVoiceCancel"
			@voice-error="handleVoiceError"
			@mode-change="handleModeChange"
		/>

		<!-- SSE客户端组件 -->
		<gao-ChatSSEClient
			ref="sseClientRef"
			:max-retry-count="1"
			:timeout="300000"
			:heartbeat-timeout="120000"
			@onOpen="handleSSEOpen"
			@onMessage="handleSSEMessage"
			@onError="handleSSEError"
			@onFinish="handleSSEFinish"
		/>

		<!-- 右上角菜单弹窗 -->
		<u-popup :show="showMenuPopup" closeable mode="bottom" :overlay="false" duration="200" :round="20"  @close="closeMenuPopup">
			<view class="menu-popup" :class="{ 'menu-enter': showMenuPopup }" v-if="showMenuPopup">
				<view class="menu-item" @click="handleSearchHistory">
					<text class="menu-icon">🔍</text>
					<text class="menu-text">搜索会话</text>
				</view>
				<view class="menu-item" @click="handleClearChat">
					<text class="menu-icon">🗑️</text>
					<text class="menu-text">清空会话</text>
				</view>
				<view class="menu-item" @click="handleSettings">
					<text class="menu-icon">⚙️</text>
					<text class="menu-text">会话设置</text>
				</view>
			</view>
		</u-popup>

		<!-- 会话设置弹窗 -->
		<u-popup :show="showSettingsPopup" mode="bottom" :round="20" :safe-area-inset-top="true" @close="showSettingsPopup = false">
			<chat-settings
				:current-settings="chatSettings"
				@cancel="showSettingsPopup = false"
				@save="handleSettingsSave"
			/>
		</u-popup>
	</view>
</template>

<script>
import ChatItem from "../components/chatItem.vue";
import ChatBottomInput from "../components/ChatBottomInput.vue";
import ChatSettings from "../components/chatSettings.vue";
import gaoChatSSEClient from "@/uni_modules/gao-ChatSSEClient/components/gao-ChatSSEClient/gao-ChatSSEClient.vue";
import apiModule from "../components/api.js";

const { ajaxSSE } = apiModule;

export default {
	name: "ChatIndex",
	components: {
		ChatItem,
		ChatBottomInput,
		ChatSettings,
		"gao-ChatSSEClient": gaoChatSSEClient,
	},
	data() {
		return {
			chatMessages: [],
			scrollTop: 0, // 滚动位置
			scrollViewHeight: 0,
			scrollContentHeight: 0,
			scrollIntoView: "", // 滚动到指定元素
			userAvatarText: "我", // 用户头像文字，可以根据用户信息动态设置
			isAITyping: false, // AI是否正在输入
			currentAIMessage: null, // 当前AI消息对象（用于流式更新）
			isSSEConnected: false, // SSE连接状态
			showMenuPopup: false, // 右上角菜单弹窗显示状态
			showSettingsPopup: false, // 会话设置弹窗显示状态

			// 会话设置
			chatSettings: {
				enableMemory: true, // 是否开启无限上下文
				maxSessionCount: 6, // 最大提交会话条数
				chatMode: 'chat', // 对话模式: 'chat' | 'agents' - 默认为chat模式
				enabledAgents: ['search', 'openUrl', 'doc', 'qa', 'task', 'accounting'] // 启用的Agents
			}
		};
	},

	mounted() {
		// 初始化SSE客户端
		this.initSSEClient();

		// 加载保存的会话设置
		this.loadChatSettings();
	},

	onNavigationBarButtonTap(e) {
		// 监听右上角按钮点击
		if (e.index === 0) {
			this.showMenuPopup = true;
		}
	},

  methods: {
    closeMenuPopup() {
      this.showMenuPopup = false;
    },
		/**
		 * 初始化SSE客户端
		 */
		initSSEClient() {
			if (this.$refs.sseClientRef) {
				ajaxSSE.setSSEClient(this.$refs.sseClientRef);
				console.log("✅ SSE客户端初始化完成");
			} else {
				// 如果组件还没有挂载，延迟初始化
				this.$nextTick(() => {
					this.initSSEClient();
				});
			}
		},

		/**
		 * 处理发送文字消息
		 * @param {Object|String} messageData 消息数据或内容
		 */
		handleSendMessage(messageData) {
			// 兼容旧格式（字符串）和新格式（对象）
			let content, mode, chatMode;

			if (typeof messageData === 'string') {
				content = messageData;
				mode = null;
				chatMode = this.chatSettings.chatMode;
			} else {
				content = messageData.content;
				mode = messageData.mode;
				chatMode = messageData.chatMode;
			}

			// 添加用户消息
			const userMessage = {
				id: this.generateMessageId(),
				role: "user",
				content: content,
				timestamp: Date.now(),
				mode: mode, // 保存模式信息
				chatMode: chatMode
			};

			this.chatMessages.push(userMessage);
			this.scrollToBottom();

			// 发送SSE请求，传递模式信息
			this.sendSSERequest(content, mode, chatMode);
		},

		/**
		 * 发送SSE请求
		 * @param {String} userInput 用户输入
		 * @param {String} mode 模式：'search' | 'think' | null
		 * @param {String} chatMode 对话模式：'chat' | 'agents'
		 */
		async sendSSERequest(userInput, mode = null, chatMode = 'chat') {
			if (this.isAITyping) {
				uni.showToast({
					title: "AI正在回复中，请稍候",
					icon: "none",
				});
				return;
			}

			this.isAITyping = true;

			// 构建消息历史（包含刚添加的用户消息）
			const messages = this.buildMessageHistory();

			try {
				// 创建AI消息占位符
				this.currentAIMessage = {
					id: this.generateMessageId(),
					role: "assistant",
					content: "",
					timestamp: Date.now(),
					isStreaming: true,
				};
				this.chatMessages.push(this.currentAIMessage);
				this.scrollToBottom();

				// 等待组件渲染完成后清空reasoning_content
				this.$nextTick(() => {
					const chatItemRefs = this.$refs[`chatItem_${this.currentAIMessage.id}`];
					if (chatItemRefs && chatItemRefs[0]) {
						chatItemRefs[0].clearReasoningContent();
					}
				});

				// 发送SSE请求
				await ajaxSSE.post({
					messages,
					onMessage: this.handleStreamMessage,
					onOpen: this.handleStreamOpen,
					onError: this.handleStreamError,
					onFinish: this.handleStreamFinish,
				});
				this.scrollToBottom();
			} catch (error) {
				console.error("❌ SSE请求失败:", error);
				this.handleStreamError(error);
			}
		},

		/**
		 * 构建消息历史
		 * @returns {Array} 消息历史数组
		 */
		buildMessageHistory() {
			const messages = [];

			// 添加历史对话（最近4轮，排除当前正在处理的消息）
			const historyMessages = this.chatMessages
				.filter(
					(msg) =>
						(msg.role === "user" || msg.role === "assistant") &&
						msg.content &&
						msg.content.trim() !== "" &&
						!msg.isStreaming // 排除正在流式输入的消息
				)
				.slice(-8); // 最近8条消息（4轮对话）

			historyMessages.forEach((msg) => {
				messages.push({
					role: msg.role,
					content: msg.content,
				});
			});

			return messages;
		},

		/**
		 * 处理流式消息
		 * @param {Object} data 消息数据
		 */
		handleStreamMessage(data) {
			console.log("📨 收到流式消息:", data);

			if (data && this.currentAIMessage) {
				// 检查是否是结束标识，如果是则忽略显示
				if (
					data.content === "[DONE]" ||
					data.content === "data: [DONE]" ||
					data.content?.includes("[DONE]")
				) {
					console.log("🏁 检测到结束标识 [DONE]，忽略显示");
					this.currentAIMessage.isStreaming = false;
					const messageIndex = this.chatMessages.findIndex(
						(msg) => msg.id === this.currentAIMessage.id
					);
					if (messageIndex !== -1) {
						this.$set(this.chatMessages, messageIndex, {
							...this.currentAIMessage,
						});
					}
					return;
				}

				// 处理reasoning_content（思考过程）
				if (
					data.reasoningContent &&
					typeof data.reasoningContent === "string" &&
					data.reasoningContent.length > 0
				) {
					// 获取对应的ChatItem组件引用
					const messageIndex = this.chatMessages.findIndex(
						(msg) => msg.id === this.currentAIMessage.id
					);
					if (messageIndex !== -1) {
						// 通过$refs获取ChatItem组件并调用updateReasoningContent方法
						this.$nextTick(() => {
							const chatItemRefs = this.$refs[`chatItem_${this.currentAIMessage.id}`];
							if (chatItemRefs && chatItemRefs[0]) {
								chatItemRefs[0].updateReasoningContent(data.reasoningContent);
							}
						});
					}
				}

				// 只有当content不为空且不是结束标识时才累加
				if (
					data.content &&
					typeof data.content === "string" &&
					data.content.length > 0
				) {
					this.currentAIMessage.content += data.content;

					// 更新消息列表中的对应项
					const messageIndex = this.chatMessages.findIndex(
						(msg) => msg.id === this.currentAIMessage.id
					);
					if (messageIndex !== -1) {
						this.$set(this.chatMessages, messageIndex, {
							...this.currentAIMessage,
						});
					}

					// 流式更新时正常滚动
					this.scrollToBottom();
				}

				// 检查是否完成
				if (data.isFinished) {
					console.log("✅ 流式消息完成");
					this.currentAIMessage.isStreaming = false;
					// 更新最终状态
					const messageIndex = this.chatMessages.findIndex(
						(msg) => msg.id === this.currentAIMessage.id
					);
					if (messageIndex !== -1) {
						this.$set(this.chatMessages, messageIndex, {
							...this.currentAIMessage,
						});
					}

					// 流式消息完成后，等待150ms再滚动到底部
					setTimeout(() => {
						this.scrollToBottom();
						console.log("🎯 流式消息完成150ms后滚动");
					}, 150);
				}
			}
		},

		/**
		 * 处理流式连接打开
		 * @param {Object} response 响应对象
		 */
		handleStreamOpen(response) {
			this.isSSEConnected = true;
			console.log("✅ SSE流式连接已建立");
		},

		/**
		 * 处理流式连接错误
		 * @param {Object} error 错误对象
		 */
		handleStreamError(error) {
			this.isAITyping = false;
			this.isSSEConnected = false;

			if (this.currentAIMessage) {
				this.currentAIMessage.content =
					"抱歉，网络连接出现问题，请稍后重试。";
				this.currentAIMessage.isStreaming = false;
			}

			console.error("❌ SSE流式连接错误:", error);
			uni.showToast({
				title: "网络连接失败",
				icon: "none",
				duration: 2000,
			});

			// 错误时等待150ms后滚动到底部显示错误消息
			setTimeout(() => {
				this.scrollToBottom();
				console.log("🎯 错误处理150ms后滚动");
			}, 150);
		},

		/**
		 * 处理流式连接完成
		 */
		handleStreamFinish() {
			this.isAITyping = false;
			this.isSSEConnected = false;

			if (this.currentAIMessage) {
				this.currentAIMessage.isStreaming = false;
			}

			this.currentAIMessage = null;
			console.log("✅ SSE流式连接完成");

			// SSE连接完成后，等待150ms再滚动到底部
			setTimeout(() => {
				this.scrollToBottom();
				console.log("🎯 SSE连接完成150ms后滚动");
			}, 150);
		},

		/**
		 * SSE组件事件处理器
		 */
		handleSSEOpen(response) {
			console.log("🔗 SSE组件连接打开:", response);
		},

		handleSSEMessage(data) {
			console.log("📨 SSE组件收到消息:", data);
		},

		handleSSEError(error) {
			console.log("❌ SSE组件连接错误:", error);
		},

		handleSSEFinish() {
			console.log("✅ SSE组件连接完成");
		},

		/**
		 * 处理语音消息
		 * @param {Object} voiceResult 语音结果
		 */
		handleVoiceMessage(voiceResult) {
			// 调用语音识别API处理
			this.processVoiceMessage(voiceResult);
		},

		/**
		 * 处理语音开始
		 */
		handleVoiceStart() {
			console.log("语音录制开始");
		},

		/**
		 * 处理语音取消
		 */
		handleVoiceCancel() {
			console.log("语音录制取消");
		},

		/**
		 * 处理语音错误
		 * @param {Object} error 错误信息
		 */
		handleVoiceError(error) {
			console.error("语音录制错误:", error);
		},

		/**
		 * 处理Chat模式变化
		 * @param {String} mode 当前激活的模式：'search' | 'think' | null
		 */
		handleModeChange(mode) {
			console.log('🎯 Chat模式变化:', mode);

			// 这里可以根据模式做一些处理
			// 比如显示不同的提示信息
			if (mode === 'search') {
				console.log('💡 已启用联网搜索模式');
			} else if (mode === 'think') {
				console.log('💡 已启用深度思考模式');
			} else {
				console.log('💡 已取消特殊模式');
			}
		},

		/**
		 * 处理语音消息
		 * @param {Object} voiceResult 语音结果
		 */
		processVoiceMessage(voiceResult) {
			// TODO: 集成真实的语音识别API
			uni.showToast({
				title: "语音识别功能待开发",
				icon: "none",
				duration: 2000,
			});
		},

		/**
		 * 生成消息ID
		 * @returns {String} 消息ID
		 */
		generateMessageId() {
			return (
				Date.now().toString() + Math.random().toString(36).substr(2, 9)
			);
		},

		/**
		 * 滚动到底部
		 */
		scrollToBottom() {
			this.$nextTick(() => {
				// const query = uni.createSelectorQuery().in(this)
				// query.select('.chat-messages').boundingClientRect()
				// query.selectAll('.chat-messages .message-wrapper').boundingClientRect()
				// query.exec(res => {
				//   const scrollViewHeight = res[0].height
				//   const scrollContentHeight = res[1].reduce((total, item) => total + item.height, 0)
				//   const scrollTop = scrollContentHeight - scrollViewHeight
				//   console.log("🚀 -> scrollToBottom -> 设置scrollTop:", this.scrollTop)
				//   this.scrollTop = scrollTop + 1000000
				// })

        this.scrollIntoView = 'scroll-bottom-anchor'
        setTimeout(() => {
			    this.scrollIntoView = ''
			  }, 100)
			});
			// this.$nextTick(() => {
			//   // 方法1：使用scroll-into-view滚动到底部锚点（最精确）
			//   // this.scrollIntoView = 'scroll-bottom-anchor'
			//   console.log("🚀 -> scrollToBottom -> 使用scroll-into-view")

			//   // 方法2：同时使用scroll-top作为备用（防止scroll-into-view失效）
			//   this.scrollTop = 999999
			//   console.log("🚀 -> scrollToBottom -> 设置scrollTop:", this.scrollTop)

			//   // 清除scroll-into-view，避免影响下次滚动
			//   setTimeout(() => {
			//     this.scrollIntoView = ''
			//   }, 1)
			// })
		},

		/**
		 * 判断是否显示时间
		 * @param {Object} message 当前消息
		 * @param {Number} index 消息索引
		 * @returns {Boolean} 是否显示时间
		 */
		shouldShowTime(message, index) {
			if (index === 0) return true; // 第一条消息总是显示时间

			const prevMessage = this.chatMessages[index - 1];
			if (!prevMessage) return true;

			// 如果与上一条消息时间间隔超过5分钟，显示时间
			const timeDiff = message.timestamp - prevMessage.timestamp;
			return timeDiff > 5 * 60 * 1000;
		},

		/**
		 * 处理编辑消息（已由ChatItem内部处理）
		 * @param {Object} message 要编辑的消息
		 */
		handleEditMessage(message) {
			console.log('📝 编辑消息:', message);
			// 编辑功能已由ChatItem组件内部处理，这里保留兼容性
		},

		/**
		 * 处理编辑保存
		 * @param {Object} data 包含原消息和新内容的对象
		 */
		handleEditSave(data) {
			console.log('💾 保存编辑:', data);
			const { message, newContent } = data;

			// 找到消息在列表中的位置
			const messageIndex = this.chatMessages.findIndex(msg => msg.id === message.id);
			if (messageIndex !== -1) {
				// 更新消息内容
				this.$set(this.chatMessages, messageIndex, {
					...this.chatMessages[messageIndex],
					content: newContent,
					timestamp: Date.now() // 更新时间戳
				});

				uni.showToast({
					title: '消息已保存',
					icon: 'success',
					duration: 1500
				});
			}
		},

		/**
		 * 处理编辑后重新发起
		 * @param {Object} data 包含原消息和新内容的对象
		 */
		handleEditResend(data) {
			console.log('🔄 编辑后重新发起:', data);
			const { message, newContent } = data;

			// 找到消息在列表中的位置
			const messageIndex = this.chatMessages.findIndex(msg => msg.id === message.id);
			if (messageIndex !== -1) {
				// 删除该消息之后的所有消息（包括AI回复）
				this.chatMessages.splice(messageIndex + 1);

				// 更新当前消息内容
				this.$set(this.chatMessages, messageIndex, {
					...this.chatMessages[messageIndex],
					content: newContent,
					timestamp: Date.now()
				});

				// 直接发送SSE请求
				this.sendSSERequest(newContent);
			}
		},

		/**
		 * 处理重新发起消息
		 * @param {Object} message 要重新发起的消息
		 */
		handleResendMessage(message) {
			console.log('🔄 重新发起消息:', message);

			if (message.role === 'user') {
				// 用户消息重新发起：在当前位置重新发起
				// 找到当前消息的位置
				const messageIndex = this.chatMessages.findIndex(msg => msg.id === message.id);

				// 删除从当前用户消息之后的所有消息（不包括当前消息本身）
				if (messageIndex !== -1) {
					this.chatMessages.splice(messageIndex + 1);
				}

				// 直接发送SSE请求，不要再添加用户消息（因为已经存在了）
				this.sendSSERequest(message.content);
			}
		},

		/**
		 * 处理搜索对话历史
		 */
		handleSearchHistory() {
			this.showMenuPopup = false;
			console.log('🔍 搜索对话历史');
			uni.showToast({
				title: '搜索功能开发中',
				icon: 'none',
				duration: 2000
			});
		},

		/**
		 * 处理清空对话
		 */
		async handleClearChat() {
			this.showMenuPopup = false;
			console.log('🗑️ 清空对话');
      if (await this.$confirm1(`确定要清空所有对话记录吗？此操作不可恢复。`) == 1) {
        this.chatMessages = []
        uni.showToast({
          title: '对话已清空',
          icon: 'success',
          duration: 1500
        })
      }
		},

		/**
		 * 处理设置
		 */
		handleSettings() {
			this.showMenuPopup = false;
			this.showSettingsPopup = true;
			console.log('⚙️ 打开会话设置');
		},

		/**
		 * 加载会话设置
		 */
		loadChatSettings() {
			try {
				const savedSettings = uni.getStorageSync('chatSettings');
				if (savedSettings) {
					this.chatSettings = { ...this.chatSettings, ...savedSettings };
					console.log('📖 已加载会话设置:', this.chatSettings);
				}
			} catch (error) {
				console.error('❌ 加载会话设置失败:', error);
			}
		},

		/**
		 * 处理设置保存
		 * @param {Object} settings 新的设置数据
		 */
		handleSettingsSave(settings) {
			this.chatSettings = { ...settings };
			this.showSettingsPopup = false;
			console.log('💾 会话设置已更新:', this.chatSettings);

			// 持久化设置
			try {
				uni.setStorageSync('chatSettings', this.chatSettings);
			} catch (error) {
				console.error('❌ 保存会话设置失败:', error);
			}
		},

		/**
		 * 处理功能卡片点击
		 * @param {String} type 功能类型
		 */
		handleFeatureCardTap(type) {
			console.log('🎯 功能卡片点击:', type);

			// 添加触觉反馈
			uni.vibrateShort({
				type: 'light'
			});

			if (type === 'chat') {
				// 智能对话卡片点击 - 聚焦到输入框
				this.$nextTick(() => {
					// 通过事件通知底部输入组件聚焦
					uni.$emit('focusInput');
				});

				uni.showToast({
					title: '请在下方输入框开始对话',
					icon: 'none',
					duration: 2000
				});
			} else if (type === 'voice') {
				// 语音交流卡片点击 - 触发语音录制
				this.$nextTick(() => {
					// 通过事件通知底部输入组件开始语音录制
					uni.$emit('startVoiceRecord');
				});

				uni.showToast({
					title: '请长按麦克风按钮开始录音',
					icon: 'none',
					duration: 2000
				});
			}
		},
	},
};
</script>

<style lang="scss" scoped>
@import "./chat.scss";
</style>
