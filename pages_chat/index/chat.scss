.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100rpx);
  position: relative;

  .chat-messages {
    flex: 1;
    padding: 20rpx;
    padding-bottom: 100rpx; // 为固定输入框留出空间
    height: calc(100vh - 130rpx);

    .chat-welcome {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 60vh;
      padding: 120rpx 40rpx;
      text-align: center;
      overflow: hidden; // 隐藏超出边界的装饰元素

      // 主要内容区域
      .welcome-main {
        margin-bottom: 80rpx;

        .welcome-avatar {
          margin-bottom: 40rpx;

          .avatar-container {
            position: relative;
            width: 140rpx;
            height: 140rpx;
            margin: 0 auto;

            // 背景光晕
            .avatar-bg {
              position: absolute;
              top: -10rpx;
              left: -10rpx;
              right: -10rpx;
              bottom: -10rpx;
              background: linear-gradient(135deg, rgba(24, 144, 255, 0.3), rgba(64, 169, 255, 0.2));
              border-radius: 50%;
              animation: breathe 3s ease-in-out infinite;
            }

            // 主头像
            .avatar-icon {
              position: relative;
              width: 140rpx;
              height: 140rpx;
              background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow:
                0 8rpx 32rpx rgba(24, 144, 255, 0.4),
                0 4rpx 16rpx rgba(24, 144, 255, 0.3);
              z-index: 2;

              .icon-text {
                font-size: 48rpx;
                font-weight: 700;
                color: white;
                text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
              }
            }

            // 水波纹扩散效果
            .avatar-pulse {
              position: absolute;
              top: -20rpx;
              left: -20rpx;
              right: -20rpx;
              bottom: -20rpx;
              border-radius: 50%;
              z-index: 1;

              // 创建多层水波纹
              &::before,
              &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 100%;
                height: 100%;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(24, 144, 255, 0.2) 0%, rgba(24, 144, 255, 0.08) 60%, transparent 85%);
                border: 1rpx solid rgba(24, 144, 255, 0.3);
                transform: translate(-50%, -50%);
                animation: ripple-expand 4s infinite cubic-bezier(0.25, 0.46, 0.45, 0.94);
              }

              &::after {
                animation-delay: 2s;
                background: radial-gradient(circle, rgba(64, 169, 255, 0.15) 0%, rgba(64, 169, 255, 0.05) 60%, transparent 85%);
                border-color: rgba(64, 169, 255, 0.25);
              }
            }
          }
        }

        .welcome-content {
          .welcome-title {
            font-size: 40rpx;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 16rpx;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .welcome-subtitle {
            font-size: 28rpx;
            color: #7f8c8d;
            line-height: 1.5;
            opacity: 0.8;
          }
        }
      }

      // 功能卡片 - 有色渐变设计
      .feature-cards {
        width: 100%;
        margin-bottom: 60rpx;

        .feature-card {
          display: flex;
          align-items: center;
          border-radius: 20rpx;
          padding: 32rpx 36rpx;
          margin-bottom: 20rpx;
          position: relative;
          overflow: hidden;
          opacity: 0;
          transform: translateY(30rpx) scale(0.95);
          animation: cardSlideUp 0.6s ease-out forwards;

          // 渐进式动画延迟
          &:nth-child(1) { animation-delay: 0.2s; }
          &:nth-child(2) { animation-delay: 0.4s; }

          &:last-child {
            margin-bottom: 0;
          }

          // 智能对话卡片 - 科技蓝色渐变
          &:nth-child(1) {
            background: linear-gradient(135deg,
              rgba(24, 144, 255, 0.12) 0%,
              rgba(64, 169, 255, 0.08) 50%,
              rgba(24, 144, 255, 0.06) 100%);
            border: 1rpx solid rgba(24, 144, 255, 0.15);
            box-shadow:
              0 8rpx 32rpx rgba(24, 144, 255, 0.12),
              0 4rpx 16rpx rgba(24, 144, 255, 0.08),
              0 2rpx 8rpx rgba(24, 144, 255, 0.04);

            &:active {
              transform: translateY(-4rpx) scale(1.02);
              background: linear-gradient(135deg,
                rgba(24, 144, 255, 0.18) 0%,
                rgba(64, 169, 255, 0.12) 50%,
                rgba(24, 144, 255, 0.10) 100%);
              box-shadow:
                0 16rpx 48rpx rgba(24, 144, 255, 0.18),
                0 8rpx 24rpx rgba(24, 144, 255, 0.12),
                0 4rpx 12rpx rgba(24, 144, 255, 0.08);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
          }

          // 语音交流卡片 - 蓝色渐变
          &:nth-child(2) {
            background: linear-gradient(135deg,
              rgba(33, 150, 243, 0.12) 0%,
              rgba(103, 58, 183, 0.08) 50%,
              rgba(33, 150, 243, 0.06) 100%);
            border: 1rpx solid rgba(33, 150, 243, 0.15);
            box-shadow:
              0 8rpx 32rpx rgba(33, 150, 243, 0.12),
              0 4rpx 16rpx rgba(33, 150, 243, 0.08),
              0 2rpx 8rpx rgba(33, 150, 243, 0.04);

            &:active {
              transform: translateY(-4rpx) scale(1.02);
              background: linear-gradient(135deg,
                rgba(33, 150, 243, 0.18) 0%,
                rgba(103, 58, 183, 0.12) 50%,
                rgba(33, 150, 243, 0.10) 100%);
              box-shadow:
                0 16rpx 48rpx rgba(33, 150, 243, 0.18),
                0 8rpx 24rpx rgba(33, 150, 243, 0.12),
                0 4rpx 12rpx rgba(33, 150, 243, 0.08);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
          }

          // 背景光效
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
              transparent,
              rgba(255, 255, 255, 0.3),
              transparent);
            transition: left 0.6s ease;
          }

          &:active::before {
            left: 100%;
          }

          // 右侧装饰渐变 - 根据卡片类型设置不同颜色
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4rpx;
            height: 100%;
            border-radius: 0 20rpx 20rpx 0;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:nth-child(1)::after {
            background: linear-gradient(180deg,
              rgba(24, 144, 255, 0.6) 0%,
              rgba(64, 169, 255, 0.4) 100%);
          }

          &:nth-child(2)::after {
            background: linear-gradient(180deg,
              rgba(33, 150, 243, 0.6) 0%,
              rgba(103, 58, 183, 0.4) 100%);
          }

          &:active::after {
            opacity: 1;
          }

          .card-icon {
            width: 88rpx;
            height: 88rpx;
            border-radius: 18rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36rpx;
            margin-right: 28rpx;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10rpx);

            // 统一的图标容器阴影
            box-shadow:
              0 4rpx 16rpx rgba(0, 0, 0, 0.08),
              0 2rpx 8rpx rgba(0, 0, 0, 0.04),
              inset 0 1rpx 0 rgba(255, 255, 255, 0.9);

            // 图标背景动画
            &::before {
              content: '';
              position: absolute;
              top: -50%;
              left: -50%;
              width: 200%;
              height: 200%;
              background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.6) 50%,
                transparent 70%);
              transform: rotate(-45deg);
              transition: transform 0.6s ease;
            }

            // 悬浮时图标动画
            .feature-card:active & {
              transform: scale(1.1) rotate(5deg);
              background: rgba(255, 255, 255, 0.95);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

              &::before {
                transform: rotate(-45deg) translate(100%, 100%);
              }
            }
          }

          .card-content {
            flex: 1;
            text-align: left;
            position: relative;
            z-index: 2;

            .card-title {
              font-size: 32rpx;
              font-weight: 700;
              color: #2c3e50;
              margin-bottom: 12rpx;
              background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              letter-spacing: 0.5rpx;
              transition: all 0.3s ease;
            }

            .card-desc {
              font-size: 26rpx;
              color: #7f8c8d;
              line-height: 1.5;
              opacity: 0.85;
              transition: all 0.3s ease;
            }

            // 悬浮时文字效果
            .feature-card:active & {
              .card-title {
                transform: translateX(4rpx);
              }

              .card-desc {
                transform: translateX(4rpx);
                opacity: 1;
                color: #5a6c7d;
              }
            }

            // 根据卡片类型设置标题颜色
            .feature-card:nth-child(1) & .card-title {
              background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            .feature-card:nth-child(2) & .card-title {
              background: linear-gradient(135deg, #2196F3 0%, #42A5F5 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }

      // 开始提示
      .start-hint {
        opacity: 0.6;
        animation: float 2s ease-in-out infinite;

        .hint-text {
          font-size: 24rpx;
          color: #95a5a6;
          margin-bottom: 12rpx;
        }

        .hint-arrow {
          font-size: 32rpx;
          color: #1890ff;
          animation: bounce 1.5s ease-in-out infinite;
        }
      }
    }

    // 动画定义
    @keyframes breathe {
      0%, 100% {
        transform: scale(1);
        opacity: 0.6;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.8;
      }
    }

    // 柔和的水波纹扩散动画
    @keyframes ripple-expand {
      0% {
        width: 100%;
        height: 100%;
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
      }
      10% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
      }
      25% {
        width: 130%;
        height: 130%;
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
      }
      50% {
        width: 180%;
        height: 180%;
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1);
      }
      75% {
        width: 230%;
        height: 230%;
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1);
      }
      90% {
        width: 260%;
        height: 260%;
        opacity: 0.05;
        transform: translate(-50%, -50%) scale(1);
      }
      100% {
        width: 280%;
        height: 280%;
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
      }
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-8rpx);
      }
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-8rpx);
      }
      60% {
        transform: translateY(-4rpx);
      }
    }

    // 功能卡片动画
    @keyframes cardSlideUp {
      0% {
        opacity: 0;
        transform: translateY(30rpx) scale(0.95);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .message-wrapper {
      margin-bottom: 0; // chatItem组件内部已有margin
    }
  }

  // 右上角菜单弹窗样式
  .menu-popup {
    background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
    padding: 32rpx 0 40rpx;
    width: 100vw;
    position: relative;
    opacity: 0;
    transform: translateY(20rpx);
    animation: menuSlideUp 0.2s ease-out forwards;

    &.menu-enter {
      animation-delay: 0.05s;
    }

    // 增强阴影和边框
    box-shadow:
      0 -8rpx 32rpx rgba(0, 0, 0, 0.12),
      0 -4rpx 16rpx rgba(0, 0, 0, 0.08),
      0 -2rpx 8rpx rgba(0, 0, 0, 0.04);
    border-top: 1rpx solid rgba(0, 0, 0, 0.06);

    // 顶部装饰条
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80rpx;
      height: 6rpx;
      background: linear-gradient(90deg, #e0e0e0, #bdbdbd, #e0e0e0);
      border-radius: 3rpx;
    }

    // 顶部高光
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 28rpx 40rpx;
      margin: 0 20rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;
      position: relative;
      opacity: 0;
      transform: translateX(-20rpx);
      animation: menuItemSlideIn 0.3s ease-out forwards;

      &:nth-child(1) { animation-delay: 0.1s; }
      &:nth-child(2) { animation-delay: 0.15s; }
      &:nth-child(3) { animation-delay: 0.2s; }
      &:nth-child(4) { animation-delay: 0.25s; }

      &:active {
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.08), rgba(24, 144, 255, 0.04));
        transform: scale(0.98);
      }

      &:hover {
        background: rgba(0, 0, 0, 0.02);
      }

      .menu-icon {
        font-size: 32rpx;
        margin-right: 28rpx;
        width: 44rpx;
        text-align: center;
        color: #666;
        filter: grayscale(0.2);
      }

      .menu-text {
        font-size: 32rpx;
        color: #2c3e50;
        flex: 1;
        font-weight: 500;
        letter-spacing: 0.5rpx;
      }

      // 右侧箭头指示
      &::after {
        content: '›';
        font-size: 36rpx;
        color: #bdc3c7;
        font-weight: 400;
        opacity: 0.6;
      }
    }

    // 移除分割线，改用间距
    .menu-item:not(:last-child) {
      margin-bottom: 8rpx;
    }

    // 为每个菜单项添加微妙的分组效果
    .menu-item:first-child {
      margin-top: 16rpx;
    }

    .menu-item:last-child {
      margin-bottom: 8rpx;
    }
  }

  // 菜单入场动画
  @keyframes menuSlideUp {
    0% {
      opacity: 0;
      transform: translateY(30rpx) scale(0.95);
    }
    60% {
      opacity: 0.8;
      transform: translateY(-2rpx) scale(1.01);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  // 菜单项入场动画
  @keyframes menuItemSlideIn {
    0% {
      opacity: 0;
      transform: translateX(-20rpx) scale(0.9);
    }
    60% {
      opacity: 0.8;
      transform: translateX(2rpx) scale(1.02);
    }
    100% {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }

  // 科技装饰元素
  .tech-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 0;

    // 浮动粒子
    .floating-particles {
      position: absolute;
      width: 100%;
      height: 100%;

      .particle {
        position: absolute;
        width: 6rpx;
        height: 6rpx;
        background: linear-gradient(45deg, #1890ff, #40a9ff);
        border-radius: 50%;
        opacity: 0.6;
        animation: float-particle 8s infinite ease-in-out;

        &.particle-1 {
          top: 15%;
          left: 10%;
          animation-delay: 0s;
          animation-duration: 6s;
        }

        &.particle-2 {
          top: 25%;
          right: 15%;
          animation-delay: 1s;
          animation-duration: 8s;
        }

        &.particle-3 {
          top: 45%;
          left: 8%;
          animation-delay: 2s;
          animation-duration: 7s;
        }

        &.particle-4 {
          top: 60%;
          right: 12%;
          animation-delay: 3s;
          animation-duration: 9s;
        }

        &.particle-5 {
          top: 75%;
          left: 20%;
          animation-delay: 4s;
          animation-duration: 6s;
        }

        &.particle-6 {
          top: 35%;
          right: 25%;
          animation-delay: 5s;
          animation-duration: 8s;
        }
      }
    }



    // 光效装饰
    .light-effects {
      position: absolute;
      width: 100%;
      height: 100%;

      .light-beam {
        position: absolute;
        background: linear-gradient(45deg, transparent, rgba(24, 144, 255, 0.2), transparent);
        opacity: 0.4;
        animation: beam-sweep 12s infinite ease-in-out;

        &.light-beam-1 {
          top: 10%;
          left: -20%;
          width: 200rpx;
          height: 2rpx;
          transform: rotate(15deg);
          animation-delay: 0s;
        }

        &.light-beam-2 {
          bottom: 20%;
          right: -20%;
          width: 150rpx;
          height: 1rpx;
          transform: rotate(-25deg);
          animation-delay: 6s;
        }
      }


    }
  }

  // 科技装饰动画关键帧
  @keyframes float-particle {
    0%, 100% {
      transform: translateY(0) translateX(0);
      opacity: 0.6;
    }
    25% {
      transform: translateY(-20rpx) translateX(10rpx);
      opacity: 0.8;
    }
    50% {
      transform: translateY(-10rpx) translateX(-15rpx);
      opacity: 0.4;
    }
    75% {
      transform: translateY(-30rpx) translateX(5rpx);
      opacity: 0.7;
    }
  }



  @keyframes beam-sweep {
    0%, 100% {
      opacity: 0;
      transform: translateX(-100rpx);
    }
    50% {
      opacity: 0.4;
      transform: translateX(100rpx);
    }
  }


}