# ChatItem 组件使用说明

## 功能特性

1. **思考过程显示**: 支持 `<think>` 标签和 `reasoning_content` 字段，带动画展开收缩
2. **图片点击放大**: 支持点击图片进行预览放大
3. **流式渲染**: 支持流式返回内容的实时显示

## 使用方法

### 基本使用

```vue
<ChatItem
  :message="message"
  :showTime="true"
  :userAvatarText="'我'"
  ref="chatItem"
/>
```

### 处理流式返回

#### 方式1: think标签格式
```javascript
handleStreamData(chunk) {
  const data = JSON.parse(chunk.replace('data: ', ''))
  const delta = data.choices[0].delta

  if (delta.content) {
    this.message.content += delta.content
  }
}
```

#### 方式2: reasoning_content格式 (deepseek-r1)
```javascript
// 使用封装的API，会自动提取reasoning_content
handleStreamMessage(messageData) {
  // messageData现在包含 content 和 reasoningContent 两个字段

  // 处理思考过程
  if (messageData.reasoningContent) {
    this.$refs.chatItem.updateReasoningContent(messageData.reasoningContent)
  }

  // 处理回复内容
  if (messageData.content) {
    this.message.content += messageData.content
  }
}

// 在开始新对话时，记得清空之前的reasoning_content
startNewMessage() {
  this.$refs.chatItem.clearReasoningContent()
  this.message.content = ''
}
```

**注意**: API已经修复，现在会正确提取`reasoning_content`字段并包含在返回的消息数据中。

## 支持格式

1. **think标签**: `<think>思考内容</think>`
2. **Unicode编码**: `\u003cthink\u003e思考内容\u003c/think\u003e`
3. **reasoning_content字段**: 通过组件方法设置

## 组件方法

- `updateReasoningContent(text)`: 累积更新reasoning_content内容（用于流式返回）
- `setReasoningContent(text)`: 设置reasoning_content完整内容（替换模式）
- `clearReasoningContent()`: 清空reasoning_content内容

## 特性

- 思考内容默认折叠，点击展开/收起
- 平滑的动画过渡效果
- 自动清理换行符（首尾trim，连续\n\n转为\n）
- 支持图片点击预览
