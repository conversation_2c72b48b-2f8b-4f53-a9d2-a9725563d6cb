<!--
 * @Description: 会话设置组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27 15:30:00
-->

<template>
  <view class="chat-settings">
    <!-- 设置标题 -->
    <view class="settings-header">
      <view class="header-title">会话设置</view>
      <view class="header-subtitle">自定义您的对话体验</view>
    </view>

    <!-- 设置内容 - 固定高度可滚动 -->
    <scroll-view class="settings-content" scroll-y="true">
      <!-- 会话记忆设置 -->
      <view class="setting-section">
        <view class="section-header">
          <view class="section-icon">💭</view>
          <view class="section-info">
            <view class="section-title">无限上下文</view>
            <view class="section-desc">开启后AI将记住整个会话历史</view>
          </view>
          <u-switch
            v-model="settings.enableMemory"
            :active-color="activeColor"
            :inactive-color="inactiveColor"
            size="20"
            @change="handleMemoryChange"
          />
        </view>
      </view>

      <!-- 最大会话条数设置 -->
      <view class="setting-section">
        <view class="section-header">
          <view class="section-icon">🗂️</view>
          <view class="section-info">
            <view class="section-title">最大提交会话条数</view>
            <view class="section-desc">设置每次提交的最大会话数量：{{ settings.maxSessionCount }}条</view>
          </view>
        </view>
        <view class="slider-container">
          <view class="slider-row">
            <text class="slider-label-left">1条</text>
            <u-slider
              v-model="settings.maxSessionCount"
              step="1"
              min="1"
              max="12"
              :active-color="activeColor"
              :inactive-color="inactiveColor"
              @change="handleSessionCountChange"
              class="slider-middle"
            />
            <text class="slider-label-right">12条</text>
          </view>
        </view>
      </view>

      <!-- 对话模式选择 -->
      <view class="setting-section">
        <view class="section-header">
          <view class="section-icon">⚙️</view>
          <view class="section-info">
            <view class="section-title">对话模式</view>
            <view class="section-desc">选择AI的对话模式</view>
          </view>
        </view>

        <view class="mode-options">
          <view
            class="mode-option"
            :class="{ active: settings.chatMode === 'chat' }"
            @click="handleModeChange('chat')"
          >
            <view class="mode-icon">💬</view>
            <view class="mode-content">
              <view class="mode-title">Chat对话模式</view>
              <view class="mode-desc">纯对话交流，简洁高效</view>
            </view>
            <view class="mode-check" v-if="settings.chatMode === 'chat'">✓</view>
          </view>

          <view
            class="mode-option"
            :class="{ active: settings.chatMode === 'agents' }"
            @click="handleModeChange('agents')"
          >
            <view class="mode-icon">🛠️</view>
            <view class="mode-content">
              <view class="mode-title">Agents模式</view>
              <view class="mode-desc">智能工具辅助，功能丰富</view>
            </view>
            <view class="mode-check" v-if="settings.chatMode === 'agents'">✓</view>
          </view>
        </view>
      </view>

      <!-- Agents功能选择 - 仅在agents模式下显示 -->
      <view class="setting-section" v-if="settings.chatMode === 'agents'">
        <view class="section-header">
          <view class="section-icon">🛠️</view>
          <view class="section-info">
            <view class="section-title">Agents功能</view>
            <view class="section-desc">选择启用的智能工具</view>
          </view>
          <view class="select-all-btn" @click="toggleSelectAll">
            {{ isAllSelected ? '取消全选' : '全选' }}
          </view>
        </view>

        <view class="agents-list">
          <view
            class="agent-item"
            v-for="agent in agentsList"
            :key="agent.key"
            :class="{ active: settings.enabledAgents.includes(agent.key) }"
            @click="toggleAgent(agent.key)"
          >
            <view class="agent-icon">{{ agent.icon }}</view>
            <view class="agent-content">
              <view class="agent-title">{{ agent.title }}</view>
              <view class="agent-desc">{{ agent.desc }}</view>
            </view>
            <view class="agent-check" v-if="settings.enabledAgents.includes(agent.key)">✓</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="settings-footer">
      <view class="footer-btn cancel-btn" @click="handleCancel">取消</view>
      <view class="footer-btn save-btn" @click="handleSave">保存设置</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ChatSettings',

  props: {
    // 当前设置
    currentSettings: {
      type: Object,
      default: () => ({
        enableMemory: true,
        maxSessionCount: 6,
        chatMode: 'chat', // 默认为chat对话模式
        enabledAgents: ['search', 'openUrl', 'doc', 'qa', 'task', 'accounting']
      })
    }
  },

  data() {
    return {
      // 主题色
      activeColor: '#1890ff',
      inactiveColor: '#e0e0e0',

      // 设置数据 - 确保默认值
      settings: {
        enableMemory: true,
        maxSessionCount: 6,
        chatMode: 'chat', // 默认为chat对话模式
        enabledAgents: ['search', 'openUrl', 'doc', 'qa', 'task', 'accounting']
      },

      // Agents列表
      agentsList: [
        {
          key: 'search',
          icon: '🔍',
          title: '搜索工具',
          desc: '网络搜索和信息查询'
        },
        {
          key: 'openUrl',
          icon: '🌐',
          title: '网页访问',
          desc: '打开和分析网页内容'
        },
        {
          key: 'doc',
          icon: '📄',
          title: '知识文档库',
          desc: '知识文档库查询和检索'
        },
        {
          key: 'qa',
          icon: '❓',
          title: '问答系统',
          desc: '智能问答和知识库'
        },
        {
          key: 'task',
          icon: '✅',
          title: '任务管理',
          desc: '任务创建和管理功能'
        },
        {
          key: 'accounting',
          icon: '💰',
          title: '财务助手',
          desc: '财务计算和分析工具'
        }
      ]
    }
  },

  computed: {
    // 是否全选 - 确保数组安全
    isAllSelected() {
      if (!Array.isArray(this.settings.enabledAgents) || !Array.isArray(this.agentsList)) {
        return false;
      }
      return this.settings.enabledAgents.length === this.agentsList.length;
    }
  },

  mounted() {
    // 初始化设置，确保所有字段都有默认值
    this.settings = {
      enableMemory: this.currentSettings.enableMemory !== undefined ? this.currentSettings.enableMemory : true,
      maxSessionCount: this.currentSettings.maxSessionCount || 6,
      chatMode: this.currentSettings.chatMode || 'chat', // 默认为chat对话模式
      enabledAgents: Array.isArray(this.currentSettings.enabledAgents) && this.currentSettings.enabledAgents.length > 0
        ? [...this.currentSettings.enabledAgents]
        : ['search', 'openUrl', 'doc', 'qa', 'task', 'accounting']
    };
  },

  methods: {
    /**
     * 处理会话记忆开关变化
     */
    handleMemoryChange(value) {
      // 确保值不为null
      this.settings.enableMemory = Boolean(value);
      console.log('🧠 会话记忆设置:', this.settings.enableMemory);
    },

    /**
     * 处理最大会话条数变化
     */
    handleSessionCountChange(value) {
      // 确保值在有效范围内
      const count = parseInt(value) || 6;
      this.settings.maxSessionCount = Math.max(1, Math.min(12, count));
      console.log('� 最大会话条数设置:', this.settings.maxSessionCount);
    },

    /**
     * 处理对话模式变化
     */
    handleModeChange(mode) {
      if (mode === 'chat' || mode === 'agents') {
        this.settings.chatMode = mode;
        console.log('🤖 对话模式设置:', this.settings.chatMode);

        // 如果切换到chat模式，可以选择性地清空agents选择
        // 这里保留用户的选择，只是不显示而已

        // 添加触觉反馈
        uni.vibrateShort({
          type: 'light'
        });
      }
    },

    /**
     * 切换Agent功能
     */
    toggleAgent(agentKey) {
      if (!agentKey) return;

      // 确保enabledAgents是数组
      if (!Array.isArray(this.settings.enabledAgents)) {
        this.settings.enabledAgents = [];
      }

      const index = this.settings.enabledAgents.indexOf(agentKey);
      if (index > -1) {
        this.settings.enabledAgents.splice(index, 1);
      } else {
        this.settings.enabledAgents.push(agentKey);
      }
      console.log('🛠️ Agents设置:', this.settings.enabledAgents);
    },

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
      // 确保enabledAgents是数组
      if (!Array.isArray(this.settings.enabledAgents)) {
        this.settings.enabledAgents = [];
      }

      if (this.isAllSelected) {
        this.settings.enabledAgents = [];
      } else {
        this.settings.enabledAgents = this.agentsList.map(agent => agent.key).filter(key => key);
      }
    },

    /**
     * 取消设置
     */
    handleCancel() {
      this.$emit('cancel');
    },

    /**
     * 保存设置
     */
    handleSave() {
      // 最终验证和清理数据
      const finalSettings = {
        enableMemory: Boolean(this.settings.enableMemory),
        maxSessionCount: Math.max(1, Math.min(12, parseInt(this.settings.maxSessionCount) || 6)),
        chatMode: this.settings.chatMode === 'agents' ? 'agents' : 'chat', // 默认为chat模式
        enabledAgents: Array.isArray(this.settings.enabledAgents)
          ? this.settings.enabledAgents.filter(key => key && typeof key === 'string')
          : ['search', 'openUrl', 'doc', 'qa', 'task', 'accounting']
      };

      console.log('💾 保存会话设置:', finalSettings);
      this.$emit('save', finalSettings);

      uni.showToast({
        title: '设置已保存',
        icon: 'success',
        duration: 1500
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-settings {
  background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(20rpx);
  animation: settingsSlideIn 0.3s ease-out forwards;
  overflow: hidden; // 防止整体页面滚动
}

// 设置标题 - 降低高度
.settings-header {
  padding: 30rpx 40rpx 20rpx;
  text-align: center;
  background: white;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0;

  .header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8rpx;
  }

  .header-subtitle {
    font-size: 24rpx;
    color: #7f8c8d;
    opacity: 0.8;
  }
}

// 设置内容 - 中间可滚动区域
.settings-content {
  flex: 1;
  padding: 16rpx 40rpx;
  overflow-y: auto; // 确保可以滚动
  max-height: calc(100vh - 200rpx); // 为头部和底部留出空间

  .setting-section {
    background: white;
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 12rpx;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.03);
    border: 1rpx solid rgba(0, 0, 0, 0.02);

    .section-header {
      display: flex;
      align-items: center;
      min-height: 50rpx;

      .section-icon {
        font-size: 36rpx;
        margin-right: 12rpx;
        flex-shrink: 0;
      }

      .section-info {
        flex: 1;

        .section-title {
          font-size: 28rpx;
          font-weight: 500;
          color: #2c3e50;
          margin-bottom: 2rpx;
          line-height: 1.2;
        }

        .section-desc {
          font-size: 24rpx;
          color: #7f8c8d;
          line-height: 1.2;
        }
      }

      .select-all-btn {
        font-size: 24rpx;
        color: #1890ff;
        padding: 6rpx 12rpx;
        border: 1rpx solid #1890ff;
        border-radius: 16rpx;
        background: rgba(24, 144, 255, 0.05);
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:active {
          background: rgba(24, 144, 255, 0.1);
          transform: scale(0.95);
        }
      }
    }

    // Slider容器样式 - flex布局一行显示
    .slider-container {
      padding: 12rpx 0 6rpx;

      .slider-row {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .slider-label-left,
        .slider-label-right {
          font-size: 20rpx;
          color: #7f8c8d;
          flex-shrink: 0;
          width: 60rpx;
          text-align: center;
        }

        .slider-middle {
          flex: 1;
        }
      }
    }

    // 对话模式选择
    .mode-options {
      padding-top: 8rpx;

      .mode-option {
        display: flex;
        align-items: center;
        padding: 12rpx 16rpx;
        margin-bottom: 8rpx;
        border-radius: 12rpx;
        border: 2rpx solid transparent;
        background: rgba(0, 0, 0, 0.01);
        transition: all 0.3s ease;
        min-height: 56rpx;
        position: relative;
        overflow: hidden;

        &.active {
          border-color: #1890ff;
          background: linear-gradient(135deg,
            rgba(24, 144, 255, 0.08) 0%,
            rgba(24, 144, 255, 0.04) 100%);
          box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15);
        }

        &:last-child {
          margin-bottom: 0;
        }

        &:active {
          transform: scale(0.98);
        }

        // 背景光效
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
          transition: left 0.6s ease;
        }

        &:active::before {
          left: 100%;
        }

        .mode-icon {
          font-size: 32rpx;
          margin-right: 16rpx;
          flex-shrink: 0;
        }

        .mode-content {
          flex: 1;

          .mode-title {
            font-size: 28rpx;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4rpx;
            line-height: 1.2;
          }

          .mode-desc {
            font-size: 24rpx;
            color: #7f8c8d;
            line-height: 1.2;
          }
        }

        .mode-check {
          font-size: 28rpx;
          color: #1890ff;
          font-weight: 700;
          flex-shrink: 0;
        }
      }
    }

    // Agents列表
    .agents-list {
      padding-top: 10rpx;
      .agent-item {
        display: flex;
        align-items: center;
        padding: 12rpx 16rpx;
        margin-bottom: 8rpx;
        border-radius: 8rpx;
        border: 1rpx solid transparent;
        background: rgba(0, 0, 0, 0.01);
        transition: all 0.2s ease;
        min-height: 56rpx;

        &.active {
          border-color: #1890ff;
          background: rgba(24, 144, 255, 0.03);
        }

        &:last-child {
          margin-bottom: 0;
        }

        .agent-icon {
          font-size: 28rpx;
          margin-right: 10rpx;
          flex-shrink: 0;
        }

        .agent-content {
          flex: 1;

          .agent-title {
            font-size: 26rpx;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 1rpx;
            line-height: 1.2;
          }

          .agent-desc {
            font-size: 24rpx;
            color: #95a5a6;
            line-height: 1.2;
          }
        }

        .agent-check {
          font-size: 24rpx;
          color: #1890ff;
          font-weight: 700;
          flex-shrink: 0;
        }
      }
    }
  }
}

// 底部操作按钮 - 美化样式
.settings-footer {
  display: flex;
  padding: 16rpx 40rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  background: white;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  gap: 16rpx;
  flex-shrink: 0;
  position: relative;
  z-index: 10; // 确保按钮在最上层

  .footer-btn {
    flex: 1;
    height: 76rpx;
    border-radius: 38rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &.cancel-btn {
      background: #f8f9fa;
      color: #6c757d;
      border: 1rpx solid #e9ecef;

      &:active {
        background: #e9ecef;
        transform: scale(0.96);
      }
    }

    &.save-btn {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      border: none;
      box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);

      &:active {
        background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
        transform: scale(0.96);
        box-shadow: 0 1rpx 4rpx rgba(24, 144, 255, 0.3);
      }
    }
  }
}

// 设置页面入场动画
@keyframes settingsSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 设置项入场动画
@keyframes sectionSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20rpx) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateX(2rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}
</style>
