<!--
 * @Description: 对话子项组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27 11:35:08
-->

<template>
  <view class="chat-item">
    <!-- 时间显示 -->
    <view v-if="showTime" class="message-time">
      <text>{{ formatTime(message.timestamp) }}</text>
    </view>

    <!-- 用户消息 -->
    <view v-if="message.role === 'user'" class="message-user">
      <view class="message-content user-message">
        <text>{{ message.content }}</text>
      </view>
      <u-avatar
        :src="userAvatarSrc"
        size="38"
        class="user-avatar"
      ></u-avatar>
    </view>

    <!-- 助手消息 -->
    <view v-else-if="message.role === 'assistant'" class="message-assistant" :class="{ 'message-enter': !message.isStreaming }">
      <!-- <view class="message-avatar ai-avatar">
        <text class="user-avatar-text">AI</text>
      </view> -->
      <view class="message-content assistant-message">
        <!-- AI思考中动画 -->
        <view v-if="message.isStreaming && !message.content && !reasoningContent" class="thinking-animation">
          <!-- 选择你喜欢的emoji，删除其他的 -->
          <text class="thinking-emoji">⏳</text>
          <text class="thinking-text">思考中</text>
          <view class="thinking-dots">
            <view class="dot dot1"></view>
            <view class="dot dot2"></view>
            <view class="dot dot3"></view>
          </view>
        </view>

        <!-- 渲染处理后的内容块 -->
        <view v-for="(item, index) in formatDetails(message.content)" :key="index">
          <!-- 思考块处理 -->
          <view v-if="item.isThink" class="think-block">
            <view class="think-summary" @click="toggleThink(index)">
              <text class="think-icon" :class="{ 'think-icon-expanded': isThinkBlockExpanded(index) }">▶</text>
              <text class="think-title">{{ isThinkBlockExpanded(index) ? '隐藏思考过程' : '显示思考过程' }}</text>
            </view>
            <transition name="think-expand">
              <view v-if="isThinkBlockExpanded(index)" class="think-content">
                <text class="think-text">{{ item.content }}</text>
              </view>
            </transition>
          </view>
          <!-- 正常内容块 -->
          <view v-else>
            <f-md :content="item.content" :isStreaming="message.isStreaming || false" @image-click="handleImageClick" />
          </view>
        </view>
      </view>
    </view>

    <!-- AI消息工具条 -->
    <view v-if="message.role === 'assistant' && message.content && !message.isStreaming" class="ai-actions">
      <view class="action-btn" @click="copyContent">
        <text class="action-icon">📋</text>
        <text class="action-text">复制</text>
      </view>
    </view>

    <!-- 用户消息工具条 -->
    <view v-if="message.role === 'user'" class="user-actions">
      <view class="action-btn" @click="editMessage">
        <text class="action-icon">✏️</text>
        <text class="action-text">编辑</text>
      </view>
      <view class="action-btn" @click="copyContent">
        <text class="action-icon">📋</text>
        <text class="action-text">复制</text>
      </view>
      <view class="action-btn" @click="resendMessage">
        <text class="action-icon">🔄</text>
        <text class="action-text">重新发起</text>
      </view>
    </view>

    <!-- 编辑弹窗 -->
    <EditInput
      :show="showEditPopup"
      :content="message.content"
      @cancel="handleEditCancel"
      @save="handleEditSave"
      @resend="handleEditResend"
    />

    <!-- 图片预览弹窗 -->
    <view v-if="showImagePreview" class="image-preview-overlay" @click="closeImagePreview">
      <view class="image-preview-container" @click.stop>
        <image
          :src="previewImageUrl"
          class="preview-image"
          mode="aspectFit"
          @error="closeImagePreview"
        />
        <view class="preview-close-btn" @click="closeImagePreview">
          <text class="close-icon">✕</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import fMd from "@/components/f-md/f-md.vue"
import EditInput from './EditInput.vue'

export default {
  name: 'ChatItem',
  components: {
    fMd,
    EditInput
  },
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        role: 'user', // 'user' | 'assistant'
        content: '',
        timestamp: Date.now(),
        isStreaming: false
      })
    },
    // 是否显示时间
    showTime: {
      type: Boolean,
      default: false
    },
    // 用户头像文字
    userAvatarText: {
      type: String,
      default: '我'
    },
    // 用户头像图片路径
    userAvatarSrc: {
      type: String,
      default: '/static/icon_exp_default.png'
    }
  },

  data() {
    return {
      showEditPopup: false, // 是否显示编辑弹窗
      isThinkExpanded: {}, // 思考块展开状态
      showImagePreview: false, // 是否显示图片预览
      previewImageUrl: '', // 预览图片URL
      reasoningContent: '' // reasoning_content累积内容
    }
  },



  methods: {
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
,

    // 复制内容
    copyContent() {
      uni.setClipboardData({
        data: this.message.content,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
            duration: 1500
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 1500
          })
        }
      })
    },

    // 编辑消息
    editMessage() {
      this.showEditPopup = true
    },

    // 重新发起消息
    async resendMessage() {
      if (await this.$confirm1(`你确定重新发起会话吗？`) != 1) return;
      this.$emit('resend', this.message)
    },

    /**
     * 处理编辑取消
     */
    handleEditCancel() {
      this.showEditPopup = false
    },

    /**
     * 处理编辑保存
     * @param {String} newContent 新的消息内容
     */
    handleEditSave(newContent) {
      this.showEditPopup = false
      this.$emit('edit-save', {
        message: this.message,
        newContent: newContent
      })
    },

    /**
     * 处理编辑后重新发起
     * @param {String} newContent 新的消息内容
     */
    handleEditResend(newContent) {
      this.showEditPopup = false
      this.$emit('edit-resend', {
        message: this.message,
        newContent: newContent
      })
    },

    /**
     * 切换思考块展开状态
     * @param {Number} index 思考块索引
     */
    toggleThink(index) {
      // 如果是第一次点击（未设置状态），默认为展开状态，点击后关闭
      const currentState = this.isThinkExpanded[index] !== undefined ? this.isThinkExpanded[index] : true
      this.$set(this.isThinkExpanded, index, !currentState)
    },

    /**
     * 检查思考块是否展开
     * @param {Number} index 思考块索引
     * @returns {Boolean} 是否展开
     */
    isThinkBlockExpanded(index) {
      // 默认展开状态
      return this.isThinkExpanded[index] !== undefined ? this.isThinkExpanded[index] : true
    },

    /**
     * 格式化消息详情，处理思考标签和代码块
     * @param {String} content 消息内容
     * @returns {Array} 格式化后的内容块数组
     */
    formatDetails(content) {
      if (!content && !this.reasoningContent) return []

      let _content = content || ''
      const blocks = []

      // 如果有reasoning_content，先添加思考块
      if (this.reasoningContent) {
        const cleanReasoningContent = this.reasoningContent.trim().replace(/\n\n+/g, '\n')
        blocks.push({ content: cleanReasoningContent, isThink: true })
      }

      // 处理 <think> 标签（流式处理）
      while (_content.includes('<think>') || _content.includes('\\u003cthink\\u003e')) {
        let start, end, thinkContent

        // 处理标准 <think> 标签
        if (_content.includes('<think>')) {
          start = _content.indexOf('<think>')
          end = _content.indexOf('</think>')

          if (end > start) {
            // 完整标签的情况
            thinkContent = _content.substring(start + 7, end)
            // 清理思考内容的换行符
            thinkContent = thinkContent.trim().replace(/\n\n+/g, '\n')
            blocks.push({ content: thinkContent, isThink: true })
            _content = _content.substring(end + 8)
          } else {
            // 只有开始标签的情况
            thinkContent = _content.substring(start + 7)
            // 清理思考内容的换行符
            thinkContent = thinkContent.trim().replace(/\n\n+/g, '\n')
            blocks.push({ content: thinkContent, isThink: true })
            _content = ''
            break
          }
        }
        // 处理 Unicode 编码的标签
        else if (_content.includes('\\u003cthink\\u003e')) {
          start = _content.indexOf('\\u003cthink\\u003e')
          end = _content.indexOf('\\u003c/think\\u003e')

          if (end > start) {
            // 完整标签的情况
            thinkContent = _content.substring(start + 18, end)
            // 解码Unicode字符并清理换行符
            thinkContent = thinkContent.replace(/\\u003c/g, '<').replace(/\\u003e/g, '>')
            thinkContent = thinkContent.trim().replace(/\n\n+/g, '\n')
            blocks.push({ content: thinkContent, isThink: true })
            _content = _content.substring(end + 19)
          } else {
            // 只有开始标签的情况
            thinkContent = _content.substring(start + 18)
            thinkContent = thinkContent.replace(/\\u003c/g, '<').replace(/\\u003e/g, '>')
            thinkContent = thinkContent.trim().replace(/\n\n+/g, '\n')
            blocks.push({ content: thinkContent, isThink: true })
            _content = ''
            break
          }
        }

        // 如果_content开始有\n 就替换掉
        if (_content.startsWith('\n')) {
          _content = _content.replace(/^\n+/g, '')
        }
      }

      // 处理剩余内容
      if (_content.trim()) {
        blocks.push({ content: _content, isThink: false })
      }

      // 如果没有任何块，返回原内容
      if (blocks.length === 0) {
        blocks.push({ content: content, isThink: false })
      }

      return blocks
    },

    /**
     * 处理图片点击事件
     * @param {String} imageUrl 图片URL
     */
    handleImageClick(imageUrl) {
      this.previewImageUrl = imageUrl
      this.showImagePreview = true
    },

    /**
     * 关闭图片预览
     */
    closeImagePreview() {
      this.showImagePreview = false
      this.previewImageUrl = ''
    },

    /**
     * 更新reasoning_content内容（累积模式）
     * @param {String} reasoningText 推理内容片段
     */
    updateReasoningContent(reasoningText) {
      if (reasoningText) {
        // 累积reasoning_content内容
        this.reasoningContent += reasoningText
        // 强制更新视图
        this.$forceUpdate()
      }
    },

    /**
     * 设置reasoning_content内容（替换模式）
     * @param {String} reasoningText 完整的推理内容
     */
    setReasoningContent(reasoningText) {
      this.reasoningContent = reasoningText || ''
      this.$forceUpdate()
    },

    /**
     * 清空reasoning_content内容
     */
    clearReasoningContent() {
      this.reasoningContent = ''
    }

  }
}
</script>

<style lang="scss" scoped>
.chat-item {
  margin-bottom: 24rpx;

  .message-time {
    text-align: center;
    font-size: 24rpx;
    color: #999;
    margin: 20rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;

    text {
      padding: 8rpx 16rpx;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 12rpx;
      display: inline-block;
    }
  }

  .message-user {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    gap: 12rpx;

    .user-message {
      background-color: #95ec69; // 微信绿色
      color: #333;
      max-width: 80%;
      border-radius: 18rpx 6rpx 18rpx 18rpx;
      box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
    }

    .user-avatar {
      flex-shrink: 0;
    }
  }

  .message-assistant {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16rpx;
    opacity: 0;
    transform: translateY(20rpx);
    animation: messageSlideIn 0.4s ease-out forwards;

    &.message-enter {
      animation-delay: 0.1s;
    }

    .assistant-message {
      background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%); // 微妙的渐变背景
      color: #333;
      width: 90% !important;
      border-radius: 18rpx 18rpx 18rpx 6rpx;
      box-shadow:
        0 2rpx 12rpx rgba(0, 0, 0, 0.06),
        0 1rpx 3rpx rgba(0, 0, 0, 0.04);
      border: 1rpx solid rgba(0, 0, 0, 0.06);
      position: relative;
      overflow: hidden;

      // 添加微妙的左侧渐变装饰
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3rpx;
        background: linear-gradient(180deg,
          rgba(24, 144, 255, 0.1) 0%,
          rgba(24, 144, 255, 0.3) 50%,
          rgba(24, 144, 255, 0.1) 100%);
        border-radius: 0 2rpx 2rpx 0;
        pointer-events: none;
      }

      // 添加微妙的顶部高光效果
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 3rpx;
        right: 0;
        height: 1rpx;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        pointer-events: none;
      }
    }
  }

  .message-content {
    padding: 28rpx 26rpx 28rpx 32rpx; // 左侧稍微多留一点空间给装饰
    border-radius: 18rpx;
    font-size: 32rpx;
    line-height: 1.6;
    word-wrap: break-word;
    position: relative;

    // 针对AI消息的特殊样式
    .message-assistant & {
      color: #2c3e50;
      text-shadow: 0 1rpx 1rpx rgba(255, 255, 255, 0.8);

      // 段落间距优化
      p {
        margin: 0 0 16rpx 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      // 代码块样式优化
      code {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1565c0;
        padding: 4rpx 8rpx;
        border-radius: 6rpx;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 28rpx;
      }

      // 强调文本样式
      strong, b {
        color: #1976d2;
        font-weight: 600;
      }

      // 链接样式
      a {
        color: #1890ff;
        text-decoration: none;
        border-bottom: 1rpx solid rgba(24, 144, 255, 0.3);

        &:active {
          color: #40a9ff;
        }
      }
    }
  }

  .message-avatar {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;

    // AI头像特殊效果
    .message-assistant & {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
      box-shadow:
        0 4rpx 12rpx rgba(24, 144, 255, 0.3),
        0 2rpx 4rpx rgba(24, 144, 255, 0.2);

      // 添加光晕效果
      &::before {
        content: '';
        position: absolute;
        top: -2rpx;
        left: -2rpx;
        right: -2rpx;
        bottom: -2rpx;
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.3), rgba(64, 169, 255, 0.3));
        border-radius: 50%;
        z-index: -1;
        animation: pulse 2s ease-in-out infinite;
      }
    }


  }

  // AI头像脉冲动画
  @keyframes pulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  // 消息入场动画
  @keyframes messageSlideIn {
    0% {
      opacity: 0;
      transform: translateY(20rpx) scale(0.95);
    }
    60% {
      opacity: 0.8;
      transform: translateY(-2rpx) scale(1.02);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  // 工具条样式
  .user-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12rpx;
    margin-top: 6rpx;
    margin-right: 76rpx; // 对齐用户头像
    opacity: 0.7;
  }

  .ai-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12rpx;
    margin-top: 6rpx;
    margin-left: 0;
    opacity: 0.7;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 4rpx;
    padding: 8rpx 12rpx;
    background-color: rgba(0, 0, 0, 0.06);
    border-radius: 16rpx;
    font-size: 20rpx;
    color: #666;
    transition: all 0.2s ease;
    border: 1rpx solid rgba(0, 0, 0, 0.08);

    &:active {
      background-color: rgba(0, 0, 0, 0.12);
      transform: scale(0.96);
    }

    .action-icon {
      font-size: 20rpx;
      line-height: 1;
    }

    .action-text {
      font-size: 20rpx;
      line-height: 1;
    }
  }

  // AI思考中动画样式
  .thinking-animation {
    display: flex;
    align-items: center;
    padding: 8rpx 0; // 减少上下内边距
    margin: 4rpx 0; // 减少外边距

    .thinking-emoji {
      font-size: 28rpx; // 稍微减小emoji大小
      margin-right: 10rpx;
      animation: hourglassFlip 2.4s infinite ease-in-out;
      transform-origin: center;
    }

    .thinking-text {
      font-size: 24rpx;
      margin-right: 14rpx;
      font-weight: 400;
      color: #999999;
    }

    .thinking-dots {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .dot {
        width: 8rpx;
        height: 8rpx;
        border-radius: 50%;
        background: #cccccc;
        animation: thinkingWave 1.8s infinite ease-in-out;
        transform-origin: center;
      }

      .dot1 {
        animation-delay: 0s;
      }

      .dot2 {
        animation-delay: 0.3s;
      }

      .dot3 {
        animation-delay: 0.6s;
      }
    }
  }

  // 沙漏翻转动画 - 转动+停顿
  @keyframes hourglassFlip {
    0%, 20% {
      transform: rotate(0deg);
    }
    25%, 45% {
      transform: rotate(180deg);
    }
    50%, 70% {
      transform: rotate(180deg);
    }
    75%, 95% {
      transform: rotate(360deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 思考点波浪动画
  @keyframes thinkingWave {
    0%, 60%, 100% {
      transform: scale(0.8) translateY(0);
      opacity: 0.5;
    }
    30% {
      transform: scale(1.2) translateY(-6rpx);
      opacity: 1;
    }
  }

  // 思考块样式
  .think-block {
    margin: 16rpx 0;
    border: 1rpx solid rgba(24, 144, 255, 0.2);
    border-radius: 12rpx;
    background: rgba(24, 144, 255, 0.03);
    overflow: hidden;

    .think-summary {
      display: flex;
      align-items: center;
      padding: 16rpx 20rpx;
      background: rgba(24, 144, 255, 0.05);
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        background: rgba(24, 144, 255, 0.1);
      }

      .think-icon {
        font-size: 20rpx;
        color: #1890ff;
        margin-right: 12rpx;
        transition: transform 0.3s ease;
        transform-origin: center;

        &.think-icon-expanded {
          transform: rotate(90deg);
        }
      }

      .think-title {
        font-size: 26rpx;
        color: #1890ff;
        font-weight: 500;
        flex: 1;
      }
    }

    .think-content {
      padding: 20rpx;
      background: rgba(255, 255, 255, 0.8);
      border-top: 1rpx solid rgba(24, 144, 255, 0.1);

      .think-text {
        font-size: 24rpx;
        line-height: 1.6;
        color: #666;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }

  // 思考块展开收缩动画
  .think-expand-enter-active,
  .think-expand-leave-active {
    transition: all 0.2s ease;
    overflow: hidden;
  }

  .think-expand-enter,
  .think-expand-leave-to {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10rpx);
  }

  .think-expand-enter-to,
  .think-expand-leave {
    max-height: 1000rpx;
    opacity: 1;
    transform: translateY(0);
  }

  // 图片预览样式
  .image-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;

    .image-preview-container {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      background: white;
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);

      .preview-image {
        width: 100%;
        height: 100%;
        max-width: 90vw;
        max-height: 90vh;
      }

      .preview-close-btn {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 60rpx;
        height: 60rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 0, 0, 0.8);
          transform: scale(0.9);
        }

        .close-icon {
          color: white;
          font-size: 32rpx;
          font-weight: bold;
        }
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
</style>
