<!--
 * @Description: 对话底部输入组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27 11:35:08
-->

<template>
  <view class="chat-bottom-input">
    <!-- Chat模式功能按钮 - 仅在chat模式下显示 -->
    <view v-if="chatMode === 'chat'" class="chat-mode-buttons">
      <view
        class="mode-btn think-btn"
        :class="{ active: activeMode === 'think' }"
        @click="toggleMode('think')"
      >
        <text class="btn-icon">💭</text>
        <text class="btn-text">深度思考</text>
      </view>
      <view
        class="mode-btn search-btn"
        :class="{ active: activeMode === 'search' }"
        @click="toggleMode('search')"
      >
        <text class="btn-icon">🌐</text>
        <text class="btn-text">联网搜索</text>
      </view>

    </view>

    <!-- 输入区域 - 固定在底部 -->
    <view class="input-container">
      <view class="voice-btn" @click="toggleInputMode">
        <text class="voice-icon">{{ !showVoiceInput ? '🎤' : '✏️' }}</text>
      </view>

      <view class="input-wrapper">
        <textarea
          v-if="!showVoiceInput"
          class="text-input"
          placeholder="输入消息或长按语音..."
          v-model="inputText"
          :focus="shouldFocus"
          @confirm="sendMessage"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
          :auto-height="false"
          :show-confirm-bar="false"
          :cursor-spacing="20"
          :adjust-position="false"
          :style="{ height: textareaHeight + 'rpx' }"
        />

        <c-voice
          v-else
          :min-duration="2"
          :max-duration="10"
          format="mp3"
          record-type="click"
          @start="onVoiceStart"
          @success="onVoiceSuccess"
          @cancel="onVoiceCancel"
          @error="onVoiceError"
        />
      </view>

      <view v-if="!showVoiceInput" class="send-btn" @click="sendMessage" :class="{ disabled: !canSend }">
        <text>发送</text>
      </view>
    </view>
  </view>
</template>

<script>
import cVoice from '@/components/xj-voice/xj-voice.vue'

export default {
  name: 'ChatBottomInput',
  components: {
    cVoice
  },

  props: {
    // 对话模式：'chat' | 'agents'
    chatMode: {
      type: String,
      default: 'chat'
    }
  },
  data() {
    return {
      inputText: '',
      showVoiceInput: false,
      currentVoiceFile: null, // 当前录音文件
      shouldFocus: false, // 是否应该聚焦输入框
      activeMode: null, // 当前激活的模式：'search' | 'think' | null
      textareaHeight: 72 // 动态计算的textarea高度
    }
  },

  computed: {
    canSend() {
      return this.inputText.trim().length > 0 || this.showVoiceInput
    }
  },

  mounted() {
    // 监听功能卡片点击事件
    uni.$on('focusInput', this.handleFocusInput);
    uni.$on('startVoiceRecord', this.handleStartVoiceRecord);
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('focusInput', this.handleFocusInput);
    uni.$off('startVoiceRecord', this.handleStartVoiceRecord);
  },

  methods: {
    // 切换输入模式
    toggleInputMode() {
      this.showVoiceInput = !this.showVoiceInput
      this.inputText = ''
      this.textareaHeight = 72 // 重置为初始高度
    },

    /**
     * 切换Chat模式功能按钮
     * @param {String} mode 模式：'search' | 'think'
     */
    toggleMode(mode) {
      // 如果点击的是当前激活的模式，则取消激活
      if (this.activeMode === mode) {
        this.activeMode = null
      } else {
        // 否则激活新模式（自动取消其他模式）
        this.activeMode = mode
      }

      console.log('🎯 Chat模式切换:', this.activeMode)

      // 触发模式变化事件
      this.$emit('mode-change', this.activeMode)

      // 添加触觉反馈
      uni.vibrateShort({
        type: 'light'
      })
    },

    /**
     * 处理输入框内容变化
     * @param {Object} e 输入事件
     */
    handleInput(e) {
      this.inputText = e.detail.value
      this.calculateTextareaHeight()
    },

    /**
     * 动态计算textarea高度
     */
    calculateTextareaHeight() {
      const text = this.inputText
      if (!text) {
        this.textareaHeight = 72
        return
      }

      // 计算行数
      const lines = text.split('\n')
      let totalLines = 0

      lines.forEach(line => {
        if (line.length === 0) {
          totalLines += 1
        } else {
          // 估算每行能容纳的字符数（考虑中英文混合）
          const charsPerLine = 20 // 根据实际字体大小和容器宽度调整
          const lineCount = Math.ceil(line.length / charsPerLine)
          totalLines += Math.max(1, lineCount)
        }
      })

      // 计算高度：基础高度 + 行数 * 行高
      const baseHeight = 72 // 基础高度
      const lineHeight = 32 // 每行高度
      const calculatedHeight = baseHeight + (totalLines - 1) * lineHeight

      // 限制最大高度
      this.textareaHeight = Math.min(calculatedHeight, 500)
    },

    /**
     * 处理输入框聚焦
     */
    handleFocus() {
      this.shouldFocus = false
      console.log('📝 输入框聚焦')
    },

    /**
     * 处理输入框失焦
     */
    handleBlur() {
      console.log('📝 输入框失焦')
    },

    /**
     * 录音开始事件
     */
    onVoiceStart() {
      console.log('开始录音')
      uni.showToast({
        title: '开始录音',
        icon: 'none',
        duration: 1000
      })
      this.$emit('voice-start')
    },

    /**
     * 录音成功事件
     * @param {Object} result 录音结果
     */
    onVoiceSuccess(result) {
      console.log('录音成功', result)
      this.currentVoiceFile = result

      uni.showToast({
        title: '录音完成',
        icon: 'success',
        duration: 1000
      })

      // 触发语音消息事件
      this.$emit('voice-message', result)
    },

    /**
     * 录音取消事件
     */
    onVoiceCancel() {
      console.log('录音取消')
      this.currentVoiceFile = null
      this.$emit('voice-cancel')
    },

    /**
     * 录音错误事件
     * @param {Object} error 错误信息
     */
    onVoiceError(error) {
      console.error('录音错误', error)
      this.currentVoiceFile = null

      this.$emit('voice-error', error)
    },

    // 发送消息
    sendMessage() {
      if (!this.canSend) return

      const content = this.inputText.trim()
      if (!content) return

      // 构建消息对象，包含模式信息
      const messageData = {
        content: content,
        mode: this.activeMode, // 当前激活的模式
        chatMode: this.chatMode // 对话模式
      }

      // 触发发送消息事件
      this.$emit('send-message', messageData)

      // 清空输入框并重置高度
      this.inputText = ''
      this.textareaHeight = 72 // 重置为初始高度

      // 发送后清除模式选择（可选）
      // this.activeMode = null
    },

    /**
     * 处理聚焦输入框事件
     */
    handleFocusInput() {
      console.log('📝 聚焦输入框');

      // 切换到文字输入模式
      if (this.showVoiceInput) {
        this.showVoiceInput = false;
      }

      // 聚焦输入框（需要延迟执行）
      this.$nextTick(() => {
        // 在小程序中，需要通过其他方式来聚焦输入框
        // 这里可以通过设置一个标志位来实现
        this.shouldFocus = true;
      });
    },

    /**
     * 处理开始语音录制事件
     */
    handleStartVoiceRecord() {
      console.log('🎤 开始语音录制');

      // 切换到语音输入模式
      if (!this.showVoiceInput) {
        this.showVoiceInput = true;
      }

      // 延迟触发录音（让界面先切换）
      this.$nextTick(() => {
        setTimeout(() => {
          // 这里可以通过ref调用语音组件的开始录音方法
          // 或者显示录音提示
          uni.showToast({
            title: '请长按麦克风开始录音',
            icon: 'none',
            duration: 2000
          });
        }, 300);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-bottom-input {
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
  padding: 16rpx 20rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  // Chat模式功能按钮
  .chat-mode-buttons {
    display: flex;
    gap: 16rpx;
    margin-bottom: 16rpx;
    padding: 0 8rpx;

    .mode-btn {
      display: flex;
      align-items: center;
      gap: 10rpx;
      padding: 12rpx 20rpx;
      border-radius: 24rpx;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1rpx solid rgba(0, 0, 0, 0.08);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      flex-shrink: 0;
      position: relative;
      overflow: hidden;

      // 添加微妙的内阴影
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
        border-radius: 24rpx;
        pointer-events: none;
      }

      .btn-icon {
        font-size: 28rpx;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
      }

      .btn-text {
        font-size: 26rpx;
        color: #495057;
        font-weight: 600;
        position: relative;
        z-index: 1;
        text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
      }

      // 悬停效果
      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      }

      // 点击效果
      &:active {
        transform: translateY(0) scale(0.98);
        transition: all 0.1s ease;
      }
    }

    // 联网搜索按钮专属样式
    .search-btn {
      &.active {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border-color: #1890ff;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
        transform: translateY(-1rpx);

        &::before {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
        }

        .btn-icon {
          filter: drop-shadow(0 1rpx 3rpx rgba(0, 0, 0, 0.2));
        }

        .btn-text {
          color: white;
          text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
        }
      }
    }

    // 深度思考按钮专属样式
    .think-btn {
      &.active {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border-color: #1890ff;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
        transform: translateY(-1rpx);

        &::before {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
        }

        .btn-icon {
          filter: drop-shadow(0 1rpx 3rpx rgba(0, 0, 0, 0.2));
        }

        .btn-text {
          color: white;
          text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  .input-container {
    display: flex;
    align-items: flex-end;
    gap: 16rpx;
    min-height: 72rpx;
    // 移除固定高度，让容器自适应内容高度

    .voice-btn {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      background-color: #f7f7f7;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      flex-shrink: 0;

      .voice-icon {
        font-size: 32rpx;
        color: #666;
      }

      &.recording {
        background-color: #ff4d4f;
        transform: scale(1.1);

        .voice-icon {
          color: white;
        }
      }

      &:active {
        transform: scale(0.95);
        background-color: #e8e8e8;
      }
    }

    .input-wrapper {
      flex: 1;
      min-height: 72rpx;
      display: flex;
      align-items: flex-end; // 底部对齐，适应textarea高度变化
    }

    .text-input {
      width: 100%;
      padding: 20rpx 24rpx;
      border: 1rpx solid #e5e5e5;
      border-radius: 36rpx;
      font-size: 32rpx;
      line-height: 32rpx;
      background-color: #f7f7f7;
      color: #333;
      box-sizing: border-box;
      resize: none;
      word-wrap: break-word;
      word-break: break-all;
      transition: height 0.2s ease; // 高度变化动画

      &::placeholder {
        color: #999;
        font-size: 32rpx;
      }

      &:focus {
        border-color: #07c160;
        background-color: white;
      }
    }



    .send-btn {
      width: 120rpx;
      height: 72rpx;
      border-radius: 36rpx;
      font-size: 32rpx;
      color: white;
      background-color: #07c160;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      flex-shrink: 0;

      &.disabled {
        background-color: #c8c8c8;
        opacity: 0.6;
      }

      &:active:not(.disabled) {
        background-color: #1565c0;
        transform: scale(0.95);
      }
    }
  }
}
</style>
