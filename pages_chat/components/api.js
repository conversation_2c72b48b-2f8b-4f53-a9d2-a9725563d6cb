/**
 * @Description: SSE聊天API封装
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27
 */

import { getConfig } from "@/framework/config/config"

// API配置
const API_CONFIG = {
  baseURL: getConfig().baseURL + '/assistant/chat',
  apiKey: uni.getStorageSync(getConfig().tokenName),
  timeout: 300000, // 5分钟
  heartbeatTimeout: 120000, // 2分钟
  maxRetryCount: 1 // 最多重试1次
}


/**
 * SSE请求封装类
 */
class ChatSSEAPI {
  constructor() {
    this.sseClient = null
    this.isConnected = false
    this.currentController = null
  }

  /**
   * 设置SSE客户端引用
   * @param {Object} sseClientRef SSE客户端组件引用
   */
  setSSEClient(sseClientRef) {
    this.sseClient = sseClientRef
  }

  /**
   * 发送SSE POST请求
   * @param {Object} options 请求选项
   * @param {Array} options.messages 消息数组，OpenAI格式
   * @param {Function} options.onMessage 消息回调函数
   * @param {Function} options.onOpen 连接打开回调
   * @param {Function} options.onError 错误回调
   * @param {Function} options.onFinish 完成回调
   * @param {String} options.model 模型名称，可选
   * @param {String} options.apiKey API密钥，可选
   * @param {String} options.baseURL 基础URL，可选
   * @returns {Promise} 请求Promise
   */
  post(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.sseClient) {
        const error = new Error('SSE客户端未初始化，请先调用setSSEClient方法')
        console.error('❌', error.message)
        reject(error)
        return
      }

      const {
        messages = [],
        onMessage,
        onOpen,
        onError,
        onFinish,
        model = API_CONFIG.model,
        apiKey = API_CONFIG.apiKey,
        baseURL = API_CONFIG.baseURL,
        stream = true,
        timeout = API_CONFIG.timeout,
        heartbeatTimeout = API_CONFIG.heartbeatTimeout,
        maxRetryCount = API_CONFIG.maxRetryCount
      } = options

      // 验证必要参数
      if (!messages || messages.length === 0) {
        const error = new Error('messages参数不能为空')
        console.error('❌', error.message)
        reject(error)
        return
      }

      // 构建请求配置
      const requestConfig = {
        url: baseURL,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: {
          model,
          stream,
          messages
        },
        timeout,
        heartbeatTimeout,
        maxRetryCount
      }

      console.log('🚀 发起SSE请求:', {
        url: requestConfig.url,
        model,
        messagesCount: messages.length,
        stream,
        maxRetryCount: requestConfig.maxRetryCount
      })

      // 内部事件处理器
      const handleOpen = (response) => {
        this.isConnected = true
        console.log('✅ SSE连接已建立:', response)
        onOpen && onOpen(response)
      }

      const handleMessage = (data) => {
        try {
          // 解析SSE消息
          const parsedData = this.parseSSEMessage(data)
          if (parsedData) {
            onMessage && onMessage(parsedData)
          }
        } catch (error) {
          console.error('❌ 解析SSE消息失败:', error, data)
          onError && onError(error)
        }
      }

      const handleError = (error) => {
        this.isConnected = false
        console.error('❌ SSE连接错误:', error)
        onError && onError(error)
        reject(error)
      }

      const handleFinish = () => {
        this.isConnected = false
        console.log('✅ SSE连接完成')
        onFinish && onFinish()
        resolve()
      }

      // 绑定事件监听器
      this.sseClient.$off('onOpen')
      this.sseClient.$off('onMessage')
      this.sseClient.$off('onError')
      this.sseClient.$off('onFinish')

      this.sseClient.$on('onOpen', handleOpen)
      this.sseClient.$on('onMessage', handleMessage)
      this.sseClient.$on('onError', handleError)
      this.sseClient.$on('onFinish', handleFinish)

      // 启动SSE连接
      try {
        this.sseClient.startChat(requestConfig)
      } catch (error) {
        console.error('❌ 启动SSE连接失败:', error)
        reject(error)
      }
    })
  }

  /**
   * 解析SSE消息数据
   * @param {Object} data SSE原始数据
   * @returns {Object|null} 解析后的数据
   */
  parseSSEMessage(data) {
    try {
      console.log('🔍 原始SSE数据:', data)

      let jsonData = data

      // 如果data有data属性，先提取data属性
      if (data && typeof data === 'object' && data.data) {
        // 如果data.data是字符串，尝试解析JSON
        if (typeof data.data === 'string') {
          // 检查是否是结束标识
          if (data.data === '[DONE]' || data.data === 'data: [DONE]' || data.data.includes('[DONE]')) {
            console.log('🏁 检测到结束标识，返回完成状态')
            return {
              content: '',
              reasoningContent: '',
              isFinished: true,
              raw: data
            }
          }

          try {
            jsonData = JSON.parse(data.data)
          } catch (e) {
            // 如果解析失败，直接使用data.data作为内容
            return {
              content: data.data,
              reasoningContent: '',
              isFinished: false,
              raw: data
            }
          }
        } else {
          jsonData = data.data
        }
      }

      // 如果是字符串，尝试解析JSON
      if (typeof jsonData === 'string') {
        jsonData = JSON.parse(jsonData)
      }

      return this.extractContent(jsonData)
    } catch (error) {
      console.error('❌ 解析SSE消息JSON失败:', error, data)
      // 如果解析失败，尝试提取原始内容
      if (data && data.data) {
        return {
          content: typeof data.data === 'string' ? data.data : JSON.stringify(data.data),
          reasoningContent: '',
          isFinished: false,
          raw: data
        }
      }
      return null
    }
  }

  /**
   * 从OpenAI格式的响应中提取内容
   * @param {Object} data OpenAI格式的响应数据
   * @returns {Object|null} 提取的内容
   */
  extractContent(data) {
    try {
      console.log('🔍 提取内容，数据结构:', data)

      // 检查是否是OpenAI格式的响应
      if (data && data.choices && Array.isArray(data.choices) && data.choices.length > 0) {
        const choice = data.choices[0]

        // 检查是否有delta内容（流式响应）
        if (choice.delta) {
          // 检查content字段
          const hasContent = typeof choice.delta.content === 'string'
          // 检查reasoning_content字段
          const hasReasoningContent = typeof choice.delta.reasoning_content === 'string'

          if (hasContent || hasReasoningContent) {
            // 过滤掉结束标识
            if (hasContent && (choice.delta.content === '[DONE]' || choice.delta.content.includes('[DONE]'))) {
              console.log('🏁 检测到结束标识，返回完成状态')
              return {
                content: '',
                reasoningContent: '',
                finishReason: choice.finish_reason || 'stop',
                index: choice.index,
                id: data.id,
                model: data.model,
                created: data.created,
                isFinished: true
              }
            }

            const result = {
              content: hasContent ? choice.delta.content : '',
              reasoningContent: hasReasoningContent ? choice.delta.reasoning_content : '',
              finishReason: choice.finish_reason,
              index: choice.index,
              id: data.id,
              model: data.model,
              created: data.created,
              isFinished: choice.finish_reason !== null && choice.finish_reason !== undefined
            }
            console.log('✅ 成功提取delta内容:', {
              content: result.content,
              reasoningContent: result.reasoningContent,
              hasContent,
              hasReasoningContent
            })
            return result
          }
        }

        // 检查是否有message内容（完整响应）
        if (choice.message) {
          const hasContent = typeof choice.message.content === 'string'
          const hasReasoningContent = typeof choice.message.reasoning_content === 'string'

          if (hasContent || hasReasoningContent) {
            const result = {
              content: hasContent ? choice.message.content : '',
              reasoningContent: hasReasoningContent ? choice.message.reasoning_content : '',
              finishReason: choice.finish_reason,
              index: choice.index,
              id: data.id,
              model: data.model,
              created: data.created,
              isFinished: true
            }
            console.log('✅ 成功提取message内容:', {
              content: result.content,
              reasoningContent: result.reasoningContent
            })
            return result
          }
        }

        // 如果choices存在但没有有效内容，可能是结束标记
        if (choice.finish_reason) {
          console.log('🏁 检测到结束标记:', choice.finish_reason)
          return {
            content: '',
            reasoningContent: '',
            finishReason: choice.finish_reason,
            index: choice.index,
            id: data.id,
            model: data.model,
            created: data.created,
            isFinished: true
          }
        }
      }

      // 如果不是标准格式，尝试其他方式提取内容
      console.warn('⚠️ 非标准OpenAI格式，尝试其他方式提取:', data)

      // 直接检查是否有content字段
      if (data && typeof data.content === 'string') {
        return {
          content: data.content,
          reasoningContent: typeof data.reasoning_content === 'string' ? data.reasoning_content : '',
          isFinished: false,
          raw: data
        }
      }

      // 最后的兜底方案，但不应该到这里
      console.error('❌ 无法识别的数据格式:', data)
      return null

    } catch (error) {
      console.error('❌ 提取内容失败:', error, data)
      return null
    }
  }

  /**
   * 停止当前SSE连接
   */
  stop() {
    if (this.sseClient && this.isConnected) {
      console.log('🛑 停止SSE连接')
      this.sseClient.stopChat()
      this.isConnected = false
    }
  }

  /**
   * 检查连接状态
   * @returns {Boolean} 是否已连接
   */
  isConnecting() {
    return this.isConnected
  }

  /**
   * 更新API配置
   * @param {Object} config 新的配置
   */
  updateConfig(config = {}) {
    Object.assign(API_CONFIG, config)
    console.log('🔧 API配置已更新:', API_CONFIG)
  }

  /**
   * 获取当前API配置
   * @returns {Object} 当前配置
   */
  getConfig() {
    return { ...API_CONFIG }
  }


}

// 创建单例实例
const ajaxSSE = new ChatSSEAPI()

// 导出
export default {
  ajaxSSE,
  API_CONFIG
}
