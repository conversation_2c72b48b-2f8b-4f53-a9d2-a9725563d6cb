# SSE API 使用示例

## 基本用法

```javascript
// 1. 导入API
import apiModule from './api.js'
const { ajaxSSE } = apiModule

// 2. 在组件中初始化SSE客户端
// 在template中添加SSE组件
<gao-ChatSSEClient ref="sseClientRef" />

// 3. 在mounted中初始化
mounted() {
  ajaxSSE.setSSEClient(this.$refs.sseClientRef)
}

// 4. 发送请求
async sendMessage() {
  try {
    await ajaxSSE.post({
      messages: [
        {
          role: 'user',
          content: '你好，请介绍一下Vue.js'
        }
      ],
      onMessage: (data) => {
        console.log('收到消息:', data.content)
        // 处理流式消息
      },
      onOpen: (response) => {
        console.log('连接已建立')
      },
      onError: (error) => {
        console.error('连接错误:', error)
      },
      onFinish: () => {
        console.log('连接完成')
      }
    })
  } catch (error) {
    console.error('请求失败:', error)
  }
}
```

## 高级用法

### 自定义配置

```javascript
// 更新API配置
ajaxSSE.updateConfig({
  baseURL: 'https://your-api-domain.com/chat',
  apiKey: 'your-api-key',
  model: 'your-model-name',
  maxRetryCount: 1 // 最多重试1次，避免频繁重连
})

// 发送请求时覆盖配置
await ajaxSSE.post({
  messages: [...],
  baseURL: 'https://another-api.com/chat', // 临时覆盖
  apiKey: 'another-key', // 临时覆盖
  onMessage: (data) => {
    // 处理消息
  }
})
```

### 停止连接

```javascript
// 停止当前SSE连接
ajaxSSE.stop()

// 检查连接状态
if (ajaxSSE.isConnecting()) {
  console.log('正在连接中')
}
```

### 完整的对话历史

```javascript
// 构建包含历史对话的消息数组（包含刚添加的用户消息）
const messages = []

// 添加历史消息（包括刚添加的用户消息）
this.chatMessages.forEach(msg => {
  if ((msg.role === 'user' || msg.role === 'assistant') &&
      msg.content &&
      msg.content.trim() !== '' &&
      !msg.isStreaming) {
    messages.push({
      role: msg.role,
      content: msg.content
    })
  }
})

await ajaxSSE.post({
  messages,
  onMessage: (data) => {
    if (data.content) {
      // 累加内容实现流式显示
      this.aiResponse += data.content
    }
  }
})
```

## 数据格式

### 请求格式 (OpenAI标准)
```javascript
{
  "model": "Qwen/Qwen2.5-72B-Instruct",
  "stream": true,
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ]
}
```

### 响应格式 (OpenAI标准)
```javascript
{
  "choices": [
    {
      "delta": {
        "content": "你好"
      },
      "finish_reason": null,
      "index": 0
    }
  ],
  "created": 1753556865,
  "id": "chatcmpl-1753556864423912000",
  "model": "openUrl",
  "object": "chat.completion.chunk"
}
```

### 解析后的数据格式
```javascript
{
  content: "你好",           // 消息内容
  finishReason: null,       // 完成原因
  index: 0,                 // 选择索引
  id: "chatcmpl-xxx",       // 消息ID
  model: "openUrl",         // 模型名称
  created: 1753556865,      // 创建时间
  isFinished: false         // 是否完成
}
```

## 错误处理

```javascript
await ajaxSSE.post({
  messages: [...],
  onError: (error) => {
    // 处理各种错误
    if (error.message.includes('网络')) {
      uni.showToast({
        title: '网络连接失败',
        icon: 'none'
      })
    } else if (error.message.includes('权限')) {
      uni.showToast({
        title: 'API权限不足',
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: '请求失败，请重试',
        icon: 'none'
      })
    }
  }
})
```

## 注意事项

1. **初始化顺序**: 必须先设置SSE客户端引用，再发送请求
2. **消息格式**: 严格按照OpenAI格式传递messages数组
3. **错误处理**: 建议在onError回调中处理各种异常情况
4. **内存管理**: 长时间使用时注意及时停止不需要的连接
5. **跨域问题**: 确保后端API支持跨域请求
