<!--
 * @Description: 消息编辑弹窗组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-27 20:35:00
-->

<template>
  <u-popup
    :show="show"
    mode="center"
    :round="20"
    :close-on-click-overlay="false"
    @close="handleCancel"
  >
    <view class="edit-popup">
      <!-- 标题栏 -->
      <view class="edit-header">
        <text class="edit-title">编辑消息</text>
      </view>

      <!-- 编辑区域 -->
      <view class="edit-content">
        <textarea
          v-model="editContent"
          class="edit-textarea"
          placeholder="请输入消息内容..."
          :maxlength="2000"
          :show-confirm-bar="false"
          :adjust-position="false"
          :auto-height="true"
          :cursor-spacing="20"
        />
        <view class="char-count">
          <text class="count-text">{{ editContent.length }}/2000</text>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="edit-actions">
        <view class="cancel-text" @click="handleCancel">
          <text class="cancel-label">取消</text>
        </view>
        <view class="action-buttons">
          <!-- <view class="action-btn save-btn" @click="handleSave">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存</text>
          </view> -->
          <view class="action-btn resend-btn" @click="handleResend">
            <text class="btn-icon">🚀</text>
            <text class="btn-text">重新发起</text>
          </view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'EditInput',
  props: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      default: false
    },
    // 原始消息内容
    content: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editContent: '' // 编辑中的内容
    }
  },
  watch: {
    // 监听show变化，重置编辑内容
    show(newVal) {
      if (newVal) {
        this.editContent = this.content
      }
    },
    // 监听content变化
    content(newVal) {
      if (this.show) {
        this.editContent = newVal
      }
    }
  },
  methods: {
    /**
     * 处理取消
     */
    handleCancel() {
      this.$emit('cancel')
    },

    /**
     * 处理保存
     */
    handleSave() {
      const trimmedContent = this.editContent.trim()

      if (!trimmedContent) {
        uni.showToast({
          title: '消息内容不能为空',
          icon: 'none',
          duration: 2000
        })
        return
      }

      this.$emit('save', trimmedContent)
    },

    /**
     * 处理重新发起
     */
    handleResend() {
      const trimmedContent = this.editContent.trim()

      if (!trimmedContent) {
        uni.showToast({
          title: '消息内容不能为空',
          icon: 'none',
          duration: 2000
        })
        return
      }

      this.$emit('resend', trimmedContent)
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-popup {
  width: 90vw;
  max-width: 680rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;

  // 标题栏
  .edit-header {
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);

    .edit-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      text-align: center;
    }
  }

  // 编辑区域
  .edit-content {
    padding: 32rpx;

    .edit-textarea {
      width: 100%;
      min-height: 200rpx;
      max-height: 400rpx;
      padding: 24rpx;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      border: 1rpx solid rgba(0, 0, 0, 0.08);
      font-size: 30rpx;
      line-height: 1.6;
      color: #333;
      box-sizing: border-box;

      &:focus {
        border-color: #07c160;
        background-color: white;
      }
    }

    .char-count {
      display: flex;
      justify-content: flex-end;
      margin-top: 16rpx;

      .count-text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  // 按钮区域
  .edit-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx 32rpx;

    // 取消文字按钮
    .cancel-text {
      padding: 16rpx 8rpx;

      .cancel-label {
        font-size: 28rpx;
        color: #576b95;
        transition: color 0.2s ease;
        font-weight: 500;
      }

      &:active .cancel-label {
        color: #8a9dc9;
      }
    }

    // 右侧按钮组
    .action-buttons {
      display: flex;
      gap: 12rpx;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4rpx;
        height: 60rpx;
        padding: 0 18rpx;
        border-radius: 8rpx;
        transition: all 0.2s ease;
        min-width: 120rpx;

        &:active {
          transform: scale(0.96);
        }

        .btn-icon {
          font-size: 22rpx;
        }

        .btn-text {
          font-size: 24rpx;
          font-weight: 500;
        }
      }

      .save-btn {
        background-color: #07c160;

        .btn-text {
          color: white;
        }

        &:active {
          background-color: #1565c0;
        }
      }

      .resend-btn {
        background-color: #1989fa;

        .btn-text {
          color: white;
        }

        &:active {
          background-color: #147ce0;
        }
      }
    }
  }
}
</style>
