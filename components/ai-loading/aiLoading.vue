<!--
 * @Description: AI分析加载动画组件 - 绚丽的全屏加载效果
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02 10:19:00
-->
<template>
	<view v-if="show" class="ai-loading-overlay" @click.stop>
		<view class="ai-loading-container">
			<!-- 主要动画元素 -->
			<view class="ai-animation-wrapper">
				<!-- 中央AI图标 -->
				<view class="ai-icon-container">
					<view class="ai-icon">
						<image src="/static/images/ai.gif" mode="aspectFit" class="ai-gif"></image>
					</view>
					<view class="ai-pulse-ring"></view>
					<view class="ai-pulse-ring ring-2"></view>
					<view class="ai-pulse-ring ring-3"></view>
				</view>

				<!-- 旋转的数据点 -->
				<view class="data-points">
					<view class="data-point point-1">💡</view>
					<view class="data-point point-2">📊</view>
					<view class="data-point point-3">💰</view>
					<view class="data-point point-4">📋</view>
					<view class="data-point point-5">🎯</view>
					<view class="data-point point-6">⚡</view>
				</view>
			</view>

			<!-- 加载文本 -->
			<view class="ai-loading-text">
				<text class="main-text">{{ mainText }}</text>
				<text class="sub-text">{{ subText }}</text>
			</view>

			<!-- 进度条动画 -->
			<view class="progress-container">
				<view class="progress-bar">
					<view class="progress-fill"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: "AiLoading",
	props: {
		show: {
			type: Boolean,
			default: false
		},
		mainText: {
			type: String,
			default: "AI正在努力帮您分析"
		},
		subText: {
			type: String,
			default: "智能识别账单信息中..."
		}
	}
}
</script>

<style lang="scss" scoped>
/* AI分析加载动画样式 */
.ai-loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(15rpx);
	z-index: 9999;
	display: flex;
	justify-content: center;
	align-items: center;
	animation: fadeIn 0.2s ease-in-out;
}

.ai-loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 80rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 50rpx;
	backdrop-filter: blur(25rpx);
	box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.3);
	min-width: 600rpx;
	max-width: 90vw;
	animation: slideUpScale 0.2s ease-out;
}

.ai-animation-wrapper {
	position: relative;
	width: 320rpx;
	height: 320rpx;
	margin-bottom: 80rpx;
}

.ai-icon-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
}

.ai-icon {
	width: 140rpx;
	height: 140rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: gentleBounce 3s infinite ease-in-out;
}

.ai-gif {
	width: 100%;
	height: 100%;
}

.ai-pulse-ring {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 5rpx solid #1890ff;
	border-radius: 50%;
	opacity: 0;
	animation: pulse 3s infinite ease-out;
}

.ai-pulse-ring {
	width: 160rpx;
	height: 160rpx;
}

.ai-pulse-ring.ring-2 {
	width: 220rpx;
	height: 220rpx;
	animation-delay: 1s;
	border-color: #40a9ff;
}

.ai-pulse-ring.ring-3 {
	width: 280rpx;
	height: 280rpx;
	animation-delay: 2s;
	border-color: #69c0ff;
}

.data-points {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	animation: rotate 12s linear infinite;
}

.data-point {
	position: absolute;
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	box-shadow: 0 6rpx 18rpx rgba(0, 0, 0, 0.15);
	animation: float 4s ease-in-out infinite;
}

.data-point.point-1 {
	top: 0;
	left: 50%;
	transform: translateX(-50%);
	animation-delay: 0s;
}

.data-point.point-2 {
	top: 25%;
	right: 0;
	animation-delay: 0.5s;
}

.data-point.point-3 {
	bottom: 25%;
	right: 0;
	animation-delay: 1s;
}

.data-point.point-4 {
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	animation-delay: 1.5s;
}

.data-point.point-5 {
	bottom: 25%;
	left: 0;
	animation-delay: 2s;
}

.data-point.point-6 {
	top: 25%;
	left: 0;
	animation-delay: 2.5s;
}

.ai-loading-text {
	text-align: center;
	margin-bottom: 60rpx;
}

.main-text {
	display: block;
	font-size: 42rpx;
	font-weight: 600;
	color: #1890ff;
	margin-bottom: 25rpx;
	background: linear-gradient(45deg, #1890ff, #40a9ff, #69c0ff, #91d5ff);
	background-size: 300% 300%;
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	animation: textGradient 4s ease-in-out infinite;
}

.sub-text {
	display: block;
	font-size: 32rpx;
	color: #666;
	opacity: 0.8;
	animation: fadeInOut 3s ease-in-out infinite;
}

.progress-container {
	width: 500rpx;
}

.progress-bar {
	width: 100%;
	height: 10rpx;
	background: rgba(0, 0, 0, 0.1);
	border-radius: 5rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #1890ff, #40a9ff, #69c0ff, #91d5ff, #1890ff);
	background-size: 400% 100%;
	border-radius: 5rpx;
	animation: progressMove 3s linear infinite;
}

/* 动画定义 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUpScale {
	from {
		transform: translateY(150rpx) scale(0.8);
		opacity: 0;
	}
	to {
		transform: translateY(0) scale(1);
		opacity: 1;
	}
}

@keyframes gentleBounce {
	0%, 100% {
		transform: translateY(0) scale(1);
	}
	50% {
		transform: translateY(-8rpx) scale(1.05);
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(0.8);
	}
	50% {
		opacity: 0.6;
	}
	100% {
		opacity: 0;
		transform: translate(-50%, -50%) scale(1.8);
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-12rpx);
	}
}

@keyframes textGradient {
	0%, 100% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
}

@keyframes fadeInOut {
	0%, 100% {
		opacity: 0.6;
	}
	50% {
		opacity: 1;
	}
}

@keyframes progressMove {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}
</style>