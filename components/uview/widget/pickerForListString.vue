<!--
 * @Description: picker
 * @Author: 徐静(<EMAIL>)
 * @Date: 2023-04-08 22:07:32
 * @LastEditTime: 2025-03-20 16:43:51
 * @Copyright: (c) 2011-2021 http://www.winksoft.net All rights reserved.
-->
<!--

import UViewPicker from '@/components/uview/widget/pickerForListString'
<UViewPicker :list="columnBlackEdu" v-model="info.property">
    <template v-slot>
        <u-form-item label="类型" borderBottom >
            <div class>{{info.property}}</div>
        </u-form-item>
    </template>
</UViewPicker>
-->

<template>
<div class="w100 h100" @click=" open">
    <slot>
        <div class=""></div>
    </slot>
    <u-picker :show="openModel" @confirm="confirm" :columns="columns" @cancel="cancel" :defaultIndex="defaultIndex"></u-picker>
</div>
</template>

<script>
export default {
    name: 'pickerForListString',
    props: {
        list: {
            type: Array,
        },
        value: {
            type: String,
            default: '',

        }
    },
    watch: {
        // 监听列表
        list: {
            handler(v) {
                // console.log("🚀 -> file: pickerForListString.vue:39 -> handler -> v:", this.list)
                this.init();
            },
            deep: true,
        },
        // 监听值
        value(v) {
            this.init();
        }
    },
    data() {
        return {
            openModel: false,
            columns: [],
            defaultIndex:[],
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        // 初始化
        init() {
            const _u = JSON.parse(JSON.stringify(this.list));
            this.columns = [];
            this.columns.push(_u)
            if(this.value != ''){
                let _index = _u.findIndex((item) => {
                    return item == this.value
                })
                this.defaultIndex = [_index];
            }
        },
        // 开启picker
        open() {
            this.openModel = true;
        },
        // 取消Picker
        cancel() {
            this.openModel = false;
        },
        // 完成编辑
        confirm(e) {
            // console.log("🚀 -> file: pickerForListString.vue:72 -> confirm -> e:", e)
            this.openModel = false;
            this.$emit('input', e.value[0]);
        }
    },
};
</script>

<style lang="scss" scoped></style>
