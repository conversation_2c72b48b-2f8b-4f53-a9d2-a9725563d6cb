<!--
 * @Description: AI智能识别弹窗组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30
 * @Props:
 *   - show: Boolean - 控制弹窗显示/隐藏
 * @Events:
 *   - close: 关闭弹窗
 *   - submit: 提交识别内容，参数为识别的文本内容
-->
<template>
  <u-popup :show="show" @close="handleClose" class="ai-recognition-popup-box" height="80vh" mode="bottom" :duration="200" :round="20">
    <view class="ai-recognition-popup">
      <!-- 拖拽指示器 -->
      <view class="drag-indicator"></view>

      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">⚡ AI智能识别</text>
        <view class="popup-close" @click="handleClose">
          <text>×</text>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="ai-input-section">
        <view class="input-header">
          <view class="input-label">
            <text class="label-text">请输入需要AI识别的内容</text>
            <text class="label-tip">支持图片文字识别、语音转文字等</text>
          </view>
          <!-- 录音按钮在标题右侧 -->
          <view class="voice-button-container">
            <xj-voice
              ref="voiceRef"
              :style-mode="'compact'"
              :record-type="'click'"
              :min-duration="1"
              :max-duration="60"
              :auto-send-after-recognition="true"
              @success="handleVoiceSuccess"
              @auto-send="handleVoiceAutoSend"
              @error="handleVoiceError"
              @cancel="handleVoiceCancel"
            />
          </view>
        </view>
        <textarea
          class="ai-textarea"
          placeholder="在此输入文字内容，或描述您希望AI帮助识别的内容..."
          v-model="inputText"
          maxlength="1000"
          :auto-height="true"
          :show-confirm-bar="false"
        />
        <view class="char-count">
          <text>{{ inputText.length }}/1000</text>
        </view>
      </view>

      <!-- 底部按钮组 -->
      <view class="popup-footer">
        <view class="button-group">
          <button class="cancel-btn" @click="handleCancel">
            取消
          </button>
          <button class="send-btn" @click="handleSubmit" :disabled="isSubmitting">
            <text class="send-icon">✨</text>
            <text>{{ isSubmitting ? '识别中...' : '发送识别' }}</text>
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import XjVoice from '@/components/xj-voice/xj-voice.vue'

export default {
  name: 'XjAitextareaPopup',
  components: {
    XjVoice
  },
  props: {
    // 控制弹窗显示/隐藏
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputText: '', // 输入的文本内容
      isSubmitting: false // 是否正在提交中
    }
  },
  watch: {
    // 监听show变化，弹窗关闭时清空输入内容
    show(newVal) {
      if (!newVal) {
        this.inputText = ''
        this.isSubmitting = false
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 取消操作
    handleCancel() {
      this.inputText = ''
      this.handleClose()
    },

    // 提交识别请求
    async handleSubmit() {
      // 验证输入内容
      if (!this.inputText.trim()) {
        this.$toast('识别内容不得为空')
        return
      }

      try {
        this.isSubmitting = true

        // TODO: 这里将来接入真实的AI识别接口
        // const response = await this.$ajax.post('/ai/recognize', {
        //   content: this.inputText.trim()
        // })
        // 模拟识别结果
        const recognizedContent = this.inputText.trim()

        // 通过事件将识别结果传递给父组件
        this.$emit('submit', recognizedContent)

        // 清空输入并关闭弹窗
        this.inputText = ''
        this.handleClose()

      } catch (error) {
        console.error('AI识别失败:', error)
      } finally {
        this.isSubmitting = false
      }
    },

    // 录音成功回调
    handleVoiceSuccess(result) {
      console.log('录音成功:', result)

      // 如果有语音识别结果，将其添加到输入框
      if (result.recognitionText && result.recognitionText.trim()) {
        if (this.inputText.trim()) {
          this.inputText += '\n\n' + result.recognitionText
        } else {
          this.inputText = result.recognitionText
        }
        // this.$toast('语音识别成功')
      } else {
        // this.$toast('语音录制成功')
      }
    },

    // 语音自动发送回调
    handleVoiceAutoSend(result) {
      console.log('语音自动发送:', result)

      // 设置识别的文本内容
      this.inputText = result.text

      // 自动执行发送识别操作
      this.handleSubmit()
    },

    // 录音错误回调
    handleVoiceError(error) {
      console.error('录音失败:', error)
      // this.$toast('录音失败，请重试')
    },

    // 录音取消回调
    handleVoiceCancel() {
      console.log('录音已取消')
    },

    // 触发自动录音功能
    triggerAutoVoice() {
      try {
        // 通过ref调用录音组件的开始录音方法
        if (this.$refs.voiceRef && this.$refs.voiceRef.startRecording) {
          console.log('触发自动录音...')
          this.$refs.voiceRef.startRecording()
        } else {
          console.warn('录音组件未找到或不支持自动录音功能')
        }
      } catch (error) {
        console.error('触发自动录音失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// AI识别弹窗样式
.ai-recognition-popup {
  background-color: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  width: 100%;
  height: 60vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  // 拖拽指示器
  .drag-indicator {
    width: 72rpx;
    height: 8rpx;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4rpx;
    margin: 16rpx auto 0;
    flex-shrink: 0;
  }

  // 标题栏
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 40rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    flex-shrink: 0;

    .popup-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .popup-close {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      text {
        font-size: 40rpx;
        color: #666666;
        line-height: 1;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(0.95);
      }
    }
  }

  // 输入区域
  .ai-input-section {
    flex: 1;
    padding: 32rpx 40rpx 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .input-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 24rpx;
      flex-shrink: 0;

      .input-label {
        flex: 1;

        .label-text {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 8rpx;
        }

        .label-tip {
          display: block;
          font-size: 24rpx;
          color: #999999;
          line-height: 1.4;
        }
      }

      .voice-button-container {
        flex-shrink: 0;
        margin-left: 24rpx;
        margin-top: 4rpx; // 微调对齐
        align-self: flex-start; // 确保按钮靠左对齐
      }
    }

    .ai-textarea {
      flex: 1;
      width: 100%;
      padding: 24rpx;
      background-color: rgba(24, 144, 255, 0.05);
      border: 2rpx solid rgba(24, 144, 255, 0.1);
      border-radius: 16rpx;
      font-size: 28rpx;
      color: #333333;
      line-height: 1.6;
      transition: all 0.3s ease;
      resize: none;
      min-height: 380rpx; // 增加默认高度

      &:focus {
        border-color: rgba(24, 144, 255, 0.3);
        background-color: rgba(24, 144, 255, 0.08);
        box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
      }

      &::placeholder {
        color: #999999;
        font-size: 26rpx;
      }
    }

    .char-count {
      text-align: right;
      margin-top: 12rpx;
      flex-shrink: 0;

      text {
        font-size: 22rpx;
        color: #999999;
      }
    }
  }

  // 底部按钮组
  .popup-footer {
    padding: 24rpx 40rpx 40rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    flex-shrink: 0;

    .button-group {
      display: flex;
      gap: 16rpx;

      .cancel-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 16rpx;
        background-color: rgba(0, 0, 0, 0.05);
        color: #666666;
        font-size: 28rpx;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
          transform: scale(0.98);
        }
      }

      .send-btn {
        flex: 2;
        height: 80rpx;
        border-radius: 16rpx;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: #ffffff;
        font-size: 28rpx;
        font-weight: 600;
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);

        .send-icon {
          font-size: 24rpx;
          line-height: 1;
        }

        &:active {
          transform: scale(0.98);
          box-shadow: 0 2rpx 12rpx rgba(24, 144, 255, 0.4);
        }

        &:disabled {
          background: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          transform: none;
        }
      }
    }
  }
}
</style>
