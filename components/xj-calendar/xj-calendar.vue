<!--
 * @Description:  老徐手撸的日历
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-17
-->
<template>
  <view class="sign-calendar">
    <view class="month-bg" v-if="monthOpen">{{ m + 1 }}</view>
    <view class="top-bar">
      <view class="date-display">
        <span class="f-40 weight-5">{{ m + 1 }}月</span>
        <span class="f-30 weight-4 pt-10 c888">{{ y }}</span>
        <view class="today-btn" v-if="!isCurrentMonth" @click="goToToday">今天</view>
        <view class="clear-btn" v-if="hasSelection" @click="clearSelection">清除选择</view>
      </view>
      <view class="nav-buttons">
        <view class="nav-btn" @click="turning('prev')" :class="{ disabled: !canGoPrev }">
          <text class="arrow">&#8249;</text>
        </view>
        <view class="nav-btn" @click="turning('next')" :class="{ disabled: !canGoNext }">
          <text class="arrow">&#8250;</text>
        </view>
      </view>
    </view>

    <view class="week">
      <view class="week-day" v-for="(item, index) in weekDay" :key="index">{{ item }}</view>
    </view>

    <view :class="{ hide: !monthOpen }" class="content" :style="{ height: height }"
          @touchmove="onGlobalTouchMove" @touchend="onGlobalTouchEnd">
      <view :style="{ top: positionTop + 'upx' }" class="days">
        <view class="item" v-for="(item, index) in dates" :key="index" :class="{
          'item-range-bg': isInSelectedRange(item)
        }">
          <view class="day"
            @click="selectOne(item, $event)"
            @touchstart="onTouchStart(item, index, $event)"
            :data-index="index"
            :class="{
            choose: !enableDragSelect && choose == `${item.year}-${item.month + 1}-${item.date}`,
            nolm: !item.lm,
            disabled: !isDateInRange(item.year, item.month + 1, item.date),
            'range-start': isRangeStart(item),
            'range-end': isRangeEnd(item),
            'range-middle': isRangeMiddle(item),
            'drag-selecting': isDragSelecting(item)
          }">{{ item.date }}</view>
          <view class="sign" v-if="isSigned(item.year, item.month + 1, item.date) && isDateInRange(item.year, item.month + 1, item.date)" :style="{ backgroundColor: getSignColor(item.year, item.month + 1, item.date) }"></view>
          <view class="today-text" v-if="isToday(item.year, item.month, item.date)">今</view>
        </view>
      </view>
    </view>

    <image src="/static/xj-calendar/ico-arrow-up.png" mode="scaleToFill" @click="trgWeek()" class="weektoggel" :class="{ down: !monthOpen }"></image>
  </view>
</template>

<script>
export default {
  name: 'xj-calendar',
  props: {
    // 第一列星期几
    weekstart: {
      type: Number,
      default: 7
    },
    // 已经签到的日期
    signeddates: {
      type: Array,
      default: () => []
    },
    // 是否展开
    open: {
      type: Boolean,
      default: true
    },
    // 日期范围限制
    minDate: {
      type: String,
      default: null
    },
    maxDate: {
      type: String,
      default: null
    },
    // 是否支持拖拽多选
    enableDragSelect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      text: {
        year: '年',
        month: '月',
        week: ['一', '二', '三', '四', '五', '六', '日'],
        today: '今'
      },
      y: new Date().getFullYear(), // 年
      m: new Date().getMonth(), // 月
      dates: [], // 当前月日期集合
      positionTop: 0,
      monthOpen: true,
      choose: '',
      // 动画状态
      isAnimating: false,
      slideDirection: '', // 'left' 或 'right'
      // 拖拽多选相关状态
      isDragging: false, // 是否正在拖拽
      dragStartDate: null, // 拖拽开始的日期
      dragEndDate: null, // 拖拽结束的日期
      selectedRange: [], // 选中的日期范围
      longPressTimer: null, // 长按定时器
      dragStartIndex: -1, // 拖拽开始的索引
      dragEndIndex: -1 // 拖拽结束的索引
    }
  },
  created() {
    this.dates = this.monthDay(this.y, this.m)
    !this.open && this.trgWeek()
  },
  mounted() {
    let date = new Date()
    let y = date.getFullYear()
    let m = date.getMonth()
    let d = date.getDate()
    this.choose = `${y}-${m + 1}-${d}`

    console.log(this.choose)
  },
  computed: {
    // 顶部星期栏目
    weekDay() {
      return this.text.week.slice(this.weekstart - 1).concat(this.text.week.slice(0, this.weekstart - 1))
    },
    height() {
      return (this.dates.length / 7) * 80 + 'upx'
    },
    // 是否可以向前翻页
    canGoPrev() {
      if (!this.minDate) return true
      const minDateObj = new Date(this.minDate)
      const currentMonth = new Date(this.y, this.m, 1)
      return currentMonth > new Date(minDateObj.getFullYear(), minDateObj.getMonth(), 1)
    },
    // 是否可以向后翻页
    canGoNext() {
      if (!this.maxDate) return true
      const maxDateObj = new Date(this.maxDate)
      const currentMonth = new Date(this.y, this.m, 1)
      return currentMonth < new Date(maxDateObj.getFullYear(), maxDateObj.getMonth(), 1)
    },
    // 是否是当前月份
    isCurrentMonth() {
      const today = new Date()
      return this.y === today.getFullYear() && this.m === today.getMonth()
    },
    // 是否有选择的日期
    hasSelection() {
      if (this.enableDragSelect) {
        // 多选模式：检查是否有选中的日期范围
        return this.selectedRange && this.selectedRange.length > 0
      } else {
        // 单选模式：检查是否有选中的日期
        return this.choose && this.choose !== ''
      }
    }
  },
  methods: {
    // 获取当前月份天数
    monthDay(y, m) {
      let firstDayOfMonth = new Date(y, m, 1).getDay() // 当月第一天星期几
      let lastDateOfMonth = new Date(y, m + 1, 0).getDate() // 当月最后一天
      let lastDayOfLastMonth = new Date(y, m, 0).getDate() // 上一月的最后一天
      let dates = [] // 所有渲染日历
      let weekstart = this.weekstart == 7 ? 0 : this.weekstart // 方便进行日期计算，默认星期从0开始
      let startDay = (() => {
        // 周初有几天是上个月的
        if (firstDayOfMonth == weekstart) {
          return 0
        } else if (firstDayOfMonth > weekstart) {
          return firstDayOfMonth - weekstart
        } else {
          return 7 - weekstart + firstDayOfMonth
        }
      })()
      let endDay = 7 - ((startDay + lastDateOfMonth) % 7) // 结束还有几天是下个月的
      for (let i = 1; i <= startDay; i++) {
        dates.push({
          date: lastDayOfLastMonth - startDay + i,
          day: weekstart + i - 1 || 7,
          month: m - 1 >= 0 ? m - 1 : 12,
          year: m - 1 >= 0 ? y : y - 1
        })
      }
      for (let j = 1; j <= lastDateOfMonth; j++) {
        dates.push({
          date: j,
          day: (j % 7) + firstDayOfMonth - 1 || 7,
          month: m,
          year: y,
          lm: true
        })
      }
      for (let k = 1; k <= endDay; k++) {
        dates.push({
          date: k,
          day: (lastDateOfMonth + startDay + weekstart + k - 1) % 7 || 7,
          month: m + 1 <= 11 ? m + 1 : 0,
          year: m + 1 <= 11 ? y : y + 1
        })
      }
      return dates
    },
    // 已经签到处理
    isSigned(y, m, d) {
      if (!this.signeddates || this.signeddates.length === 0) return false
      let dateStr = `${y}-${m.toString().padStart(2, '0')}-${d.toString().padStart(2, '0')}`

      return this.signeddates.some(item => {
        if (typeof item === 'string') {
          return item === dateStr
        } else if (typeof item === 'object' && item !== null) {
          return item.date === dateStr && item.date !== '' && item.date !== null
        }
        return false
      })
    },
    // 获取签到标记的颜色
    getSignColor(y, m, d) {
      if (!this.signeddates || this.signeddates.length === 0) return '#ff6b35'
      let dateStr = `${y}-${m.toString().padStart(2, '0')}-${d.toString().padStart(2, '0')}`

      for (let item of this.signeddates) {
        if (typeof item === 'string') {
          if (item === dateStr) return '#ff6b35' // 默认颜色
        } else if (typeof item === 'object' && item !== null) {
          if (item.date === dateStr && item.date !== '' && item.date !== null) {
            return item.color && item.color !== '' ? item.color : '#ff6b35'
          }
        }
      }
      return '#ff6b35'
    },
    // 检查日期是否在允许范围内
    isDateInRange(y, m, d) {
      const dateStr = `${y}-${m.toString().padStart(2, '0')}-${d.toString().padStart(2, '0')}`
      const currentDate = new Date(dateStr)

      if (this.minDate) {
        const minDate = new Date(this.minDate)
        if (currentDate < minDate) return false
      }

      if (this.maxDate) {
        const maxDate = new Date(this.maxDate)
        if (currentDate > maxDate) return false
      }

      return true
    },
    isToday(y, m, d) {
      let date = new Date()
      return y == date.getFullYear() && m == date.getMonth() && d == date.getDate()
    },
    // 切换成周模式
    trgWeek() {
      this.monthOpen = !this.monthOpen
      if (this.monthOpen) {
        this.positionTop = 0
      } else {
        let index = -1
        this.dates.forEach((i, x) => {
          this.isToday(i.year, i.month, i.date) && (index = x)
        })
        this.positionTop = -((Math.ceil((index + 1) / 7) || 1) - 1) * 80
      }
    },
    // 点击回调
    selectOne(i, event) {
      // 如果启用了拖拽多选，不处理点击事件（通过拖拽处理）
      if (this.enableDragSelect) {
        return false
      }

      let date = `${i.year}-${i.month + 1}-${i.date}`
      let selectD = new Date(date)

      // 检查是否在当前月
      if (selectD.getMonth() != this.m) {
        console.log('不在当前月范围内')
        return false
      }

      // 检查是否在允许的日期范围内
      if (!this.isDateInRange(i.year, i.month + 1, i.date)) {
        console.log('不在允许的日期范围内')
        return false
      }

      this.choose = date
      this.$emit('on-click', date)
    },
        // 上个月，下个月
    turning(_action) {
      if (_action === 'next' && !this.canGoNext) {
        return false
      }
      if (_action === 'prev' && !this.canGoPrev) {
        return false
      }

      if (this.isAnimating) {
        return false // 防止动画期间重复点击
      }

      // 设置滑动方向和动画状态
      this.slideDirection = _action === 'next' ? 'left' : 'right'
      this.isAnimating = true

      // 延迟更新数据，让动画先开始
      setTimeout(() => {
        if (_action === 'next') {
          if (this.m + 1 == 12) {
            this.m = 0
            this.y = this.y + 1
          } else {
            this.m = this.m + 1
          }
        } else {
          if (this.m + 1 == 1) {
            this.m = 11
            this.y = this.y - 1
          } else {
            this.m = this.m - 1
          }
        }

        this.dates = this.monthDay(this.y, this.m)

        // 触发月份切换事件
        const monthParam = `${this.y}-${(this.m + 1).toString().padStart(2, '0')}`
        this.$emit('month-change', monthParam)

        // 动画结束后重置状态
        setTimeout(() => {
          this.isAnimating = false
          this.slideDirection = ''
        }, 300)
      }, 150)
    },
    // 跳转到今天
    goToToday() {
      const today = new Date()
      this.y = today.getFullYear()
      this.m = today.getMonth()
      this.dates = this.monthDay(this.y, this.m)

      // 触发月份切换事件
      const monthParam = `${this.y}-${(this.m + 1).toString().padStart(2, '0')}`
      this.$emit('month-change', monthParam)

      // 更新选中的日期为今天
      const todayStr = `${this.y}-${this.m + 1}-${today.getDate()}`
      this.choose = todayStr
      this.$emit('on-click', todayStr)
    },

    // 清除选择
    clearSelection() {
      if (this.enableDragSelect) {
        // 多选模式：清除选中的日期范围
        this.selectedRange = []
        this.dragStartDate = null
        this.dragEndDate = null
        this.dragStartIndex = -1
        this.dragEndIndex = -1
      } else {
        // 单选模式：清除选中的日期
        this.choose = ''
      }

      // 触发清除选择事件
      this.$emit('on-clean')
    },

    // 触摸开始
    onTouchStart(item, index, event) {
      // 如果未启用拖拽多选，直接返回
      if (!this.enableDragSelect) return

      // 检查是否在当前月和允许的日期范围内
      if (item.month !== this.m || !this.isDateInRange(item.year, item.month + 1, item.date)) {
        return
      }

      // 清除之前的选中范围
      this.selectedRange = []

      // 立即开始拖拽，不需要长按
      this.isDragging = true
      this.dragStartDate = `${item.year}-${item.month + 1}-${item.date}`
      this.dragEndDate = this.dragStartDate
      this.dragStartIndex = index
      this.dragEndIndex = index
      this.updateSelectedRange()

      // 阻止默认行为和冒泡
      event.preventDefault()
      event.stopPropagation()
    },

    // 全局触摸移动处理
    onGlobalTouchMove(event) {
      if (!this.enableDragSelect || !this.isDragging) return

      // 获取触摸点坐标
      const touch = event.touches[0]
      if (!touch) return

      // 通过坐标找到对应的日期元素
      const element = document.elementFromPoint(touch.clientX, touch.clientY)
      if (!element) return

      // 查找最近的.day元素
      const dayElement = element.closest('.day')
      if (!dayElement) return

      // 获取索引
      const index = parseInt(dayElement.getAttribute('data-index'))
      if (isNaN(index) || index < 0 || index >= this.dates.length) return

      const item = this.dates[index]

      // 检查是否在当前月和允许的日期范围内
      if (item.month !== this.m || !this.isDateInRange(item.year, item.month + 1, item.date)) {
        return
      }

      // 更新拖拽结束日期
      this.dragEndDate = `${item.year}-${item.month + 1}-${item.date}`
      this.dragEndIndex = index
      this.updateSelectedRange()

      event.preventDefault()
    },

    // 全局触摸结束处理
    onGlobalTouchEnd(event) {
      if (!this.enableDragSelect || !this.isDragging) return

      // 结束拖拽
      this.isDragging = false

      // 如果有选中范围，发送选中范围事件
      if (this.selectedRange.length > 0) {
        this.$emit('on-click', this.selectedRange)
      }

      event.preventDefault()
    },

    // 单个元素触摸结束（备用）
    onTouchEnd(item, index, event) {
      // 这个方法现在主要由全局处理器处理
      // 保留作为备用
    },

    // 更新选中范围
    updateSelectedRange() {
      if (!this.dragStartDate || !this.dragEndDate) {
        this.selectedRange = []
        return
      }

      // 确保开始索引小于结束索引
      const startIndex = Math.min(this.dragStartIndex, this.dragEndIndex)
      const endIndex = Math.max(this.dragStartIndex, this.dragEndIndex)

      // 收集选中范围内的日期
      const range = []
      for (let i = startIndex; i <= endIndex; i++) {
        const item = this.dates[i]
        // 只选择当前月份内的日期
        if (item.month === this.m && this.isDateInRange(item.year, item.month + 1, item.date)) {
          range.push(`${item.year}-${item.month + 1}-${item.date}`)
        }
      }

      this.selectedRange = range
    },

    // 判断是否是范围开始
    isRangeStart(item) {
      if (!this.enableDragSelect || this.selectedRange.length === 0) return false
      const dateStr = `${item.year}-${item.month + 1}-${item.date}`
      return dateStr === this.selectedRange[0]
    },

    // 判断是否是范围结束
    isRangeEnd(item) {
      if (!this.enableDragSelect || this.selectedRange.length === 0) return false
      const dateStr = `${item.year}-${item.month + 1}-${item.date}`
      return dateStr === this.selectedRange[this.selectedRange.length - 1]
    },

    // 判断是否是范围中间
    isRangeMiddle(item) {
      if (!this.enableDragSelect || this.selectedRange.length <= 2) return false
      const dateStr = `${item.year}-${item.month + 1}-${item.date}`
      return this.selectedRange.includes(dateStr) &&
             dateStr !== this.selectedRange[0] &&
             dateStr !== this.selectedRange[this.selectedRange.length - 1]
    },

    // 判断是否正在被拖拽选择
    isDragSelecting(item) {
      return this.isDragging && this.selectedRange.includes(`${item.year}-${item.month + 1}-${item.date}`)
    },

    // 判断是否在选中范围内（用于背景样式）
    isInSelectedRange(item) {
      if (!this.enableDragSelect || this.selectedRange.length === 0) return false
      const dateStr = `${item.year}-${item.month + 1}-${item.date}`
      return this.selectedRange.includes(dateStr)
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-calendar {
  color: #333;
  font-size: 28upx;
  text-align: center;
  background-color: #fff;
  padding-bottom: 10upx;
  position: relative;

  .month-bg {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 280upx;
    font-weight: 900;
    color: #e0e0e0;
    z-index: 1;
    pointer-events: none;
    line-height: 1;
    opacity: 0.6;
  }

      .top-bar {
    height: 80upx;
    border-bottom: 1upx solid #e5e5e5;
    position: relative;
    z-index: 2;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30upx;

    .date-display {
      display: flex;
      align-items: center;
      gap: 20upx;
      font-size: 32upx;
      font-weight: 600;
      color: #333;

      .today-btn {
        font-size: 24upx;
        font-weight: 500;
        color: #007aff;
        background-color: rgba(0, 122, 255, 0.1);
        padding: 8upx 20upx ;
        border-radius: 20upx;
        transition: all 0.2s ease;
        margin-top: 10upx;

        &:active {
          background-color: rgba(0, 122, 255, 0.2);
          transform: scale(0.95);
        }
      }

      .clear-btn {
        font-size: 24upx;
        font-weight: 500;
        color: #ff6b35;
        background-color: rgba(255, 107, 53, 0.1);
        padding: 8upx 20upx;
        border-radius: 20upx;
        transition: all 0.2s ease;
        margin-top: 10upx;

        &:active {
          background-color: rgba(255, 107, 53, 0.2);
          transform: scale(0.95);
        }
      }
    }

    .nav-buttons {
      display: flex;
      align-items: center;
      gap: 20upx;

      .nav-btn {
        width: 60upx;
        height: 60upx;
        border-radius: 30upx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        transition: all 0.2s ease;

        &:active:not(.disabled) {
          background-color: #e0e0e0;
          transform: scale(0.95);
        }

        &.disabled {
          background-color: #f8f8f8;
          opacity: 0.4;

          .arrow {
            color: #ccc;
          }
        }

        .arrow {
          font-size: 36upx;
          font-weight: bold;
          color: #666;
        }
      }
    }
  }

  .week {
    display: flex;
    align-items: center;
    height: 80upx;
    line-height: 80upx;
    border-bottom: 1upx solid #f0f0f0;
    background-color: #fafafa;
    position: relative;
    z-index: 2;

    view {
      flex: 1;
      color: #666;
      font-weight: 500;
    }
  }

  .content {
    position: relative;
    overflow: hidden;
    transition: height 0.4s ease;
    background-color: transparent;
    z-index: 2;

    .days {
      transition: top 0.3s;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      position: relative;

      .item {
        position: relative;
        display: block;
        height: 80upx;
        line-height: 80upx;
        width: calc(100% / 7);

        // 选中范围背景
        &.item-range-bg {
          // background-color: rgba(0, 122, 255, 0.1);
        }

        .day {
          font-style: normal;
          display: inline-block;
          vertical-align: middle;
          width: 60upx;
          height: 60upx;
          line-height: 60upx;
          overflow: hidden;
          border-radius: 60upx;
          color: #333;
          transition: all 0.2s ease;

          &.choose {
            background-color: #007aff;
            color: #fff;
            box-shadow: 0 2upx 8upx rgba(0, 122, 255, 0.3);
          }

          &.nolm {
            color: #ccc;
            opacity: 0.6;
          }

          &.disabled {
            color: #ddd !important;
            background-color: transparent !important;
            pointer-events: none;
            opacity: 0.3;
          }

          // 拖拽多选样式
          &.range-start {
            background-color: #007aff !important;
            color: #fff !important;
            box-shadow: 0 2upx 8upx rgba(0, 122, 255, 0.3);
          }

          &.range-end {
            background-color: #007aff !important;
            color: #fff !important;
            box-shadow: 0 2upx 8upx rgba(0, 122, 255, 0.3);
          }

          &.range-middle {
            background-color: rgba(0, 122, 255, 0.3) !important;
            color: #007aff !important;
          }

          &.drag-selecting {
            background-color: rgba(0, 122, 255, 0.4) !important;
            color: #007aff !important;
            transform: scale(1.02);
            transition: all 0.1s ease;
          }
        }

        .sign {
          font-style: normal;
          width: 12upx;
          height: 12upx;
          border-radius: 6upx;
          position: absolute;
          left: 50%;
          margin-left: -6upx;
          bottom: 0upx;
          pointer-events: none;
          box-shadow: 0 1upx 3upx rgba(0, 0, 0, 0.2);
        }

        .today-text {
          position: absolute;
          font-size: 20upx;
          font-weight: bold;
          width: 20upx;
          height: 20upx;
          line-height: 20upx;
          right: 5upx;
          top: 10upx;
          color: #ff6b35;
          background-color: rgba(255, 107, 53, 0.1);
          border-radius: 10upx;
        }
      }
    }
  }

  .hide {
    height: 80upx !important;
  }

  .weektoggel {
    width: 80upx;
    height: 40upx;
    margin: 10upx auto 0;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(195deg) brightness(104%) contrast(97%);
    position: relative;
    z-index: 2;

    &.down {
      transform: rotate(180deg);
    }
  }
}
</style>
