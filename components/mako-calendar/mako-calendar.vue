<template>
	<view class="container">
		<view class="calendar">
			<Calendar ref="CalendarRef" showLunar @change="handleChooseDate" showShrink @height="heightChange">
			</Calendar>
		</view>
		<Schedule :scrollHeight="scrollHeight" :chooseDate="currentDate" :list="list"></Schedule>
		<view class="arrow-left" @tap="goToday" v-if="showGoToday">
			返回
		</view>
	</view>
</template>

<script>
	const dayjs = require('./static/js/dayjs.min.js');
	import Schedule from './components/schedule/schedule.vue'
	import Calendar from './components/calendar/calendar.vue'
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			},
		},
		components: {
			Schedule,
			Calendar
		},
		computed: {
			scrollHeight() {
				return `calc(100vh - ${this.calendarHeight}rpx)`
			},
			currentDate() {
				if (this.selectedDate) {
					const {
						year,
						month,
						day
					} = this.selectedDate
					return dayjs(`${year}-${month}-${day}`).format('YYYY-MM-DD')
				} else return null
			},
			showGoToday() {
				if (this.selectedDate && this.today) {
					const {
						year,
						month,
						day
					} = this.selectedDate
					return !(this.today.year === year && this.today.month === month && this.today.day === day)
				} else return false
			},
		},
		data() {
			return {
				today: {},
				calendarHeight: '100vh',
				selectedDate: null,
			}
		},
		mounted() {
			this.today = {
				year: dayjs().get('year'),
				month: dayjs().get('month') + 1,
				day: dayjs().get('date'),
				value: dayjs().format('YYYY-MM-DD')
			}
		},
		methods: {
			goToday() {
				this.$nextTick(() => {
					this.$refs['CalendarRef'].goToday()
				})
			},
			handleChooseDate(e) {
				// {date: "2024-06-29", year: 2024, month: 6, day: 29, week: 6, …}
				this.selectedDate = e
				this.$emit("change", this.selectedDate);
			},
			heightChange(e) {
				this.calendarHeight = e
			},
		}
	}
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		position: relative;
	}

	.container .calendar {
		position: sticky;
		top: 0;
		z-index: 11;
	}

	.plus {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		border: 2rpx solid #9f9f9f;
		background: #0f2c50;
		position: fixed;
		z-index: 99;
		right: 44rpx;
		bottom: 88rpx;
		width: 88rpx;
		height: 88rpx;
	}

	.arrow-left {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: #0f2c50;
		color: #fff;
		position: fixed;
		z-index: 99;
		right: 44rpx;
		bottom: 188rpx;
		width: 88rpx;
		height: 88rpx;
	}
</style>