<template>
	<MakoCalendar :list="list" @change="change"></MakoCalendar>
</template>

<script>
	/**
	 * @property {String} list 日程数据, 字段看下面列子
	 * @event {Function()} change 切换日期触发事件
	 */
	import MakoCalendar from './mako-calendar.vue';
	export default {
		components: {
			MakoCalendar
		},
		data() {
			return {
				today: '2024-09-16', // 测试时候,把这个改为当天时间,看效果
				list: []
			}
		},
		mounted() {
			setTimeout(() => {
				this.getList()
			}, 1000)
		},
		methods: {
			change(e) {
				console.log(e);
			},
			getList() {
				const date = this.today
				this.list = [{
						id: '1',
						startTime: `${date} 13:45:00`, // 必填
						endTime: `${date} 14:45:00`, // 必填
						backgroundColor: '',
						title: '日程一',
					},
					{
						id: '2',
						startTime: `${date} 14:35:00`,
						endTime: `${date} 15:45:00`,
						backgroundColor: '#0f2c50',
						title: '日程二',
					},
					{
						id: '3',
						startTime: `${date} 16:45:00`,
						endTime: `${date} 18:45:00`,
						backgroundColor: '#00b42a',
						title: '日程三',
					}
				]
			},
		}
	}
</script>