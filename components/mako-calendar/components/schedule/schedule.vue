<template>
	<view class="dayTable">
		<scroll-view class="calendar_body" :style="{
			height: scrollHeight
		}" :scroll-y="isScroll" :scroll-top="scrollTop">
			<!-- 默认底色表格 -->
			<view class="calendar_table" @tap="touchSt($event)">
				<view v-for="(item,index) in defaultList" :key="index" :ref='index' class="cal_tr" :class="item.trClass"
					:style="{'height':unitHeight+'px;' + item.hidClass}">
					<view class="left_time">{{item.timeTitle}}</view>
					<view class="right_content" :style="{
						height: unitHeightRpx
					}"></view>
				</view>
			</view>

			<!-- 区域 -->
			<view class="calendar_schedule">
				<!-- 已创建的 -->
				<view v-for="(item,index) in scheduleList" :key="index" :style="item.style" class="schedule_a"
					@click="handleClick(item)">
					<view class="create_content_box">
						<view class="schedule_content_name">{{item.title}}</view>
						<view class="schedule_content_time">{{item.start_time}} ~ {{item.end_time}}({{item.week}})
						</view>
					</view>
				</view>
			</view>
			<!-- 时间线刻度 -->
			<view v-if="isToday==0" class="time_now" :style="nowTime.line">
				<view class="left_text_red">{{nowTime.text}}</view>
				<view class="left_text_radio"></view>
				<view class="time_line"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		handleCalendarData,
		getWeek
	} from './utils.js'
	let hidID = -1;
	export default {
		props: {
			chooseDate: {
				type: String,
				default: '',
			},
			scrollHeight: {
				type: String,
				default: '100vh'
			},
			list: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {
				unitHeight: 12, // px
				unitMinute: 15,
				showMinute: 60,
				scale: 0,
				minRatio: 0,
				minAll: 0,
				defaultList: [],
				scheduleList: [],
				nowLine: "",
				startId: 0,
				isScroll: true,
				nowTime: {},
				scrollTop: 0,
				timeId: -1,
				isToday: 0,
				classCourseVisible: false,
				classCourseDetail: null
			};
		},
		watch: {
			chooseDate: {
				handler(newVal) {
					if (newVal) {
						if (hidID > -1 && this.defaultList.length) {
							this.defaultList[hidID].hidClass = "font-size:12px;"
						}
						this.isTodayFun(newVal);
					}
				},
				// immediate: true
			},
			list: {
				handler(newVal) {
					this.createList();
				},
				immediate: true,
				// deep: true
			},
		},
		computed: {
			unitHeightRpx() {
				return this.unitHeight * 2 + 'rpx'
			}
		},
		created() {
			this.minRatio = this.showMinute / this.unitMinute; //4
			this.minAll = 1440 / this.unitMinute; // (24*60) / 15 = 96
			//后续修改
			this.scrollTop = this.unitHeight * 36;
			this.getDefaultTable();
		},
		methods: {
			//判断是否为今天
			isTodayFun(d) {
				let isToday;
				let td = new Date();
				td = new Date(td.getFullYear(), td.getMonth(), td.getDate());
				let od = new Date(d);
				od = new Date(od.getFullYear(), od.getMonth(), od.getDate());
				let xc = od - td;
				let result = "";
				if (xc < 0) {
					isToday = -1
				} else if (xc == 0) {
					isToday = 0;
				} else {
					isToday = 1;
				}
				this.isToday = isToday;
				if (this.isToday == 0) {
					this.getTimeNow();
				}
			},
			//获取当前时间imeNow
			getTimeNow() {
				let self = this;
				let hidId = -1;
				let nowDate = new Date();
				let hour = nowDate.getHours(); //获取当前小时数(0-23)
				let min = nowDate.getMinutes(); //获取当前分钟数(0-59)
				let top = (hour * self.minRatio + min / self.unitMinute) * self.unitHeight - 1;
				let timeLine = "top:" + top + "px;";
				let text = nowDate.toString().substring(15, 21);
				//是否隐藏上下时间线
				if (min < this.unitMinute) {
					hidId = hour * self.minRatio;
				} else if (min > (this.unitMinute * 3)) {
					hidId = (hour + 1) * self.minRatio;
				}
				self.nowLine = timeLine;
				self.nowTime = {
					line: timeLine,
					text: text
				};
				// 红线到达的位置，防止时间信息重叠
				if (hidId > -1) {
					self.defaultList[hidId].hidClass = "font-size:0;"
				};
				hidID = hidId;
				let timeId = hour * self.minRatio + Math.floor(min / self.unitMinute);

				self.timeId = timeId;
				this.scrollTop = timeId * this.unitHeight - 50;
			},
			//获取表格默认数据
			getDefaultTable() {
				let self = this;
				let list = [],
					rat = this.minRatio;
				for (let i = 0; i < 97; i++) {
					let time = "",
						timeClass = "";
					let hour = Math.floor(i / rat);
					hour = hour < 10 ? '0' + hour : hour;

					if (i % rat === 0) {
						time = hour + ":00";
						timeClass = "hasTime"
					} else {
						time = hour + ':' + i % rat * self.unitMinute;
					}
					list.push({
						trClass: timeClass,
						timeTitle: time,
						hidClass: ''
					})
				};
				// console.log(list);
				this.defaultList = list;
			},
			//整理列表数据
			createList() {
				let newList = handleCalendarData(JSON.parse(JSON.stringify(this.list)))
				let list = [];
				if (newList.length) {
					newList.forEach(arr => {
						let len = arr.length
						if (len === 1) {
							arr.forEach(item => {
								item.width = '100%'
								item.left = '0'
							})
						} else {
							arr.forEach((item, index) => {
								item.width = Math.floor(100 / len) + '%'
								item.left = (Math.floor(100 / len) * index) + '%'
							})
						}
					})
					// console.log('newList', newList);
					let scheduleList = []
					newList.map(arr => {
						scheduleList.push(...arr)
					})

					for (let i = 0; i < scheduleList.length; i++) {
						const {
							startTime,
							endTime,
							width,
							left,
							backgroundColor,
							title,
						} = scheduleList[i]
						let start = startTime.substring(11, 16).split(":");
						let end = endTime.substring(11, 16).split(":");
						let st = parseInt(start[0] * this.minRatio) + parseInt(start[1] / this.unitMinute);
						let ed = parseInt(end[0] * this.minRatio) + parseInt(end[1] / this.unitMinute);
						let height = `${(ed - st) * this.unitHeight}px`
						let top = `${st * this.unitHeight}px`

						list.push({
							title,
							start_time: startTime.substring(11, 16),
							end_time: endTime.substring(11, 16),
							week: '周' + getWeek(endTime),
							top: st * this.unitHeight,
							style: `height: ${height};top: ${top};width: ${width};left: ${left};background-color: ${backgroundColor || '#cf1c26'};`,
							startId: st,
							endId: ed,
						})
					}
				}
				this.scheduleList = list;
			},
			//点击列表
			handleClick(item) {
				console.log(item);
				uni.showModal({
					title: item.title,
					content: `${item.start_time} ~ ${item.end_time} (${item.week})`,
				})
			},

			//是否隐藏时间
			isHidTime(id) {
				let timeId = this.timeId;
				if (id == timeId || id == timeId + 1) {
					return true;
				} else {
					return false;
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.create_content_box {
		width: 100%;
		height: 100%;
		overflow: hidden;
		color: #ffffff;
		padding: 4rpx 0;
	}

	.dayTable {
		width: 100%;
		background-color: #ffffff;
	}

	.calendar_body::-webkit-scrollbar {
		display: none;
	}

	.calendar_body {
		width: calc(100vw - 8px);
		position: relative;
		overflow-x: hidden;
		transition: all .2s;

		.calendar_table {
			margin-top: 15px;
			position: absolute;
			top: 0;
			padding-bottom: 44rpx;
			z-index: 10;

			.cal_tr {
				width: 100%;
				height: 16px;
				display: flex;
				font-size: 0;
				color: #9f9f9f;
			}

			.left_time {
				line-height: 0;
				width: 50px;
				text-align: center;
			}

			.right_content {
				border-top: 1px solid #ffffff;
				width: calc(100vw - 50px);
			}

			.hasTime {
				font-size: 24rpx;
			}

			.hasTime .right_content {
				border-color: #f0f0f0;
			}
		}

		.calendar_schedule {
			margin-top: 15px;
			position: absolute;
			top: 0;
			right: 0;
			width: 86%;
			z-index: 20;

			// .redBg {
			// 	background-color: rgba(254, 222, 224, 1);
			// 	color: #F65B67;
			// 	// border-left: 4px solid #F65B67;
			// }

			// .blurBg {
			// 	// border-left: 4px solid #34BDA0;
			// 	color: #34BDA0;
			// 	background-color: rgba(222, 248, 234, 1);
			// }

			.schedule_a {
				font-size: 24rpx;
				left: 50px;
				right: 0px;
				position: absolute;
				cursor: pointer;
				border-radius: 4px;
			}

			.schedule_content_time {
				margin-left: 5px;
				min-width: 135px;
			}

			.schedule_content_name {
				// line-height: 100%;
				margin-left: 5px;
				width: 250px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.time_now {
			margin-top: 15px;
			display: flex;
			align-items: center;
			width: 100vw;
			height: 0.5px;
			position: absolute;
			z-index: 50;

			.left_text_red {
				text-align: center;
				width: 50px;
				color: #FF0000;
				font-size: 24rpx;
				line-height: 0;
			}

			.left_text_radio {
				width: 4px;
				height: 4px;
				background-color: #FF0000;
				border: 1rpx solid #ffffff;
				border-radius: 50%;
				line-height: 0;
				margin-left: -3px;
			}

			.time_line {
				flex-grow: 1;
				border: 0.5px solid red;
			}
		}
	}
</style>