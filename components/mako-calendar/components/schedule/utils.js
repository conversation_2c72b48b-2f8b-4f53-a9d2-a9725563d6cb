// 兼容ios时间格式
const formateIOS = (time) => {
	if (!time) return ''
	var myDate = new Date(time.replace(/-/g, "/"));
	return myDate.getTime();
}
const weeks = ["日", "一", "二", "三", "四", "五", "六"];
export const getWeek = (date) => {
	if (date !== undefined) {
		return weeks[new Date(date).getDay()] || null;
	} else return null;
};

export const handleCalendarData = (data) => {
	// 将数据按开始时间升序排列
	const newData = data
		.sort(
			(a, b) =>
			formateIOS(a.startTime) - formateIOS(b.startTime)
		);
	const tempArray = []; // 存储最终返回值，格式为二维数组
	const IndexList = []; // 记录已经处理过的数据下标，下次循环直接跳过
	for (let index = 0; index < newData.length; index++) {
		if (IndexList.includes(index)) continue;
		const startTime = formateIOS(newData[index].startTime);
		let endTime = formateIOS(newData[index].endTime);
		IndexList.push(index);
		const InnerArray = [newData[index]];
		for (let n = 1; n < newData.length; n++) {
			if (IndexList.includes(n)) continue;
			// 将日期转时间戳进行对比，如果n的开始时间大于等于index的开始时间，且小于等于index的结束时间，则说明时间有交叉，结束时间同理
			if (
				(formateIOS(newData[n].startTime) >= startTime &&
					formateIOS(newData[n].startTime) <= endTime) ||
				(formateIOS(newData[n].endTime) >= startTime &&
					formateIOS(newData[n].endTime) <= endTime)
			) {
				// 取两个结束时间中的最大值作为下一次循环对比的结束时间
				endTime = Math.max(formateIOS(newData[n].endTime), endTime);
				InnerArray.push(newData[n]);
				IndexList.push(n);
			}
		}
		tempArray.push(InnerArray);
	};
	return tempArray;

}