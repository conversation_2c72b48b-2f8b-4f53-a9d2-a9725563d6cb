<template>
  <view class="c-voice-container">
    <!-- 录音按钮（点击模式） -->
    <view
      class="voice-button-normal"
      :class="{
        'disabled': isDisabled,
        'recording': isRecording,
        'compact-mode': styleMode === 'compact'
      }"
      @tap="handleTap"
      @selectstart.prevent
      @contextmenu.prevent
    >
      <text class="voice-text">{{ isRecording ? '录音中...' : getButtonText() }}</text>
    </view>



    <!-- 录音时的弹窗界面 -->
    <view v-if="isRecording" class="recording-popup" >
      <view class="recording-content">
        <!-- 圆形进度条 -->
        <view class="circle-progress-container">
          <u-circle-progress
            :percent="progressPercentage"
            :width="280"
            :border-width="10"
            :duration="800"
            active-color="#1890ff"
            inactive-color="rgba(255,255,255,0.25)"
            bg-color="transparent"
          >
            <view class="progress-inner">
              <view class="mic-icon" >
                🎤
              </view>
              <text class="remaining-text" >{{ formatTime(remainingTime) }}</text>
            </view>
          </u-circle-progress>
        </view>

        <!-- 状态提示 -->
        <view class="status-tip">
          <text class="tip-text">
            {{ recordingTip }}
          </text>
        </view>

        <!-- 音频波形动画 -->
        <view class="audio-wave">
          <view
            v-for="(bar, index) in audioWaveBars"
            :key="index"
            class="wave-bar"
            :style="{
              height: bar.height + 'rpx',
              animationDelay: bar.delay + 's'
            }"
          ></view>
        </view>

        <!-- 录音操作按钮 -->
        <view class="popup-actions">
          <view class="popup-action-button cancel-button" @tap="handleCancelTap">
            <text class="popup-action-emoji">🚫</text>
            <text class="popup-action-text">取消</text>
          </view>
          <view class="popup-action-button finish-button" @tap="handleFinishTap">
            <text class="popup-action-emoji">✅</text>
            <text class="popup-action-text">发送</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 录音时长过短提示 -->
    <u-toast ref="toast" />

    <!-- 调试信息 -->
    <!-- #ifdef H5 -->
    <view v-if="showDebugInfo" class="debug-info">
      <text class="debug-text">调试信息:</text>
      <text class="debug-text">浏览器: {{ userAgent }}</text>
      <text class="debug-text">协议: {{ protocol }}</text>
      <text class="debug-text">域名: {{ hostname }}</text>
      <text class="debug-text">录音状态: {{ isRecording ? '录音中' : '未录音' }}</text>
      <text class="debug-text">禁用状态: {{ isDisabled ? '已禁用' : '可用' }}</text>
      <button @click="testRecordingSupport" class="debug-btn">测试录音支持</button>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
/**
 * cVoice 录音组件
 * 功能：长按录音，支持录音动画，可设置最短和最长时长，集成语音识别功能
 * 作者：AI Assistant
 * 版本：1.1.0
 *
 * 新增功能：
 * - 集成语音识别接口 /chat/stt
 * - 支持开启/关闭语音识别功能
 * - 返回识别文本结果
 * - 支持语音识别完成后自动发送功能
 *
 * 事件说明：
 * - @success: 录音成功事件，包含录音信息和识别结果
 * - @recognition-success: 语音识别成功事件
 * - @recognition-error: 语音识别失败事件
 * - @auto-send: 自动发送事件（当启用autoSendAfterRecognition且识别成功时触发）
 * - @start: 开始录音事件
 * - @cancel: 取消录音事件
 * - @error: 录音错误事件
 */
import uCircleProgress from './u-circle-progress.vue'
import { getConfig } from '@/framework/config/config'

export default {
  name: 'cVoice',
  components: {
    uCircleProgress
  },
  props: {
    // 最短录音时长（秒）
    minDuration: {
      type: Number,
      default: 2
    },
    // 最长录音时长（秒）
    maxDuration: {
      type: Number,
      default: 60
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 录音格式 mp3 或 wav
    format: {
      type: String,
      default: 'mp3',
      validator: (value) => ['mp3', 'wav'].includes(value)
    },
    // 录音类型：只支持点击模式
    recordType: {
      type: String,
      default: 'click',
      validator: (value) => ['click'].includes(value)
    },
    // 样式模式：'default' 默认模式，'compact' 紧凑模式（用于AI弹窗）
    styleMode: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'compact'].includes(value)
    },
    // 是否启用语音识别
    enableRecognition: {
      type: Boolean,
      default: true
    },
    // 语音识别完成后是否自动发送（识别到内容不为空时直接发送，不需要手动点击确定）
    autoSendAfterRecognition: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      isRecording: false,        // 是否正在录音
      isDisabled: false,         // 是否禁用状态
      recordingTime: 0,          // 录音时长
      recordingTimer: null,      // 录音计时器
      mediaRecorder: null,       // H5录音器
      audioChunks: [],           // 录音数据块
      stream: null,              // 媒体流
      recorderManager: null,     // uni录音管理器（非H5）
      showDebugInfo: false,      // 显示调试信息
      userAgent: '',             // 用户代理
      protocol: '',              // 协议
      hostname: '',              // 域名
      audioWaveBars: [],         // 音频波形条数据
      smoothProgressTimer: null  // 平滑进度更新计时器
    }
  },

  computed: {
    // 录音提示文本
    recordingTip() {
      if (this.recordingTime < this.minDuration) {
        return `请继续录音，最少需要${this.minDuration}秒`
      } else if (this.recordingTime >= this.maxDuration - 5) {
        return `即将达到最大时长`
      } else {
        return '正在录音中...'
      }
    },

    // 进度百分比
    progressPercentage() {
      if (!this.isRecording) return 0

      // 使用recordingTime计算百分比
      // 例如：maxDuration=20秒，recordingTime=1秒，则进度=1/20*100=5%
      const progress = Math.min((this.recordingTime / this.maxDuration) * 100, 100)

      return Math.round(progress * 10) / 10 // 保留一位小数，避免小数点过多
    },

    // 剩余时间
    remainingTime() {
      return Math.max(this.maxDuration - this.recordingTime, 0)
    }
  },

  mounted() {
    // #ifdef H5
    this.userAgent = navigator.userAgent
    this.protocol = location.protocol
    this.hostname = location.hostname

    // 开发环境显示调试信息
    // this.showDebugInfo = process.env.NODE_ENV === 'development'
    // #endif

    this.initAudioWave()
    this.checkRecordingSupport()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    /**
     * 获取按钮文本
     */
    getButtonText() {
      // 紧凑模式（AI弹窗专用）
      if (this.styleMode === 'compact') {
        return '🎤 点击语音输入'
      }

      // 默认模式
      return '点击开始录音'
    },

    /**
     * 初始化音频波形
     */
    initAudioWave() {
      // 生成30个波形条
      this.audioWaveBars = []
      for (let i = 0; i < 45; i++) {
        this.audioWaveBars.push({
          height: Math.random() * 40 + 10, // 10-50rpx的随机高度
          delay: Math.random() * 2 // 0-2秒的随机延迟
        })
      }
    },

    /**
     * 检查录音支持
     */
    checkRecordingSupport() {
      // #ifdef H5
      console.log('🔍 开始检查H5录音支持...')
      console.log('User Agent:', navigator.userAgent)
      console.log('是否为移动设备:', /Mobile|Android|iPhone|iPad/.test(navigator.userAgent))
      console.log('当前协议:', location.protocol)
      console.log('当前域名:', location.hostname)

      if (!navigator.mediaDevices) {
        console.error('❌ navigator.mediaDevices 不存在')
        this.showMobileRecordingTip()
        return
      }

      if (!navigator.mediaDevices.getUserMedia) {
        console.error('❌ getUserMedia 不支持')
        this.showMobileRecordingTip()
        return
      }

      if (!window.MediaRecorder) {
        console.error('❌ MediaRecorder 不支持')
        this.showMobileRecordingTip()
        return
      }

      console.log('✅ H5录音API支持检查通过')

      // 检查权限状态
      if (navigator.permissions) {
        navigator.permissions.query({name: 'microphone'}).then(result => {
          console.log('🎤 麦克风权限状态:', result.state)
        }).catch(err => {
          console.log('🎤 无法查询麦克风权限:', err)
        })
      }
      // #endif

      // #ifndef H5
      // 非H5平台使用uni录音API
      try {
        this.recorderManager = uni.getRecorderManager()
        this.setupUniRecorder()
      } catch (error) {
        console.error('初始化录音管理器失败', error)
        this.isDisabled = true
      }
      // #endif
    },

    /**
     * 显示移动端录音提示
     */
    showMobileRecordingTip() {
      // #ifdef H5
      this.isDisabled = true
      console.log('❌ 录音功能不可用，浏览器不支持')
      // #endif
    },

    /**
     * 设置uni录音管理器（非H5平台）
     */
    setupUniRecorder() {
      // #ifndef H5
      if (!this.recorderManager) return

      this.recorderManager.onStart(() => {
        console.log('录音开始')
        this.startRecordingTimer()
      })

      this.recorderManager.onStop((res) => {
        console.log('录音结束', res)
        this.handleRecordingComplete(res)
      })

      this.recorderManager.onError((err) => {
        console.error('录音错误', err)
        this.handleRecordingError(err)
      })
      // #endif
    },

    /**
     * 处理tap事件（点击模式）
     */
    handleTap(e) {
      console.log('👆 tap事件触发，当前录音状态:', this.isRecording)

      if (this.disabled || this.isDisabled) {
        console.log('❌ 录音被禁用，忽略tap事件')
        return
      }

      // 阻止默认行为和事件冒泡
      e.preventDefault()
      e.stopPropagation()

      // 只处理开始录音，结束和取消通过专门的按钮处理
      if (!this.isRecording) {
        console.log('🎤 点击开始录音')
        this.startRecording()
      }
    },

    /**
     * 处理取消录音按钮点击
     */
    handleCancelTap(e) {
      console.log('❌ 点击取消录音')
      e.preventDefault()
      e.stopPropagation()
      this.cancelRecording()
    },

    /**
     * 处理结束录音按钮点击
     */
    handleFinishTap(e) {
      console.log('🛑 点击结束录音')
      e.preventDefault()
      e.stopPropagation()
      this.stopRecording()
    },



    /**
     * 开始录音
     */
    async startRecording() {
      try {
        this.isRecording = true
        this.recordingTime = 0
        this.audioChunks = []

        // #ifdef H5
        await this.startH5Recording()
        // #endif

        // #ifndef H5
        this.startUniRecording()
        // #endif

        // 触发开始录音事件
        this.$emit('start')

      } catch (error) {
        console.error('开始录音失败', error)
        this.isRecording = false
        this.$emit('error', { type: 'start_failed', error })
      }
    },

    /**
     * H5录音
     */
    async startH5Recording() {
      // #ifdef H5
      try {
        console.log('🎤 开始H5录音初始化...')

        // 获取麦克风权限
        console.log('🎤 请求麦克风权限...')

        const constraints = {
          audio: {
            sampleRate: 16000,  // 腾讯云推荐的采样率
            channelCount: 1,    // 单声道
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        }

        this.stream = await navigator.mediaDevices.getUserMedia(constraints)
        console.log('✅ 麦克风权限获取成功')

        // 检测微信环境
        const isWechat = /micromessenger/i.test(navigator.userAgent)

        // 优先选择腾讯云支持的音频格式
        let mimeType = 'audio/webm'
        let tencentSupportedTypes = []

        if (isWechat) {
          // 微信环境优先使用webm+opus格式
          tencentSupportedTypes = [
            'audio/webm;codecs=opus', // 微信首选格式
            'audio/webm',             // 微信备选格式
            'audio/wav',              // 标准格式
            'audio/mpeg',             // MP3格式
            'audio/mp4'               // M4A格式
          ]
        } else {
          // 其他环境优先使用标准格式
          tencentSupportedTypes = [
            'audio/wav',              // 腾讯云首选格式
            'audio/mpeg',             // MP3格式
            'audio/mp4',              // M4A格式
            'audio/webm;codecs=opus', // OPUS格式
            'audio/webm'              // 备选格式
          ]
        }

        console.log('🔍 检查腾讯云支持的音频格式 (微信环境:', isWechat, '):')
        for (const type of tencentSupportedTypes) {
          const supported = MediaRecorder.isTypeSupported(type)
          console.log(`  ${type}: ${supported ? '✅' : '❌'}`)
          if (supported && mimeType === 'audio/webm') {
            mimeType = type
            break
          }
        }

        console.log('🎵 选择音频格式:', mimeType)

        // 创建MediaRecorder
        const options = { mimeType }
        this.mediaRecorder = new MediaRecorder(this.stream, options)

        // 录音数据事件
        this.mediaRecorder.ondataavailable = (event) => {
          console.log('📦 收到录音数据块，大小:', event.data.size)
          if (event.data.size > 0) {
            this.audioChunks.push(event.data)
          }
        }

        // 录音停止事件
        this.mediaRecorder.onstop = () => {
          console.log('🛑 MediaRecorder停止事件触发')
          this.handleH5RecordingComplete()
        }

        // 录音错误事件
        this.mediaRecorder.onerror = (event) => {
          console.error('❌ MediaRecorder错误:', event.error)
          this.handleRecordingError({ type: 'mediarecorder_error', error: event.error })
        }

        // 开始录音
        console.log('🎤 开始录音...')
        this.mediaRecorder.start(100) // 每100ms收集一次数据
        this.startRecordingTimer()

        console.log('✅ H5录音启动成功')

      } catch (error) {
        console.error('❌ H5录音初始化失败:', error)

        // 详细的错误处理
        let errorMessage = 'H5录音失败: '
        if (error.name === 'NotAllowedError') {
          errorMessage += '用户拒绝了麦克风权限'
        } else if (error.name === 'NotFoundError') {
          errorMessage += '未找到麦克风设备'
        } else if (error.name === 'NotSupportedError') {
          errorMessage += '浏览器不支持录音功能'
        } else if (error.name === 'SecurityError') {
          errorMessage += '安全限制，需要HTTPS环境'
        } else {
          errorMessage += error.message
        }

        // 不显示toast，只在控制台记录错误

        throw new Error(errorMessage)
      }
      // #endif
    },

    /**
     * uni录音（非H5平台）
     */
    startUniRecording() {
      // #ifndef H5
      if (!this.recorderManager) {
        throw new Error('录音管理器未初始化')
      }

      const options = {
        duration: this.maxDuration * 1000,
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: this.format === 'mp3' ? 'mp3' : 'wav'
      }

      this.recorderManager.start(options)
      // #endif
    },

    /**
     * 停止录音
     */
    stopRecording() {
      if (!this.isRecording) {
        return
      }

      // 检查录音时长
      if (this.recordingTime < this.minDuration) {
        console.log(`❌ 录音时间太短，至少需要${this.minDuration}秒`)
        this.cancelRecording()
        return
      }

      try {
        // #ifdef H5
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop()
        }
        // #endif

        // #ifndef H5
        if (this.recorderManager) {
          this.recorderManager.stop()
        }
        // #endif

      } catch (error) {
        console.error('停止录音失败', error)
        this.handleRecordingError({ type: 'stop_failed', error })
      }
    },

    /**
     * 取消录音
     */
    cancelRecording() {
      try {
        console.log('❌ 开始取消录音...')
        this.isRecording = false
        this.stopRecordingTimer()

        // #ifdef H5
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop()
        }
        this.cleanupH5Resources()
        // #endif

        // #ifndef H5
        if (this.recorderManager) {
          this.recorderManager.stop()
        }
        // #endif

        // 显示取消提示
        // this.$toast('录音已取消')

        console.log('✅ 录音取消完成')
        this.$emit('cancel')
      } catch (error) {
        console.error('取消录音失败', error)
      }
    },

    /**
     * 开始录音计时
     */
    startRecordingTimer() {
      this.recordingTimer = setInterval(() => {
        this.recordingTime++

        // 更新音频波形（每秒更新）
        this.updateAudioWave()

        // 达到最大时长自动停止
        if (this.recordingTime >= this.maxDuration) {
          console.log('⏰ 达到最大录音时长，自动停止录音')
          this.stopRecording()
        }
      }, 1000)

      // 启动平滑进度更新（每100ms更新一次，让进度条更平滑）
      this.startSmoothProgress()
    },

    /**
     * 启动平滑进度更新
     */
    startSmoothProgress() {
      this.smoothProgressTimer = setInterval(() => {
        // 强制触发进度计算属性更新
        this.$forceUpdate()
      }, 100)
    },

    /**
     * 停止平滑进度更新
     */
    stopSmoothProgress() {
      if (this.smoothProgressTimer) {
        clearInterval(this.smoothProgressTimer)
        this.smoothProgressTimer = null
      }
    },

    /**
     * 更新音频波形
     */
    updateAudioWave() {
      // 随机更新波形条的高度，模拟音频变化
      this.audioWaveBars.forEach(bar => {
        bar.height = Math.random() * 60 + 20 // 20-80rpx的随机高度
        bar.delay = Math.random() * 0.5 // 0-0.5秒的随机延迟
      })
    },

    /**
     * 停止录音计时
     */
    stopRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer)
        this.recordingTimer = null
      }
      this.stopSmoothProgress()
    },

    /**
     * 处理H5录音完成
     */
    async handleH5RecordingComplete() {
      // #ifdef H5
      this.isRecording = false
      this.stopRecordingTimer()

      try {
        // 创建音频Blob
        const audioBlob = new Blob(this.audioChunks, {
          type: this.mediaRecorder.mimeType || 'audio/webm'
        })

        // 创建临时URL
        const tempFilePath = URL.createObjectURL(audioBlob)

        console.log('🎤 录音完成 - H5平台', {
          tempFilePath: tempFilePath,
          duration: this.recordingTime * 1000,
          fileSize: audioBlob.size,
          format: this.format,
          mimeType: this.mediaRecorder.mimeType
        })

        // 播放录音
        // this.playRecording(tempFilePath)

        // 录音信息对象
        const recordingInfo = {
          tempFilePath: tempFilePath,
          duration: this.recordingTime * 1000,
          fileSize: audioBlob.size,
          format: this.format,
          blob: audioBlob // H5特有，提供blob对象
        }

        // 如果启用语音识别，则进行识别
        if (this.enableRecognition) {
          // 获取实际的MIME类型和对应的文件扩展名
          const actualMimeType = this.mediaRecorder.mimeType || 'audio/webm'
          let fileName = 'recording.wav'  // 默认使用WAV格式
          let tencentFormat = 'wav'       // 腾讯云格式标识

          // 检测微信环境
          const isWechat = /micromessenger/i.test(navigator.userAgent)

          console.log('🔍 录音格式检测:', {
            actualMimeType,
            isWechat,
            userAgent: navigator.userAgent
          })

          // 根据实际MIME类型确定腾讯云支持的格式
          if (actualMimeType.includes('wav')) {
            fileName = 'recording.wav'
            tencentFormat = 'wav'
          } else if (actualMimeType.includes('mpeg')) {
            fileName = 'recording.mp3'
            tencentFormat = 'mp3'
          } else if (actualMimeType.includes('mp4')) {
            fileName = 'recording.m4a'
            tencentFormat = 'm4a'
          } else if (actualMimeType.includes('opus') || actualMimeType.includes('webm')) {
            // 微信环境下的特殊处理 - 后端不支持opus，强制转换为wav
            if (isWechat && actualMimeType.includes('webm')) {
              // 微信录音是webm格式但包含opus编码，后端不支持opus，强制使用wav
              fileName = 'recording.wav'
              tencentFormat = 'wav'
              console.log('🎤 微信环境检测到webm+opus，后端不支持opus，强制使用wav格式')
            } else if (actualMimeType.includes('opus')) {
              // 后端不支持opus，强制使用wav
              fileName = 'recording.wav'
              tencentFormat = 'wav'
              console.log('🎤 检测到opus格式，后端不支持，强制使用wav格式')
            } else {
              fileName = 'recording.wav'
              tencentFormat = 'wav'
              console.log('🎤 检测到webm格式，强制使用wav格式')
            }
          } else {
            // 如果是其他格式，统一使用wav
            console.log('⚠️ 检测到其他格式，强制使用wav')
            fileName = 'recording.wav'
            tencentFormat = 'wav'
          }

          console.log('🎵 创建音频文件:', {
            fileName,
            originalMimeType: actualMimeType,
            tencentFormat,
            size: audioBlob.size
          })

          // 检查是否需要格式转换
          if (tencentFormat === 'wav' && !actualMimeType.includes('wav')) {
            console.log('🔄 需要将音频转换为WAV格式')
            try {
              // 尝试转换音频格式
              const convertedBlob = await this.convertAudioToWav(audioBlob)
              const audioFile = new File([convertedBlob], fileName, {
                type: 'audio/wav'
              })
              console.log('✅ 音频转换成功，新文件大小:', convertedBlob.size)
              this.performSpeechRecognition(audioFile, recordingInfo, tencentFormat)
            } catch (convertError) {
              console.error('❌ 音频转换失败，使用原始格式:', convertError)
              // 转换失败，使用原始文件但修改MIME类型
              const audioFile = new File([audioBlob], fileName, {
                type: 'audio/wav'  // 强制设置为wav类型
              })
              this.performSpeechRecognition(audioFile, recordingInfo, tencentFormat)
            }
          } else {
            // 不需要转换，直接使用
            const audioFile = new File([audioBlob], fileName, {
              type: actualMimeType
            })
            this.performSpeechRecognition(audioFile, recordingInfo, tencentFormat)
          }
        } else {
          // 不启用语音识别，直接返回录音信息
          this.$emit('success', {
            ...recordingInfo,
            recognitionText: null,
            recognitionSupported: false
          })
        }

      } catch (error) {
        console.error('处理H5录音完成失败', error)
        this.handleRecordingError({ type: 'process_failed', error })
      } finally {
        this.cleanupH5Resources()
      }
      // #endif
    },

    /**
     * 处理uni录音完成（非H5平台）
     */
    handleRecordingComplete(res) {
      // #ifndef H5
      this.isRecording = false
      this.stopRecordingTimer()

      console.log('🎤 录音完成 - 非H5平台', {
        tempFilePath: res.tempFilePath,
        duration: res.duration || this.recordingTime * 1000,
        fileSize: res.fileSize,
        format: this.format
      })

      // 播放录音
      this.playRecording(res.tempFilePath)

      // 非H5平台暂不支持语音识别，直接返回录音信息
      this.$emit('success', {
        tempFilePath: res.tempFilePath,
        duration: res.duration || this.recordingTime * 1000,
        fileSize: res.fileSize,
        format: this.format,
        recognitionText: null, // 非H5平台暂不支持语音识别
        recognitionSupported: false
      })
      // #endif
    },

    /**
     * 播放录音
     */
    playRecording(filePath) {
      try {
        // #ifdef H5
        const audio = new Audio(filePath)
        audio.play().then(() => {
          console.log('🔊 开始播放录音')
        }).catch(err => {
          console.error('播放录音失败', err)
        })

        audio.onended = () => {
          console.log('🔊 录音播放完成')
        }
        // #endif

        // #ifndef H5
        const audioContext = uni.createInnerAudioContext()
        audioContext.src = filePath
        audioContext.play()

        audioContext.onPlay(() => {
          console.log('🔊 开始播放录音')
        })

        audioContext.onEnded(() => {
          console.log('🔊 录音播放完成')
        })

        audioContext.onError((error) => {
          console.error('播放录音失败', error)
        })
        // #endif

      } catch (error) {
        console.error('播放录音异常', error)
      }
    },

    /**
     * 清理H5资源
     */
    cleanupH5Resources() {
      // #ifdef H5
      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop())
        this.stream = null
      }

      if (this.mediaRecorder) {
        this.mediaRecorder = null
      }

      this.audioChunks = []
      // #endif
    },

    /**
     * 处理录音错误
     */
    handleRecordingError(error) {
      this.isRecording = false
      this.stopRecordingTimer()

      this.$emit('error', error)
    },

    /**
     * 格式化时间显示
     */
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    /**
     * 将音频转换为WAV格式
     * @param {Blob} audioBlob - 原始音频Blob
     * @returns {Promise<Blob>} 转换后的WAV格式Blob
     */
    async convertAudioToWav(audioBlob) {
      return new Promise((resolve, reject) => {
        try {
          // #ifdef H5
          console.log('🔄 开始音频格式转换...')

          // 创建音频上下文
          const audioContext = new (window.AudioContext || window.webkitAudioContext)()
          const fileReader = new FileReader()

          fileReader.onload = async (e) => {
            try {
              // 解码音频数据
              const arrayBuffer = e.target.result
              const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

              // 转换为WAV格式
              const wavBlob = this.audioBufferToWav(audioBuffer)
              console.log('✅ 音频转换完成')
              resolve(wavBlob)
            } catch (decodeError) {
              console.error('❌ 音频解码失败:', decodeError)
              reject(decodeError)
            }
          }

          fileReader.onerror = (error) => {
            console.error('❌ 文件读取失败:', error)
            reject(error)
          }

          fileReader.readAsArrayBuffer(audioBlob)
          // #endif

          // #ifndef H5
          // 非H5环境不支持转换，直接返回原始数据
          resolve(audioBlob)
          // #endif
        } catch (error) {
          console.error('❌ 音频转换异常:', error)
          reject(error)
        }
      })
    },

    /**
     * 将AudioBuffer转换为WAV格式的Blob
     * @param {AudioBuffer} audioBuffer - 音频缓冲区
     * @returns {Blob} WAV格式的Blob
     */
    audioBufferToWav(audioBuffer) {
      const numChannels = audioBuffer.numberOfChannels
      const sampleRate = audioBuffer.sampleRate
      const format = 1 // PCM
      const bitDepth = 16

      const bytesPerSample = bitDepth / 8
      const blockAlign = numChannels * bytesPerSample

      const buffer = audioBuffer.getChannelData(0)
      const length = buffer.length
      const arrayBuffer = new ArrayBuffer(44 + length * bytesPerSample)
      const view = new DataView(arrayBuffer)

      // WAV文件头
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i))
        }
      }

      writeString(0, 'RIFF')
      view.setUint32(4, 36 + length * bytesPerSample, true)
      writeString(8, 'WAVE')
      writeString(12, 'fmt ')
      view.setUint32(16, 16, true)
      view.setUint16(20, format, true)
      view.setUint16(22, numChannels, true)
      view.setUint32(24, sampleRate, true)
      view.setUint32(28, sampleRate * blockAlign, true)
      view.setUint16(32, blockAlign, true)
      view.setUint16(34, bitDepth, true)
      writeString(36, 'data')
      view.setUint32(40, length * bytesPerSample, true)

      // 写入音频数据
      let offset = 44
      for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, buffer[i]))
        view.setInt16(offset, sample * 0x7FFF, true)
        offset += 2
      }

      return new Blob([arrayBuffer], { type: 'audio/wav' })
    },

    /**
     * 执行语音识别
     * @param {File} audioFile - 音频文件对象
     * @param {Object} recordingInfo - 录音信息
     * @param {String} tencentFormat - 腾讯云支持的音频格式
     */
    async performSpeechRecognition(audioFile, recordingInfo, tencentFormat = 'wav') {
      try {
        // 检测环境信息
        const isWechat = /micromessenger/i.test(navigator.userAgent)
        const userAgent = navigator.userAgent

        console.log('🔍 开始语音识别...', {
          fileName: audioFile.name,
          fileSize: audioFile.size,
          fileType: audioFile.type,
          tencentFormat: tencentFormat,
          isWechat: isWechat,
          userAgent: userAgent,
          actualMimeType: recordingInfo.blob ? recordingInfo.blob.type : 'unknown'
        })

        // 显示识别中的提示
        uni.showLoading({
          title: '识别中...',
          mask: true
        })

        // 创建FormData
        const formData = new FormData()
        formData.append('audioFile', audioFile)
        formData.append('format', tencentFormat)  // 添加格式参数

        // 添加环境信息用于后端调试
        formData.append('userAgent', userAgent)
        formData.append('isWechat', isWechat.toString())

        // 调用语音识别接口
        const response = await fetch(getConfig().baseURL + '/chat/stt', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${uni.getStorageSync(getConfig().tokenName)}`
          },
          body: formData
        })

        const data = await response.json()

        uni.hideLoading()

        if (data.back === 1) {
          console.log('✅ 语音识别成功:', data.data.text)

          // 触发成功事件，包含识别结果
          this.$emit('success', {
            ...recordingInfo,
            recognitionText: data.data.text,
            recognitionData: data.data,
            recognitionSupported: true
          })

          // 触发专门的识别成功事件
          this.$emit('recognition-success', {
            text: data.data.text,
            data: data.data
          })

          // 检查是否需要自动发送
          if (this.autoSendAfterRecognition && data.data.text && data.data.text.trim()) {
            console.log('🚀 自动发送模式：识别到内容，准备自动发送')

            // 延迟一小段时间让用户看到识别结果，然后自动发送
            setTimeout(() => {
              this.$emit('auto-send', {
                text: data.data.text,
                recognitionData: data.data,
                recordingInfo: recordingInfo
              })
            }, 500) // 延迟500ms让用户看到识别结果
          }

        } else {
          console.error('❌ 语音识别失败:', data.msg)

          // 识别失败，但录音成功，仍然返回录音信息
          this.$emit('success', {
            ...recordingInfo,
            recognitionText: null,
            recognitionError: data.msg,
            recognitionSupported: true
          })

          // 触发识别失败事件
          this.$emit('recognition-error', {
            error: data.msg,
            code: data.code
          })

          uni.showToast({
            title: '语音识别失败',
            icon: 'none',
            duration: 2000
          })
        }

      } catch (error) {
        console.error('❌ 语音识别请求失败:', error)

        uni.hideLoading()

        // 网络错误或其他异常，但录音成功，仍然返回录音信息
        this.$emit('success', {
          ...recordingInfo,
          recognitionText: null,
          recognitionError: error.message,
          recognitionSupported: true
        })

        // 触发识别错误事件
        this.$emit('recognition-error', {
          error: error.message,
          type: 'network_error'
        })

        uni.showToast({
          title: '识别服务异常',
          icon: 'none',
          duration: 2000
        })
      }
    },



    /**
     * 测试录音支持（调试用）
     */
    async testRecordingSupport() {
      // #ifdef H5
      console.log('🧪 开始测试录音支持...')

      try {
        // 测试getUserMedia
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        console.log('✅ getUserMedia 测试成功')

        // 测试MediaRecorder
        const recorder = new MediaRecorder(stream)
        console.log('✅ MediaRecorder 创建成功')

        // 停止测试流
        stream.getTracks().forEach(track => track.stop())

        console.log('✅ 录音功能测试通过')
        this.isDisabled = false

      } catch (error) {
        console.error('❌ 录音功能测试失败:', error)
        this.isDisabled = true
      }
      // #endif
    },

    /**
     * 清理资源
     */
    cleanup() {
      this.stopRecordingTimer()

      // #ifdef H5
      this.cleanupH5Resources()
      // #endif

      // #ifndef H5
      if (this.recorderManager) {
        try {
          this.recorderManager.stop()
        } catch (error) {
          // 忽略停止错误
        }
      }
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.c-voice-container {
  position: relative;
  width: 100%;

  // 禁止选择和长按
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none !important;

  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none !important ;

    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }
}

// 普通状态下的录音按钮
.voice-button-normal {
  width: 100%;
  height: 72rpx;
  border-radius: 8rpx;
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  user-select: none;

  &:active {
    background: #ebebeb;
    border-color: #d9d9d9;
  }

  &.disabled {
    background: #f5f5f5;
    border-color: #d9d9d9;
    opacity: 0.6;
  }

  .voice-text {
    font-size: 30rpx;
    color: #333333;
    font-weight: 400;
  }

  &.disabled .voice-text {
    color: #999999;
  }

  // 录音中状态样式
  &.recording {
    background: #e6f7ff;
    border-color: #91d5ff;
    pointer-events: none; // 防止重复点击

    .voice-text {
      color: #1890ff;
      animation: recordingPulse 1.5s ease-in-out infinite;
    }

    &:active {
      background: #e6f7ff; // 保持不变
      border-color: #91d5ff; // 保持不变
    }
  }

  // 紧凑模式样式（AI弹窗专用）
  &.compact-mode {
    width: 200rpx;
    height: 60rpx;
    border-radius: 30rpx;
    background: rgba(24, 144, 255, 0.1);
    border: 1rpx solid rgba(24, 144, 255, 0.2);
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15);

    .voice-text {
      font-size: 24rpx;
      color: #1890ff;
      font-weight: 500;
    }

    &:active {
      background: rgba(24, 144, 255, 0.15);
      border-color: rgba(24, 144, 255, 0.3);
      transform: scale(0.98);
    }

    &.disabled {
      background: rgba(0, 0, 0, 0.05);
      border-color: rgba(0, 0, 0, 0.1);
      box-shadow: none;

      .voice-text {
        color: #999999;
      }
    }

    // 紧凑模式下的录音中状态
    &.recording {
      background: rgba(24, 144, 255, 0.2);
      border-color: rgba(24, 144, 255, 0.4);
      pointer-events: none; // 防止重复点击

      .voice-text {
        color: #1890ff;
        animation: recordingPulse 1.5s ease-in-out infinite;
      }

      &:active {
        background: rgba(24, 144, 255, 0.2); // 保持不变
        border-color: rgba(24, 144, 255, 0.4); // 保持不变
        transform: none; // 取消缩放效果
      }
    }
  }
}

// 弹窗内的录音操作按钮
.popup-actions {
  display: flex;
  gap: 16rpx;
  width: 100%;
  margin-top: 32rpx;
  pointer-events: auto; // 恢复触摸事件

  .popup-action-button {
    height: 80rpx;
    border-radius: 16rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    transition: all 0.2s ease;
    user-select: none;
    border: 2px solid transparent;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &:active {
      opacity: 0.8;
      transform: translateY(2rpx);
    }

    .popup-action-emoji {
      font-size: 24rpx;
      line-height: 1;
    }

    .popup-action-text {
      font-size: 26rpx;
      font-weight: 600;
      line-height: 1;
    }
  }

  .cancel-button {
    flex: 1;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    border-color: rgba(220, 220, 220, 0.8);
    backdrop-filter: blur(10rpx);

    .popup-action-emoji {
      color: #ff7875;
    }

    .popup-action-text {
      color: #595959;
    }

    &:active {
      background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(230, 230, 230, 0.95));
      border-color: rgba(200, 200, 200, 0.9);
    }
  }

  .finish-button {
    flex: 2;
    background: linear-gradient(135deg, #1890ff, #096dd9);
    border-color: #1890ff;
    box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);

    .popup-action-emoji {
      color: #b7eb8f;
    }

    .popup-action-text {
      color: white;
    }

    &:active {
      background: linear-gradient(135deg, #096dd9, #0050b3);
      border-color: #096dd9;
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.4);
    }
  }


}



// 录音时的弹窗界面
.recording-popup {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 85%;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 16rpx;
  z-index: 9999;
  padding: 32rpx;
  pointer-events: none; // 不阻止下层按钮的触摸事件
  transition: all 0.3s ease;

}

// 录音内容区域
.recording-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

// 圆形进度条容器
.circle-progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  min-height: 200rpx; // 与进度条尺寸匹配
}

// 进度条内部内容
.progress-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  width: 100%;
  height: 100%;

  .mic-icon {
    font-size: 48rpx;
    margin-bottom: 16rpx;
    animation: pulse 1.5s ease-in-out infinite;
    transition: all 0.3s ease;
  }

  .remaining-text {
    font-size: 32rpx;
    color: white;
    font-weight: 600;
    text-align: center;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 状态提示
.status-tip {
  margin-bottom: 24rpx;

  .tip-text {
    font-size: 26rpx;
    color: white;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
  }
}

// 音频波形动画
.audio-wave {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 64rpx;
  margin-bottom: 24rpx;
  gap: 4rpx;
  min-width: 90%;
}

.wave-bar {
  width: 5rpx;
  background: linear-gradient(to top, #1890ff, #69c0ff);
  border-radius: 2rpx;
  animation: waveAnimation 1.5s ease-in-out infinite;
  min-height: 6rpx;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.3);
    opacity: 0.6;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}



// 调试信息
.debug-info {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  z-index: 10000;
  max-width: 400rpx;

  .debug-text {
    display: block;
    margin-bottom: 8rpx;
    word-break: break-all;
  }

  .debug-btn {
    margin-top: 16rpx;
    padding: 12rpx 24rpx;
    background: #07c160;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 24rpx;
  }
}


</style>
