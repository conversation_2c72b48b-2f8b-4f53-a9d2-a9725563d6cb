<!--
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01 18:42:12
-->
<template>

    <view
			class="floating-chat-btn"
			:style="{
				right: floatingBtnPosition.right + 'rpx',
				bottom: floatingBtnPosition.bottom + 'rpx',
			}"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd"
			@click="handleClick"
		>
			<text class="chat-icon">{{ icon }}</text>
		</view>
</template>
<script>
export default {
  name: "",
  props: {
    icon: {
      type: String,
      default: "💬",
    },
  },
	data() {
    return {
      // 悬浮按钮位置和拖拽状态
			floatingBtnPosition: {
				right: 40, // 距离右边的距离
				bottom: 40, // 距离底部的距离
			},
			isDragging: false, // 是否正在拖拽
			startPosition: {
				// 拖拽开始位置
				x: 0,
				y: 0,
			},
			startBtnPosition: {
				// 按钮拖拽开始时的位置
				right: 40,
				bottom: 40,
			},
    };
	},
	mounted() {},
  methods: {
    handleClick(){
      this.$emit("click");
    },
    // 悬浮按钮拖拽相关方法
		onTouchStart(e) {
			this.isDragging = false;
			this.startPosition = {
				x: e.touches[0].clientX,
				y: e.touches[0].clientY,
			};
			this.startBtnPosition = {
				right: this.floatingBtnPosition.right,
				bottom: this.floatingBtnPosition.bottom,
			};
		},

		onTouchMove(e) {
			// 防止页面滚动
			e.preventDefault();

			const currentX = e.touches[0].clientX;
			const currentY = e.touches[0].clientY;

			// 计算移动距离
			const deltaX = currentX - this.startPosition.x;
			const deltaY = currentY - this.startPosition.y;

			// 如果移动距离超过阈值，则认为是拖拽
			if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
				this.isDragging = true;
			}

			if (this.isDragging) {
				// 获取屏幕尺寸
				const systemInfo = uni.getSystemInfoSync();
				const screenWidth = systemInfo.screenWidth;
				const screenHeight = systemInfo.screenHeight;

				// rpx转px的比例
				const rpxToPx = screenWidth / 750;

				// 按钮尺寸和边距（转换为px）
				const btnSize = 120 * rpxToPx;
				const margin = 20 * rpxToPx;

				// 计算按钮当前的left和top位置（px单位）
				const currentLeftPx =
					screenWidth -
					this.startBtnPosition.right * rpxToPx -
					btnSize;
				const currentTopPx =
					screenHeight -
					this.startBtnPosition.bottom * rpxToPx -
					btnSize;

				// 计算新的left和top位置
				let newLeftPx = currentLeftPx + deltaX;
				let newTopPx = currentTopPx + deltaY;

				// 边界限制
				newLeftPx = Math.max(
					margin,
					Math.min(screenWidth - btnSize - margin, newLeftPx)
				);
				newTopPx = Math.max(
					margin,
					Math.min(screenHeight - btnSize - margin, newTopPx)
				);

				// 转换为right和bottom距离（rpx单位）
				const newRightPx = screenWidth - newLeftPx - btnSize;
				const newBottomPx = screenHeight - newTopPx - btnSize;

				this.floatingBtnPosition.right = newRightPx / rpxToPx;
				this.floatingBtnPosition.bottom = newBottomPx / rpxToPx;
			}
		},

		onTouchEnd(e) {
			// 如果进行了拖拽，保存新位置
			if (this.isDragging) {
				this.$emit("touchend");
			}

			// 延迟重置拖拽状态，避免影响点击事件
			setTimeout(() => {
				this.isDragging = false;
			}, 100);
		},
  },
};
</script>
<style lang="scss" scoped>

// 右下角悬停对话按钮 - 可拖拽
.floating-chat-btn {
	position: fixed;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
	z-index: 1001;
	transition: box-shadow 0.3s ease, transform 0.2s ease;
	animation: pulse 2s infinite;
	cursor: move; // 鼠标悬停时显示移动光标
	user-select: none; // 防止文字选择

	&:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.4);
		animation-play-state: paused; // 拖拽时暂停动画
	}

	.chat-icon {
		font-size: 48rpx;
		color: #ffffff;
		pointer-events: none; // 防止图标干扰触摸事件
	}
}

// 动画效果
@keyframes float {
	0%,
	100% {
		transform: translateY(0px) rotate(0deg);
	}
	50% {
		transform: translateY(-10px) rotate(5deg);
	}
}

@keyframes pulse {
	0% {
		box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
	}
	50% {
		box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.5);
	}
	100% {
		box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
	}
}
</style>
