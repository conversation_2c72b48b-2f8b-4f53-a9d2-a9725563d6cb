# XjUploadImage 图片上传组件

基于 u-upload 封装的图片上传组件，支持 v-model 双向绑定，具有最大数量限制、图片预览、删除等功能。

## 功能特性

- ✅ v-model 双向绑定
- ✅ 最大数量限制
- ✅ 图片预览和删除
- ✅ 上传进度显示
- ✅ 自定义上传接口
- ✅ 文件大小限制
- ✅ 支持单图和多图模式
- ✅ 简约大气的界面设计
- ✅ 使用 emoji 图标作为默认上传按钮

## 基本用法

### 单图上传
```vue
<template>
  <view>
    <xj-upload-image 
      v-model="singleImage" 
      :max-count="1"
      upload-text="上传头像"
    />
    <text>当前图片：{{ singleImage }}</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      singleImage: ''
    }
  }
}
</script>
```

### 多图上传
```vue
<template>
  <view>
    <xj-upload-image 
      v-model="imageList" 
      :max-count="9"
      upload-text="添加图片"
    />
    <text>图片数量：{{ imageList.length }}</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imageList: []
    }
  }
}
</script>
```

### 自定义上传接口
```vue
<template>
  <xj-upload-image 
    v-model="imageList" 
    :max-count="5"
    upload-url="https://your-api.com/upload"
    upload-field="image"
    :upload-data="{ userId: 123 }"
    :upload-headers="{ 'Authorization': 'Bearer token' }"
    @upload-success="onUploadSuccess"
    @upload-error="onUploadError"
  />
</template>

<script>
export default {
  data() {
    return {
      imageList: []
    }
  },
  methods: {
    onUploadSuccess(result) {
      console.log('上传成功:', result)
    },
    onUploadError(error) {
      console.log('上传失败:', error)
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value / v-model | String/Array | [] | 绑定值，单图模式为字符串，多图模式为数组 |
| maxCount | String/Number | 9 | 最大上传数量 |
| disabled | Boolean | false | 是否禁用 |
| deletable | Boolean | true | 是否显示删除按钮 |
| uploadUrl | String | '' | 上传接口地址，为空时使用默认地址 |
| uploadField | String | 'file' | 上传字段名 |
| uploadData | Object | {} | 上传时的额外参数 |
| uploadHeaders | Object | {} | 上传时的请求头 |
| quality | Number | 0.8 | 图片质量，0-1之间 |
| maxSize | Number | 10485760 | 文件大小限制（字节），默认10MB |
| uploadIcon | String | '📷' | 上传按钮图标，支持emoji |
| uploadIconColor | String | '#c0c4cc' | 上传按钮图标颜色 |
| uploadText | String | '' | 上传按钮文字 |
| imageSize | String/Number | 80 | 图片显示尺寸 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| upload-success | 上传成功时触发 | { file, response } |
| upload-error | 上传失败时触发 | { file, error/response } |
| delete | 删除图片时触发 | { file, index } |

### 上传接口返回格式

组件支持多种返回格式，会自动识别：

```javascript
// 格式1：直接返回URL
{
  "url": "https://example.com/image.jpg"
}

// 格式2：标准格式
{
  "code": 200,
  "data": {
    "url": "https://example.com/image.jpg"
  }
}

// 格式3：成功标识
{
  "success": true,
  "url": "https://example.com/image.jpg"
}

// 格式4：直接返回URL字符串
{
  "data": "https://example.com/image.jpg"
}
```

## 样式定制

组件提供了多种样式类名，可以通过CSS进行定制：

```scss
// 修改图片尺寸
.xj-upload-image .image-item,
.xj-upload-image .add-button {
  width: 200rpx;
  height: 200rpx;
}

// 修改边框样式
.xj-upload-image .add-button {
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
}

// 修改删除按钮样式
.xj-upload-image .delete-btn {
  background-color: rgba(255, 0, 0, 0.8);
}
```

## 注意事项

1. 组件默认使用参考代码中的上传接口地址，实际使用时请配置正确的 `uploadUrl`
2. 上传字段名默认为 `upfile`，与参考代码保持一致
3. 组件会自动处理 token 认证，从本地存储中获取 token 并添加到请求头
4. 支持图片预览功能，点击图片可以全屏预览
5. 删除操作会弹出确认对话框，避免误删
6. 文件大小超限时会自动提示用户

## 更新日志

### v1.0.0
- 初始版本发布
- 支持 v-model 双向绑定
- 支持单图和多图上传
- 支持自定义上传接口
- 支持图片预览和删除
- 支持文件大小限制
