# XjUploadImage 图片上传组件使用说明

## 已完成的集成

✅ **已成功集成到 save.vue 页面**

原来的图片上传功能已经被替换为新的 `xj-upload-image` 组件，具有以下改进：

### 替换内容

**原来的代码：**
```vue
<!-- 复杂的图片上传UI -->
<view class="image-upload-container">
  <view class="uploaded-images">...</view>
  <view class="upload-btn">...</view>
</view>

<!-- 复杂的方法 -->
chooseImage() { ... }
previewImage() { ... }
removeImage() { ... }
```

**现在的代码：**
```vue
<!-- 简洁的组件调用 -->
<xj-upload-image 
  v-model="formData.images" 
  :max-count="9"
  upload-text="添加图片"
  @upload-success="onImageUploadSuccess"
  @upload-error="onImageUploadError"
  @delete="onImageDelete"
/>

<!-- 简洁的回调方法 -->
onImageUploadSuccess(result) { ... }
onImageUploadError(error) { ... }
onImageDelete(result) { ... }
```

### 功能改进

1. **v-model 双向绑定** - 自动同步图片数据
2. **自动上传** - 选择图片后自动上传到服务器
3. **上传进度显示** - 显示上传状态和进度
4. **错误处理** - 完善的错误提示和重试机制
5. **文件大小限制** - 自动检查文件大小
6. **简约界面** - 使用 emoji 图标，界面更加简洁
7. **删除确认** - 删除图片时弹出确认对话框

### 配置说明

组件使用参考代码中的上传接口：
- **上传地址**: `http://files.qcloud.rhecs.com/ueditor/php/controller.php?action=uploadimage&encode=utf-8`
- **上传字段**: `upfile`
- **最大数量**: 9张图片
- **文件大小限制**: 10MB

### 数据格式

组件支持多种数据格式：

**输入格式（v-model）：**
```javascript
// 单图模式
singleImage: ''

// 多图模式
multipleImages: []
```

**输出格式（上传成功后）：**
```javascript
// 单图模式返回字符串
"https://example.com/image.jpg"

// 多图模式返回数组
[
  "https://example.com/image1.jpg",
  "https://example.com/image2.jpg"
]
```

### 事件回调

```javascript
// 上传成功
onImageUploadSuccess(result) {
  // result.file - 文件信息
  // result.response - 服务器响应
}

// 上传失败
onImageUploadError(error) {
  // error.file - 文件信息
  // error.error - 错误信息
}

// 删除图片
onImageDelete(result) {
  // result.file - 被删除的文件
  // result.index - 删除的索引
}
```

### 测试页面

创建了测试页面 `pages_task/detail/test-upload.vue` 用于验证组件功能。

### 注意事项

1. 组件会自动处理图片的 JSON 序列化和反序列化
2. 支持编辑模式下的图片数据加载
3. 保存时会自动将图片数组转换为 JSON 字符串
4. 删除操作有确认对话框，避免误删
5. 上传失败时会显示错误提示

### 下一步

如需进一步定制，可以修改以下配置：
- 上传接口地址（uploadUrl 属性）
- 文件大小限制（maxSize 属性）
- 图片显示尺寸（imageSize 属性）
- 上传按钮样式（uploadIcon、uploadIconColor 属性）
