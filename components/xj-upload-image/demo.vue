<!--
 * @Description: XjUploadImage 组件使用示例
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30 16:08:20
-->
<template>
  <view class="demo-container">
    <view class="demo-section">
      <view class="section-title">单图上传示例</view>
      <view class="section-content">
        <xj-upload-image 
          v-model="singleImage" 
          :max-count="1"
          upload-text="上传头像"
          @upload-success="onSingleUploadSuccess"
          @upload-error="onUploadError"
        />
        <view class="result-text">
          当前图片：{{ singleImage || '暂无' }}
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">多图上传示例（最多5张）</view>
      <view class="section-content">
        <xj-upload-image 
          v-model="multipleImages" 
          :max-count="5"
          upload-text="添加图片"
          @upload-success="onMultipleUploadSuccess"
          @upload-error="onUploadError"
          @delete="onImageDelete"
        />
        <view class="result-text">
          已上传 {{ multipleImages.length }} 张图片
        </view>
        <view class="image-urls" v-if="multipleImages.length > 0">
          <view class="url-item" v-for="(url, index) in multipleImages" :key="index">
            {{ index + 1 }}. {{ url }}
          </view>
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">禁用状态示例</view>
      <view class="section-content">
        <xj-upload-image 
          v-model="disabledImages" 
          :max-count="3"
          :disabled="true"
          upload-text="已禁用"
        />
        <view class="result-text">
          禁用状态下无法上传或删除图片
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">自定义样式示例</view>
      <view class="section-content">
        <xj-upload-image 
          v-model="customImages" 
          :max-count="4"
          upload-icon="🖼️"
          upload-text="选择图片"
          upload-icon-color="#007aff"
          @upload-success="onCustomUploadSuccess"
        />
        <view class="result-text">
          使用自定义图标和颜色
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">操作按钮</view>
      <view class="section-content">
        <view class="button-group">
          <button class="demo-button" @click="clearAllImages">清空所有图片</button>
          <button class="demo-button" @click="addTestImage">添加测试图片</button>
          <button class="demo-button" @click="showImageData">查看图片数据</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'XjUploadImageDemo',
  data() {
    return {
      // 单图上传
      singleImage: '',
      
      // 多图上传
      multipleImages: [],
      
      // 禁用状态的图片（预设一些图片）
      disabledImages: [
        'https://via.placeholder.com/300x300/ff6b6b/ffffff?text=Image1',
        'https://via.placeholder.com/300x300/4ecdc4/ffffff?text=Image2'
      ],
      
      // 自定义样式的图片
      customImages: []
    }
  },
  
  methods: {
    /**
     * 单图上传成功回调
     */
    onSingleUploadSuccess(result) {
      console.log('单图上传成功:', result)
      uni.showToast({
        title: '头像上传成功',
        icon: 'success'
      })
    },
    
    /**
     * 多图上传成功回调
     */
    onMultipleUploadSuccess(result) {
      console.log('多图上传成功:', result)
      uni.showToast({
        title: `图片上传成功，共${this.multipleImages.length}张`,
        icon: 'success'
      })
    },
    
    /**
     * 自定义样式上传成功回调
     */
    onCustomUploadSuccess(result) {
      console.log('自定义样式上传成功:', result)
      uni.showToast({
        title: '图片上传成功',
        icon: 'success'
      })
    },
    
    /**
     * 上传失败回调
     */
    onUploadError(error) {
      console.error('上传失败:', error)
      uni.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      })
    },
    
    /**
     * 图片删除回调
     */
    onImageDelete(result) {
      console.log('图片删除:', result)
      uni.showToast({
        title: '图片已删除',
        icon: 'success'
      })
    },
    
    /**
     * 清空所有图片
     */
    clearAllImages() {
      uni.showModal({
        title: '确认操作',
        content: '确定要清空所有图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.singleImage = ''
            this.multipleImages = []
            this.customImages = []
            uni.showToast({
              title: '已清空所有图片',
              icon: 'success'
            })
          }
        }
      })
    },
    
    /**
     * 添加测试图片
     */
    addTestImage() {
      const testImages = [
        'https://via.placeholder.com/300x300/ff9ff3/ffffff?text=Test1',
        'https://via.placeholder.com/300x300/54a0ff/ffffff?text=Test2',
        'https://via.placeholder.com/300x300/5f27cd/ffffff?text=Test3'
      ]
      
      // 随机选择一张测试图片
      const randomImage = testImages[Math.floor(Math.random() * testImages.length)]
      
      if (this.multipleImages.length < 5) {
        this.multipleImages.push(randomImage)
        uni.showToast({
          title: '测试图片已添加',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '图片数量已达上限',
          icon: 'none'
        })
      }
    },
    
    /**
     * 查看图片数据
     */
    showImageData() {
      const data = {
        singleImage: this.singleImage,
        multipleImages: this.multipleImages,
        disabledImages: this.disabledImages,
        customImages: this.customImages
      }
      
      console.log('当前图片数据:', data)
      
      uni.showModal({
        title: '图片数据',
        content: `单图：${this.singleImage ? '1张' : '0张'}\n多图：${this.multipleImages.length}张\n禁用：${this.disabledImages.length}张\n自定义：${this.customImages.length}张`,
        showCancel: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.demo-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-content {
  .result-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
  
  .image-urls {
    margin-top: 15rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 10rpx;
    
    .url-item {
      font-size: 24rpx;
      color: #888888;
      margin-bottom: 10rpx;
      word-break: break-all;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.demo-button {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  background-color: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  
  &:active {
    background-color: #0056cc;
  }
}
</style>
