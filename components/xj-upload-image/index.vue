<!--
 * @Description: 基于u-upload封装的图片上传组件，支持v-model双向绑定
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-30 16:08:20
 * @Features:
 *   - v-model双向绑定
 *   - 最大数量限制
 *   - 图片预览和删除
 *   - 上传进度显示
 *   - 自定义上传接口
 *   - 智能图片压缩：宽度超过1200px自动压缩到1200px
 *   - 智能格式转换：所有图片转换为JPG格式
 *   - 智能质量优化：大于1MB的图片自动降低质量压缩
-->
<template>
  <view class="xj-upload-image">
    <!-- 图片列表展示 -->
    <view class="image-list">
      <view
        v-for="(item, index) in imageList"
        :key="index"
        class="image-item"
      >
        <!-- 图片预览 -->
        <image
          :src="item.url || item.thumb"
          mode="aspectFill"
          class="preview-image"
          @click="previewImage(index)"
        />

        <!-- 删除按钮 -->
        <view
          v-if="!disabled && deletable"
          class="delete-btn"
          @click="deleteImage(index)"
        >
          <u-icon name="close" color="#ffffff" size="12"></u-icon>
        </view>

        <!-- 处理中状态遮罩 -->
        <view v-if="item.status === 'processing'" class="upload-mask processing">
          <u-loading-icon size="20" color="#ffffff"></u-loading-icon>
          <text class="upload-text">处理中...</text>
        </view>

        <!-- 上传状态遮罩 -->
        <view v-if="item.status === 'uploading'" class="upload-mask">
          <u-loading-icon size="20" color="#ffffff"></u-loading-icon>
          <text class="upload-text">上传中...</text>
        </view>

        <!-- 上传失败遮罩 -->
        <view v-if="item.status === 'failed'" class="upload-mask error">
          <u-icon name="close-circle" color="#ffffff" size="20"></u-icon>
          <text class="upload-text">处理失败</text>
        </view>
      </view>

      <!-- 添加按钮 -->
      <view
        v-if="showAddButton"
        class="add-button"
        @click="chooseImage"
      >
        <view class="upload-icon-wrapper">
          <text class="upload-icon-emoji">{{ uploadIcon }}</text>
        </view>
        <text v-if="uploadText" class="add-text">{{ uploadText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'XjUploadImage',
  props: {
    // v-model绑定的值，支持字符串或数组
    value: {
      type: [String, Array],
      default: () => []
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 9
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示删除按钮
    deletable: {
      type: Boolean,
      default: true
    },
    // 上传接口地址
    uploadUrl: {
      type: String,
      default: ''
    },
    // 上传字段名
    uploadField: {
      type: String,
      default: 'file'
    },
    // 上传时的额外参数
    uploadData: {
      type: Object,
      default: () => ({})
    },
    // 上传时的请求头
    uploadHeaders: {
      type: Object,
      default: () => ({})
    },
    // 图片质量，0-1之间
    quality: {
      type: Number,
      default: 0.8
    },
    // 图片尺寸限制
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    // 上传按钮图标
    uploadIcon: {
      type: String,
      default: '📷' // 使用emoji图标
    },
    // 上传按钮图标颜色
    uploadIconColor: {
      type: String,
      default: '#c0c4cc'
    },
    // 上传按钮文字
    uploadText: {
      type: String,
      default: ''
    },
    // 图片宽高
    imageSize: {
      type: [String, Number],
      default: 80
    }
  },

  data() {
    return {
      imageList: [], // 内部图片列表
      uploadIndex: 0 // 上传索引，用于标识正在上传的图片
    }
  },

  computed: {
    // 是否显示添加按钮
    showAddButton() {
      if (this.disabled) return false
      return this.imageList.length < Number(this.maxCount)
    },

    // 获取实际的上传地址
    realUploadUrl() {
      // 使用参考代码中的上传地址
      return "http://files.qcloud.rhecs.com/ueditor/php/controller.php?action=uploadimage&encode=utf-8"
    },

    // 获取实际的上传字段名
    realUploadField() {
      if (this.uploadField !== 'file') {
        return this.uploadField
      }
      // 使用参考代码中的字段名
      return 'upfile'
    }
  },

  watch: {
    // 监听外部传入的value变化
    value: {
      handler(newVal) {
        console.log('watch value 变化:', newVal)
        this.initImageList(newVal)
      },
      immediate: true
    },

    // 监听 imageList 变化
    imageList: {
      handler(newVal) {
        console.log('watch imageList 变化:', newVal)
      },
      deep: true
    }
  },

  methods: {
    /**
     * 初始化图片列表
     * @param {String|Array} value 外部传入的值 - 简单的URL字符串或URL数组
     */
    initImageList(value) {
      console.log('initImageList - 接收到的value:', value)

      if (!value) {
        this.imageList = []
        return
      }

      // 如果是字符串，转换为包含一个图片对象的数组
      if (typeof value === 'string') {
        this.imageList = value ? [{ url: value, thumb: value, status: 'success' }] : []
      } else if (Array.isArray(value)) {
        // 如果是数组，将每个URL转换为图片对象
        this.imageList = value.map(url => ({
          url: url,
          thumb: url,
          status: 'success'
        }))
      } else {
        this.imageList = []
      }

      console.log('initImageList - 转换后的imageList:', this.imageList)
    },

    /**
     * 选择图片
     */
    chooseImage() {
      if (this.disabled) return

      const remainCount = Number(this.maxCount) - this.imageList.length
      if (remainCount <= 0) {
        uni.showToast({
          title: `最多只能上传${this.maxCount}张图片`,
          icon: 'none'
        })
        return
      }

      uni.chooseImage({
        count: Math.min(remainCount, 9),
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.handleImageChoose(res.tempFilePaths)
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 处理图片选择结果
     * @param {Array} tempFilePaths 临时文件路径数组
     */
    handleImageChoose(tempFilePaths) {
      tempFilePaths.forEach(tempPath => {
        // 检查文件大小
        uni.getFileInfo({
          filePath: tempPath,
          success: (fileInfo) => {
            if (fileInfo.size > this.maxSize) {
              uni.showToast({
                title: `图片大小不能超过${this.formatFileSize(this.maxSize)}`,
                icon: 'none'
              })
              return
            }

            // 添加到图片列表，状态为处理中
            const imageItem = {
              url: tempPath,
              thumb: tempPath,
              status: 'processing', // 新增处理中状态
              uploadIndex: this.uploadIndex++
            }

            console.log('添加图片到列表:', imageItem)
            this.imageList.push(imageItem)
            console.log('当前图片列表长度:', this.imageList.length)

            // 先处理图片（压缩/转换格式），再上传
            this.processImage(imageItem, fileInfo.size)
          },
          fail: () => {
            uni.showToast({
              title: '获取文件信息失败',
              icon: 'none'
            })
          }
        })
      })
    },

    /**
     * 处理图片（压缩和格式转换）
     * @param {Object} imageItem 图片项
     * @param {Number} originalSize 原始文件大小
     */
    async processImage(imageItem, originalSize) {
      try {
        console.log('开始处理图片:', imageItem.url, '原始大小:', this.formatFileSize(originalSize))

        // 获取图片信息
        const imageInfo = await this.getImageInfo(imageItem.url)
        console.log('图片信息:', imageInfo)

        const needCompress = originalSize > 1024 * 1024 || imageInfo.width > 1200

        if (needCompress) {
          console.log('需要压缩图片')
          // 需要压缩：计算压缩参数
          const compressOptions = this.calculateCompressOptions(imageInfo, originalSize)
          console.log('压缩参数:', compressOptions)

          // 执行压缩
          const compressedPath = await this.compressImage(imageItem.url, compressOptions)
          console.log('压缩后路径:', compressedPath)

          // 更新图片路径
          imageItem.url = compressedPath
          imageItem.thumb = compressedPath
        } else {
          console.log('图片无需压缩，直接转换为JPG格式')
          // 不需要压缩但转换为JPG格式
          const convertedPath = await this.convertToJpg(imageItem.url)
          imageItem.url = convertedPath
          imageItem.thumb = convertedPath
        }

        // 更新状态为上传中并开始上传
        imageItem.status = 'uploading'
        this.uploadImage(imageItem)

      } catch (error) {
        console.error('处理图片失败:', error)
        imageItem.status = 'failed'
        uni.showToast({
          title: '图片处理失败',
          icon: 'none'
        })
      }
    },

    /**
     * 获取图片信息
     * @param {String} src 图片路径
     * @returns {Promise} 图片信息
     */
    getImageInfo(src) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: src,
          success: resolve,
          fail: reject
        })
      })
    },

    /**
     * 计算压缩参数
     * @param {Object} imageInfo 图片信息
     * @param {Number} originalSize 原始文件大小
     * @returns {Object} 压缩参数
     */
    calculateCompressOptions(imageInfo, originalSize) {
      const { width, height } = imageInfo
      let targetWidth = width
      let targetHeight = height
      let quality = 0.8

      // 如果宽度超过1200，按比例缩放
      if (width > 1200) {
        targetWidth = 1200
        targetHeight = Math.round((height * 1200) / width)
      }

      // 如果文件大小超过1MB，降低质量
      if (originalSize > 1024 * 1024) {
        if (originalSize > 5 * 1024 * 1024) {
          quality = 0.6 // 超过5MB，质量设为0.6
        } else if (originalSize > 2 * 1024 * 1024) {
          quality = 0.7 // 超过2MB，质量设为0.7
        } else {
          quality = 0.8 // 1-2MB，质量设为0.8
        }
      }

      return {
        src: imageInfo.path,
        width: targetWidth,
        height: targetHeight,
        quality: quality,
        compressedWidth: targetWidth,
        compressedHeight: targetHeight
      }
    },

    /**
     * 压缩图片 - 使用Canvas实现，兼容H5和小程序
     * @param {String} src 原图路径
     * @param {Object} options 压缩选项
     * @returns {Promise} 压缩后的图片路径
     */
    compressImage(src, options) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        this.compressImageH5(src, options).then(resolve).catch(reject)
        // #endif

        // #ifndef H5
        // 小程序环境使用uni.compressImage
        uni.compressImage({
          src: src,
          quality: Math.round(options.quality * 100),
          width: options.width,
          height: options.height,
          success: (res) => {
            console.log('图片压缩成功:', res.tempFilePath)
            resolve(res.tempFilePath)
          },
          fail: (error) => {
            console.error('图片压缩失败:', error)
            reject(error)
          }
        })
        // #endif
      })
    },

    /**
     * H5环境下使用Canvas压缩图片
     * @param {String} src 原图路径
     * @param {Object} options 压缩选项
     * @returns {Promise} 压缩后的图片DataURL
     */
    compressImageH5(src, options) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'

        img.onload = () => {
          try {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            canvas.width = options.width
            canvas.height = options.height

            // 绘制压缩后的图片
            ctx.drawImage(img, 0, 0, options.width, options.height)

            // 转换为JPG格式的DataURL
            const compressedDataURL = canvas.toDataURL('image/jpeg', options.quality)

            console.log('H5图片压缩成功')
            resolve(compressedDataURL)
          } catch (error) {
            console.error('Canvas压缩失败:', error)
            reject(error)
          }
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }

        img.src = src
      })
    },

    /**
     * 转换图片为JPG格式（不压缩尺寸）
     * @param {String} src 原图路径
     * @returns {Promise} 转换后的图片路径
     */
    convertToJpg(src) {
      return new Promise((resolve, reject) => {
        // 获取图片信息
        uni.getImageInfo({
          src: src,
          success: (imageInfo) => {
            // #ifdef H5
            this.compressImageH5(src, {
              width: imageInfo.width,
              height: imageInfo.height,
              quality: 0.9
            }).then(resolve).catch(reject)
            // #endif

            // #ifndef H5
            // 小程序环境
            uni.compressImage({
              src: src,
              quality: 90,
              width: imageInfo.width,
              height: imageInfo.height,
              success: (res) => {
                console.log('图片格式转换成功:', res.tempFilePath)
                resolve(res.tempFilePath)
              },
              fail: reject
            })
            // #endif
          },
          fail: reject
        })
      })
    },

    /**
     * 上传图片
     * @param {Object} imageItem 图片项
     */
    uploadImage(imageItem) {
      if (!this.realUploadUrl) {
        console.error('上传地址未配置')
        imageItem.status = 'failed'
        return
      }

      // 构建请求头
      const headers = { ...this.uploadHeaders }

      // 如果有token，添加到请求头
      const token = uni.getStorageSync('token') || uni.getStorageSync('userToken')
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }

      console.log('开始上传图片:', {
        url: this.realUploadUrl,
        filePath: imageItem.url,
        name: this.realUploadField,
        formData: this.uploadData,
        headers: headers
      })

      // #ifdef H5
      // H5环境下，如果是DataURL需要特殊处理
      if (imageItem.url.startsWith('data:')) {
        this.uploadImageH5(imageItem, headers)
        return
      }
      // #endif

      // 使用uni.uploadFile上传
      uni.uploadFile({
        url: this.realUploadUrl,
        filePath: imageItem.url,
        name: this.realUploadField,
        formData: this.uploadData,
        header: headers,
        success: (res) => {
          this.handleUploadSuccess(imageItem, res)
        },
        fail: (error) => {
          this.handleUploadError(imageItem, error)
        }
      })
    },

    /**
     * H5环境下上传DataURL格式的图片
     * @param {Object} imageItem 图片项
     * @param {Object} headers 请求头
     */
    uploadImageH5(imageItem, headers) {
      try {
        // 将DataURL转换为Blob
        const blob = this.dataURLToBlob(imageItem.url)

        // 创建FormData
        const formData = new FormData()
        formData.append(this.realUploadField, blob, 'compressed.jpg')

        // 添加额外的表单数据
        Object.keys(this.uploadData).forEach(key => {
          formData.append(key, this.uploadData[key])
        })

        // 使用fetch上传
        fetch(this.realUploadUrl, {
          method: 'POST',
          headers: headers,
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          this.handleUploadSuccess(imageItem, { data: JSON.stringify(data) })
        })
        .catch(error => {
          this.handleUploadError(imageItem, error)
        })
      } catch (error) {
        this.handleUploadError(imageItem, error)
      }
    },

    /**
     * 将DataURL转换为Blob
     * @param {String} dataURL DataURL字符串
     * @returns {Blob} Blob对象
     */
    dataURLToBlob(dataURL) {
      const arr = dataURL.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },

    /**
     * 处理上传成功
     * @param {Object} imageItem 图片项
     * @param {Object} res 响应结果
     */
    handleUploadSuccess(imageItem, res) {
      try {
        const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
        console.log('上传响应数据:', data)

        // 根据接口返回格式处理：state: "SUCCESS" 表示成功
        if (data.state === 'SUCCESS' && data.url) {
          // 成功 - 更新图片状态和URL
          imageItem.status = 'success'
          imageItem.url = data.url

          console.log('图片上传成功:', data.url)
          console.log('更新后的图片项:', imageItem)
          console.log('当前图片列表:', this.imageList)

          // 立即更新 v-model 的值
          this.updateValue()

          // 强制触发界面更新
          this.$forceUpdate()

          this.$emit('upload-success', {
            file: imageItem,
            response: data
          })
        } else {
          // 失败
          imageItem.status = 'failed'
          console.error('上传失败:', data)

          this.$emit('upload-error', {
            file: imageItem,
            response: data
          })
        }
      } catch (error) {
        imageItem.status = 'failed'
        console.error('解析上传结果失败:', error)

        this.$emit('upload-error', {
          file: imageItem,
          error: error
        })
      }
    },

    /**
     * 处理上传失败
     * @param {Object} imageItem 图片项
     * @param {Object} error 错误信息
     */
    handleUploadError(imageItem, error) {
      imageItem.status = 'failed'
      console.error('上传请求失败:', error)

      this.$emit('upload-error', {
        file: imageItem,
        error: error
      })
    },

    /**
     * 删除图片
     * @param {Number} index 图片索引
     */
    deleteImage(index) {
      uni.showModal({
        title: '删除确认',
        content: '确定删除这张图片吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            const deletedItem = this.imageList[index]
            this.imageList.splice(index, 1)
            this.updateValue()

            this.$emit('delete', {
              file: deletedItem,
              index: index
            })
          }
        }
      })
    },

    /**
     * 预览图片
     * @param {Number} index 图片索引
     */
    previewImage(index) {
      const urls = this.imageList
        .filter(item => item.status === 'success' && item.url)
        .map(item => item.url)

      if (urls.length === 0) return

      uni.previewImage({
        urls: urls,
        current: index < urls.length ? index : 0
      })
    },

    /**
     * 更新v-model的值
     */
    updateValue() {
      // 直接从 imageList 中提取所有成功上传的图片URL
      const successImages = this.imageList
        .filter(item => item.status === 'success' && item.url)
        .map(item => item.url)

      console.log('updateValue - 所有图片项:', this.imageList)
      console.log('updateValue - 成功的图片URL:', successImages)
      console.log('updateValue - maxCount:', this.maxCount)

      // 根据maxCount判断返回格式
      if (Number(this.maxCount) === 1) {
        // 单图模式返回字符串
        const value = successImages[0] || ''
        console.log('updateValue - 单图模式，发送值:', value)
        this.$emit('input', value)
      } else {
        // 多图模式返回数组 - 直接返回URL数组
        console.log('updateValue - 多图模式，发送值:', successImages)
        this.$emit('input', successImages)
      }
    },

    /**
     * 格式化文件大小
     * @param {Number} size 文件大小（字节）
     * @returns {String} 格式化后的大小
     */
    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + 'KB'
      } else {
        return (size / (1024 * 1024)).toFixed(1) + 'MB'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.xj-upload-image {
  width: 100%;

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10rpx;
  }

  .image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background-color: #f5f5f5;

    .preview-image {
      width: 100%;
      height: 100%;
      border-radius: 10rpx;
    }

    .delete-btn {
      position: absolute;
      top: 6rpx;
      right: 6rpx;
      width: 36rpx;
      height: 36rpx;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .upload-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 10rpx;

      .upload-text {
        color: #ffffff;
        font-size: 24rpx;
        margin-top: 10rpx;
      }

      &.processing {
        background-color: rgba(0, 123, 255, 0.6); // 蓝色表示处理中
      }

      &.error {
        background-color: rgba(255, 0, 0, 0.6);
      }
    }
  }

  .add-button {
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed #d9d9d9;
    border-radius: 10rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
    transition: all 0.3s ease;

    &:active {
      background-color: #f0f0f0;
      border-color: #bfbfbf;
    }

    .upload-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;

      .upload-icon-emoji {
        font-size: 48rpx;
        line-height: 1;
      }
    }

    .add-text {
      color: #999999;
      font-size: 24rpx;
      margin-top: 10rpx;
    }
  }
}

/* 适配不同尺寸 */
.xj-upload-image.size-small {
  .image-item,
  .add-button {
    width: 120rpx;
    height: 120rpx;
  }
}

.xj-upload-image.size-large {
  .image-item,
  .add-button {
    width: 200rpx;
    height: 200rpx;
  }
}

/* 单行布局 */
.xj-upload-image.layout-horizontal {
  .image-list {
    flex-wrap: nowrap;
    overflow-x: auto;

    .image-item,
    .add-button {
      flex-shrink: 0;
      margin-right: 20rpx;
    }
  }
}
</style>
