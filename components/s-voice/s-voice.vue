<template>
  <view class="flex flex-col align-center m-50-auto">
    <!-- 播放录音按钮 -->
    <view v-if="voice && !isPlaying">
      <image class="play-btn" :src="ossUrl + 'icon_play.png'" @click="togglePlay" />
    </view>
    <view v-if="voice && isPlaying">
      <image class="play-btn" :src="ossUrl + 'icon_pause.png'" @click="togglePlay" />
    </view>

    <!-- 录音时长显示 -->
    <view class="mt-20">{{ duration.toFixed(1) }} 秒</view>

    <!-- 录音按钮 -->
    <view class="popup-voice mt-20" :class="isActive ? 'active-voice' : 'noactive-voice'" @click="toggleRecord">
      <image :src="ossUrl + 'icon_luyin.png'" />
    </view>

    <!-- 提示文案 -->
    <view class="mt-20">
      <span v-if="!isActive && !duration">点击开始录制</span>
      <span v-else-if="!isActive && duration">点击继续录制</span>
      <span v-else>点击停止录制</span>
    </view>

    <!-- 删除 / 确认按钮 -->
    <view v-if="showActions" class="mt-20 flex justify-between w-300">
      <button @click="deleteRecording">删除</button>
      <button @click="confirmRecording">确认</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isActive: false,
      isPlaying: false,
      showActions: false,
      recorderManager: null,
      innerAudioContext: null,
      isConfirmed: false, // ✅ 是否已点击确认
      voice: null,
      duration: 0,
      interval: null,
      ossUrl: uni.globalVar,
      segments: [], // 用来保存多个录音片段
    };
  },
  mounted() {
    this.recorderManager = uni.getRecorderManager();
    this.innerAudioContext = uni.createInnerAudioContext();

    // 录音结束
    this.recorderManager.onStop((res) => {
      const segment = {
        src: res.tempFilePath,
        duration: (res.duration / 1000).toFixed(1),
      };
      this.segments.push(segment); // 保存片段
      this.voice = segment;
      this.showActions = true;
    });

    // 播放完毕重置状态
    this.innerAudioContext.onEnded(() => {
      this.isPlaying = false;
    });
  },
  methods: {
    toggleRecord() {
      if (this.isActive) {
        // 停止录音
        this.recorderManager.stop();
        this.isActive = false;
        clearInterval(this.interval);
        this.interval = null;
        return;
      }

      // 未确认：继续录音
      if (!this.isConfirmed) {
        this.continueRecording();
      } else {
        // 已确认：提示是否重新录制
        uni.showModal({
          title: '提示',
          content: '你已确认录音，是否重新开始录制？',
          success: (res) => {
            if (res.confirm) {
              this.startNewRecording();
            }
          },
        });
      }
    },

    continueRecording() {
      uni.vibrateShort();
      this.recorderManager.start();
      this.isActive = true;
      this.duration = this.voice ? parseFloat(this.voice.duration) : 0;
      this.interval = setInterval(() => {
        this.duration += 0.1;
      }, 100);
      this.showActions = false;
    },

    startNewRecording() {
      uni.vibrateShort();
      this.recorderManager.start();
      this.isActive = true;
      this.duration = 0;
      this.interval = setInterval(() => {
        this.duration += 0.1;
      }, 100);
      this.showActions = false;
      this.voice = null;
      this.isPlaying = false;
      this.isConfirmed = false; // ✅ 重录就取消确认状态
    },

    deleteRecording() {
      this.voice = null;
      this.duration = 0;
      this.showActions = false;
      this.innerAudioContext.stop();
      this.innerAudioContext.src = '';
      this.isPlaying = false;
      this.segments = []; // 清空录音片段
    },

    confirmRecording() {
      this.$emit('success', this.segments); // 传递所有片段
      this.showActions = false;
      this.innerAudioContext.pause();
      this.isPlaying = false;
      this.isConfirmed = true; // ✅ 标记已确认
    },

    togglePlay() {
      if (!this.voice) return;

      if (this.isPlaying) {
        this.innerAudioContext.pause();
        this.isPlaying = false;
      } else {
        if (this.innerAudioContext.src !== this.voice.src) {
          this.innerAudioContext.src = this.voice.src;
        }
        this.innerAudioContext.play();
        this.isPlaying = true;
      }
    },

    // 播放所有录音片段（可以是合并后的播放）
    playAll() {
      if (this.segments.length === 0) return;
      
      const allPaths = this.segments.map(segment => segment.src);
      let allAudio = uni.createInnerAudioContext();
      allAudio.src = allPaths.join(','); // 这里需要合并音频，可能需要后端服务支持
      allAudio.play();
    },
  },
};
</script>

<style scoped>
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.align-center {
  align-items: center;
}

.m-50-auto {
  margin: 50upx auto;
}

.mt-20 {
  margin-top: 20upx;
}

.w-300 {
  width: 300upx;
}

.justify-between {
  justify-content: space-between;
}

.popup-voice {
  height: 116rpx;
  width: 116rpx;
  border-radius: 100upx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-voice image {
  width: 100%;
  height: 100%;
}

.play-btn {
  width: 60rpx;
  height: 60rpx;
}

/* 录音中闪动动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 6px rgba(46, 213, 115, 0.5);
  }

  50% {
    box-shadow: 0 0 20px rgba(46, 213, 115, 1);
  }

  100% {
    box-shadow: 0 0 6px rgba(46, 213, 115, 0.5);
  }
}

.active-voice {
  animation: pulse 1s infinite;
  background-color: rgba(46, 213, 115, 0.2);
}

.noactive-voice {
  box-shadow: 0 0 4px rgba(30, 144, 255, 1);
  background-color: rgba(30, 144, 255, 0.2);
}
</style>
