---
type: "agent_requested"
---

前端环境一定是vue2 + uniapp + uview2
已定义核心scss如下
```
.flex {
    display: flex;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.wrap{
    flex-wrap: wrap;
}

.e1,.flex-left,.expanded{
    flex-grow: 1;
}

.fs{
    flex-shrink: 0
}

@for $i from 1 through 5 {
    .expanded-#{$i} {
        flex-grow: #{$i};
    }
}

.flex-row-center{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}
.fc {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.flc {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}
.flex-row-left {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
}

.flex-row-right {
    display: flex;
    flex-direction: row;
    justify-content: right;
    align-items: center;
}


.flex-space {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
}

.flex-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.flex-column-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

// 字体尺寸和高度
@for $i from 5 through 65 {
    .f-#{$i} {
        font-size: #{$i+"rpx"};
    }

    .l-#{$i} {
        line-height: #{$i+"rpx"};
    }
}


@for $i from 1 through 100 {
    .w#{$i} {
        width: #{$i + "%"};
    }

    .h#{$i} {
        height: #{$i + "%"};
    }
}


```
这些scss样式已自动导入项目，禁止额外定义。布局时，优先考虑flex布局，且优先通过"flc fc e1 fs"来设计。
项目的api请求 请在Vue文件内使用
this.$ajax.post或者this.$ajax.get方式。
如
```
if(await this.$confirm1(`您确定删除该数据吗`) == 1){
const _res = await this.$ajax.get(`请求地址`,{请求参数})
if(_res?.code == 200){
  // todo...
}
}
```
禁止定义额外的api.js文件，直接在Vue文件methods内使用this.$ajax的方法请求数据。
优先通过这样的方式来实现前端代码。
此外 使用uview2.0框架时 对cell、form组件，做好样式设计，防止字体过小，form组件务必做好必填校验等！
编写js代码时，要考虑，代码须兼容小程序环境，优先使用uniapp提供的方法。
[!注意]：vue组件的style样式 在编写style的时候 必须强制带入scoped属性 防止污染其他设计样式!!! 注意样式边界
