<!--
 * @Description: 分组管理页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
-->

<template>
  <view class="page-container">

    <view class="content-container">
      <!-- 分组列表 -->
      <view class="group-list">
        <view
          v-for="(group, index) in groups"
          :key="group.id"
          class="group-item"
          :class="`group-item-${(index % 4) + 1}`"
        >
          <view class="group-left">
            <view class="group-icon-wrapper">
              <text class="group-icon">{{ getGroupIcon(group.id) }}</text>
            </view>
          </view>
          <view class="group-main">
            <view class="group-info">
              <text class="group-name">{{ group.groupName }}</text>
              <!-- <text class="group-count">{{ group.memoryCount }}条记忆</text> -->
            </view>
            <view class="group-weight">
              <text class="weight-label">权重: {{ group.weight }}</text>
            </view>
          </view>
          <view class="group-actions">
            <view class="action-btn edit-btn" @click="editGroup(group)">
              <text class="action-icon">✏️</text>
              <text class="action-text">编辑</text>
            </view>
            <view class="action-btn delete-btn" @click="deleteGroup(group)">
              <text class="action-icon">🗑️</text>
              <text class="action-text">删除</text>
            </view>
          </view>
        </view>
      </view>

      <view v-if="groups.length === 0" class="empty-state">
        <view class="empty-icon">📁</view>
        <text class="empty-text">暂无分组</text>
        <text class="empty-desc">点击下方按钮创建第一个分组</text>
      </view>
    </view>

    <!-- 添加按钮 -->
    <view class="add-btn" @click="addGroup">
      <text class="add-icon">+</text>
      <text class="add-text">新增分组</text>
    </view>

    <!-- 编辑弹窗 -->
    <u-popup :show="showEditModal" @close="showEditModal = false" mode="bottom" border-radius="16">
      <view class="edit-modal" v-if="showEditModal">
        <view class="modal-header">
          <text class="modal-title">{{ isEdit ? '编辑分组' : '新增分组' }}</text>
        </view>

        <view class="modal-body">
          <view class="input-group">
            <text class="input-label">分组名称</text>
            <input
              class="input-field"
              v-model="formData.groupName"
              placeholder="请输入分组名称"
              maxlength="20"
            />
          </view>

          <view class="input-group">
            <text class="input-label">分组颜色</text>
            <view class="color-picker">
              <view
                v-for="colorType in colorTypes"
                :key="colorType.type"
                class="color-item"
                :class="{ active: formData.color === colorType.type }"
                :style="{ background: colorType.gradient }"
                @click="formData.color = colorType.type"
              >
                <text class="color-name">{{ colorType.name }}</text>
              </view>
            </view>
          </view>

          <view class="input-group">
            <text class="input-label">分组图标</text>
            <view class="icon-picker">
              <view
                v-for="icon in iconOptions"
                :key="icon"
                class="icon-item"
                :class="{ active: formData.icon === icon }"
                @click="formData.icon = icon"
              >
                <text class="icon-emoji">{{ icon }}</text>
              </view>
            </view>
          </view>

          <view class="input-group">
            <text class="input-label">排序权重</text>
            <input
              class="input-field"
              v-model.number="formData.weight"
              type="number"
              placeholder="数值越大排序越靠前"
            />
            <text class="input-help">权重决定分组在首页的显示顺序</text>
          </view>
        </view>

        <view class="modal-footer">
          <view class="btn btn-secondary" @click="cancelEdit">取消</view>
          <view class="btn btn-primary" @click="saveGroup">
            {{ isEdit ? '更新' : '创建' }}
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { colorUtils, COLOR_TYPE_OPTIONS, GROUP_SELECTOR_GRADIENTS } from '../data/colors.js';
import { MEMORY_ICONS, iconUtils } from '../data/icons.js';

export default {
  name: 'GroupManage',
  data() {
    return {
      groups: [],
      showEditModal: false,
      formData: {
        groupName: '',
        weight: 100,
        color: 'primary',
        icon: '📁'
      },
      isEdit: false,
      editingGroupId: null,
      loading: false,
      groupMemoryCount: {}
    };
  },

  computed: {
    // 颜色类型选项
    colorTypes() {
      return COLOR_TYPE_OPTIONS.map(option => ({
        ...option,
        gradient: GROUP_SELECTOR_GRADIENTS[option.type]
      }));
    },

    // 图标选项
    iconOptions() {
      return MEMORY_ICONS;
    }
  },

  onLoad() {
    // 只进行初始化赋值
  },

  onShow() {
    this.loadGroups();
  },

  methods: {
    // ========== API调用方法 ==========

    // 获取分类列表
    async getCategoryList() {
      try {
        const res = await this.$ajax.get('/memory/category/list?withCount=1');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取分类列表失败:', error);
        return [];
      }
    },

    // 保存分类
    async saveCategoryData(data) {
      try {
        const params = {
          name: data.groupName || data.name,
          weight: data.weight || 0,
          color: data.color || 'primary',
          icon: data.icon || '📁'
        };

        if (data.id) {
          params.id = data.id;
        }

        const res = await this.$ajax.post('/memory/category/save', params);
        if(res?.code == 200){
          return true;
        } else {
          this.$toast(res?.msg || '保存失败');
          return false;
        }
      } catch (error) {
        console.error('保存分类失败:', error);
        return false;
      }
    },

    // 删除分类
    async deleteCategoryData(id) {
      try {
        const res = await this.$ajax.post('/memory/category/delete', { id });
        if(res?.code == 200){
          return true;
        } else {
          this.$toast(res?.msg || '删除失败');
          return false;
        }
      } catch (error) {
        console.error('删除分类失败:', error);
        return false;
      }
    },

    // ========== 数据格式化方法 ==========

    // 格式化分类数据
    formatCategories(categories) {
      return categories.map((category, index) => ({
        id: category.id,
        groupName: category.categoryName || category.name,
        dataCount: category.dataCount || 0,
        weight: category.weight || 0,
        color: category.color || 'primary',
        colorValue: this.getColorValue(category.color),
        icon: iconUtils.getValidIcon(category.icon) || this.getRandomIcon(index),
        timeCreate: category.timeCreate || Date.now() / 1000
      }));
    },

    // ========== 工具方法 ==========

    // 获取颜色值
    getColorValue(colorType) {
      return colorUtils.getBaseColor(colorType);
    },

    // 获取随机图标
    getRandomIcon(index = 0) {
      return iconUtils.getRandomIcon(index);
    },

    // ========== 页面逻辑方法 ==========

    // 加载分组列表
    async loadGroups() {
      this.loading = true;
      try {
        const categoriesData = await this.getCategoryList();
        this.groups = this.formatCategories(categoriesData).sort((a, b) => b.weight - a.weight);
      } catch (error) {
        console.error('加载分组列表失败:', error);
        uni.showToast({
          title: '加载分组失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    // 获取分组图标
    getGroupIcon(groupId) {
      const group = this.groups.find(g => g.id === groupId);
      if (group) {
        return iconUtils.getValidIcon(group.icon);
      }
      return this.getRandomIcon(groupId);
    },

    // 新增分组
    addGroup() {
      this.formData = {
        groupName: '',
        weight: 100,
        color: 'primary',
        icon: '📁'
      };
      this.isEdit = false;
      this.editingGroupId = null;
      this.showEditModal = true;
    },

    // 编辑分组
    editGroup(group) {
      this.formData = {
        groupName: group.groupName,
        weight: group.weight,
        color: group.color,
        icon: group.icon
      };
      this.isEdit = true;
      this.editingGroupId = group.id;
      this.showEditModal = true;
    },

    // 删除分组
    async deleteGroup(group) {
      console.log("🚀 -> deleteGroup -> group:", group)
      if (group.dataCount > 0) {
        this.$toast('该分组下已有数据，禁止删除，请先删除数据或者移动数据到另外分组后再来删除');
        return;
      }
      const _ap = await this.$confirm1(`您确定删除分组"${group.groupName}"吗？`);
      console.log("🚀 -> deleteGroup -> _ap:", _ap)
      if(_ap){
        const result = await this.deleteCategoryData(group.id);
        if (result) {
          this.$toast('删除成功');
          await this.loadGroups();
        }
      }
    },

    // 保存分组
    async saveGroup() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      try {
        const groupData = {
          groupName: this.formData.groupName.trim(),
          weight: this.formData.weight,
          color: this.formData.color,
          icon: this.formData.icon
        };

        if (this.isEdit && this.editingGroupId) {
          groupData.id = this.editingGroupId;
        }

        const result = await this.saveCategoryData(groupData);
        if (result) {
          const action = this.isEdit ? '更新' : '创建';
          uni.showToast({
            title: `${action}成功`,
            icon: 'success'
          });

          this.showEditModal = false;
          await this.loadGroups();
        } else {
          const action = this.isEdit ? '更新' : '创建';
        }
      } catch (error) {
        console.error('保存分组失败:', error);
        const action = this.isEdit ? '更新' : '创建';
        uni.showToast({
          title: `${action}失败`,
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 取消编辑
    cancelEdit() {
      this.showEditModal = false;
      this.formData = {
        groupName: '',
        weight: 100,
        color: 'primary',
        icon: '📁'
      };
    },

    // 表单验证
    validateForm() {
      if (!this.formData.groupName.trim()) {
        uni.showToast({
          title: '请输入分组名称',
          icon: 'none'
        });
        return false;
      }

      if (this.formData.groupName.length > 20) {
        uni.showToast({
          title: '分组名称不能超过20个字符',
          icon: 'none'
        });
        return false;
      }

      if (this.formData.weight < 0) {
        uni.showToast({
          title: '权重不能为负数',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.nav-bar {
  background-color: $card-background;
  box-shadow: $shadow-nav;

  .nav-content {
    display: flex;
    align-items: center;
    padding: $spacing-lg $spacing-md;

    .nav-left {
      display: flex;
      align-items: center;

      .nav-back {
        font-size: $font-size-xxl;
        color: $primary-color;
        margin-right: $spacing-md;
        font-weight: 300;
      }

      .nav-title {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-primary;
      }
    }
  }
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 0 $spacing-md;

  .group-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 24rpx;
    box-shadow: 0 3rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20rpx);
    gap: 20rpx;

    // 不同分组的渐变背景
    &.group-item-1 {
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.12) 0%,
        rgba(118, 75, 162, 0.08) 100%);
    }

    &.group-item-2 {
      background: linear-gradient(135deg,
        rgba(255, 107, 107, 0.12) 0%,
        rgba(255, 142, 83, 0.08) 100%);
    }

    &.group-item-3 {
      background: linear-gradient(135deg,
        rgba(72, 187, 120, 0.12) 0%,
        rgba(56, 178, 172, 0.08) 100%);
    }

    &.group-item-4 {
      background: linear-gradient(135deg,
        rgba(129, 140, 248, 0.12) 0%,
        rgba(139, 92, 246, 0.08) 100%);
    }

    &:active {
      transform: translateY(1rpx) scale(0.995);
      box-shadow: 0 1rpx 12rpx rgba(0, 0, 0, 0.12);
    }

    .group-left {
      flex-shrink: 0;

      .group-icon-wrapper {
        width: 80rpx;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.1);
        border: 1rpx solid rgba(255, 255, 255, 0.8);

        .group-icon {
          font-size: 40rpx;
          line-height: 1;
        }
      }
    }

    .group-main {
      flex: 1;
      min-width: 0;

      .group-info {
        margin-bottom: 8rpx;

        .group-name {
          font-size: 30rpx;
          font-weight: 700;
          color: #1a202c;
          margin-bottom: 4rpx;
          display: block;
          line-height: 1.3;
        }

        .group-count {
          font-size: 24rpx;
          color: #64748b;
          font-weight: 500;
        }
      }

      .group-weight {
        .weight-label {
          font-size: 20rpx;
          color: #94a3b8;
          background: rgba(255, 255, 255, 0.6);
          padding: 4rpx 10rpx;
          border-radius: 12rpx;
          border: 1rpx solid rgba(0, 0, 0, 0.06);
        }
      }
    }

    .group-actions {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      flex-shrink: 0;

      .action-btn {
        display: flex;
        align-items: center;
        gap: 6rpx;
        padding: 10rpx 16rpx;
        border-radius: 16rpx;
        transition: all 0.3s ease;
        min-width: 80rpx;
        justify-content: center;

        &.edit-btn {
          background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
          border: 1rpx solid #667eea30;

          .action-icon {
            font-size: 20rpx;
            line-height: 1;
          }

          .action-text {
            color: #667eea;
            font-size: 22rpx;
            font-weight: 600;
            line-height: 1;
          }

          &:active {
            background: linear-gradient(135deg, #667eea25 0%, #764ba225 100%);
            transform: scale(0.95);
          }
        }

        &.delete-btn {
          background: linear-gradient(135deg, #ff6b6b15 0%, #ff8e5315 100%);
          border: 1rpx solid #ff6b6b30;

          .action-icon {
            font-size: 20rpx;
            line-height: 1;
          }

          .action-text {
            color: #ff6b6b;
            font-size: 22rpx;
            font-weight: 600;
            line-height: 1;
          }

          &:active {
            background: linear-gradient(135deg, #ff6b6b25 0%, #ff8e5325 100%);
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: $spacing-xxl;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-md;
    display: block;
  }

  .empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
    display: block;
  }

  .empty-desc {
    font-size: $font-size-sm;
    color: $text-light;
  }
}

.add-btn {
  position: fixed;
  bottom: 32rpx;
  right: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 28rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8rpx;

  &:active {
    transform: translateY(2rpx) scale(0.95);
    box-shadow: 0 3rpx 12rpx rgba(102, 126, 234, 0.4);
  }

  .add-icon {
    color: white;
    font-size: 32rpx;
    font-weight: 300;
    line-height: 1;
  }

  .add-text {
    color: white;
    font-size: 26rpx;
    font-weight: 600;
    line-height: 1;
  }
}

.edit-modal {
  width: 100%;
  background-color: $card-background;
  border-radius: $border-radius-lg;
  overflow: hidden;

  .modal-header {
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;
    text-align: center;

    .modal-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }
  }

  .modal-body {
    padding: $spacing-lg;

    .input-group {
      margin-bottom: 20rpx;

      .input-label {
        font-size: 28rpx;
        color: #1a202c;
        font-weight: 600;
        margin-bottom: 12rpx;
        display: block;
      }

      .input-field {
        width: 100%;
        height: 88rpx;
        background: #f7fafc;
        border-radius: 16rpx;
        border: 1rpx solid #e2e8f0;
        padding: 20rpx 24rpx;
        font-size: 28rpx;
        color: #1a202c;
        transition: all 0.3s ease;

        &:focus {
          border-color: #667eea;
          background: #ffffff;
          box-shadow: 0 0 0 3rpx rgba(102, 126, 234, 0.1);
        }

        &::placeholder {
          color: #94a3b8;
        }
      }
    }

    .input-help {
      font-size: $font-size-xs;
      color: $text-light;
      margin-top: $spacing-xs;
      display: block;
    }

    .color-picker {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      margin-top: 8rpx;

      .color-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 60rpx;
        border-radius: 12rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;
        position: relative;

        &.active {
          border-color: #1a202c;
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        .color-name {
          color: #1a202c;
          font-size: 20rpx;
          font-weight: 600;
        }
      }
    }

    .icon-picker {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      margin-top: 8rpx;

      .icon-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        border-radius: 12rpx;
        border: 2rpx solid #e2e8f0;
        background: #f7fafc;
        transition: all 0.3s ease;

        &.active {
          border-color: #667eea;
          background: #667eea15;
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }

        .icon-emoji {
          font-size: 32rpx;
          line-height: 1;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;
    background-color: #fafbfc;
  }
}
</style>
