/**
 * @Description: 记忆系统通用样式 - 简约大气设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
 */

// 主题色彩变量 - 简约大气配色
$primary-color: #2979ff;
$primary-light: #e3f2fd;
$primary-dark: #1565c0;
$background-color: #f8f9fa;  // 淡灰背景，避免过于单调
$card-background: #ffffff;
$text-primary: #1a1a1a;      // 更深的主文字色
$text-secondary: #666666;
$text-light: #999999;
$text-placeholder: #cccccc;
$border-color: #f0f0f0;      // 更淡的边框色
$divider-color: #f5f5f5;     // 分割线颜色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$shadow-color: rgba(0, 0, 0, 0.04);  // 淡阴影
$white: #ffffff;

// 间距变量 - 精确控制
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;
$spacing-xxl: 48rpx;

// 字体大小 - 层次分明
$font-size-xs: 22rpx;
$font-size-sm: 26rpx;
$font-size-md: 30rpx;
$font-size-lg: 34rpx;
$font-size-xl: 38rpx;
$font-size-xxl: 42rpx;

// 圆角 - 现代简约
$border-radius-sm: 6rpx;
$border-radius-md: 8rpx;
$border-radius-lg: 12rpx;
$border-radius-xl: 16rpx;

// 阴影 - 轻量级设计
$shadow-sm: 0 2rpx 8rpx $shadow-color;
$shadow-md: 0 4rpx 12rpx $shadow-color;
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
$shadow-nav: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);  // 导航栏阴影

// 通用容器样式 - 纯白简约
.page-container {
  background-color: $background-color;
  min-height: 100vh;
}

.content-container {
  padding: $spacing-lg $spacing-md;
  background-color: $background-color;
}

// 导航栏样式 - 带投影分割
.nav-bar {
  background-color: $card-background;
  box-shadow: $shadow-nav;
  position: relative;
  z-index: 100;
}

// 卡片样式 - 现代简约
.card {
  background-color: $card-background;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-lg;
  overflow: hidden;
  border: 1rpx solid $border-color;
}

.card-header {
  padding: $spacing-lg;
  border-bottom: 1rpx solid $divider-color;

  .card-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
    margin: 0;
    line-height: 1.4;
  }
}

.card-body {
  padding: $spacing-lg;
}

.card-footer {
  padding: $spacing-lg;
  border-top: 1rpx solid $divider-color;
  background-color: #fafbfc;
}

// 按钮样式 - 现代简约设计
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1;
  white-space: nowrap;

  &.btn-primary {
    background-color: $primary-color;
    color: white;
    box-shadow: 0 2rpx 4rpx rgba($primary-color, 0.2);

    &:active {
      background-color: $primary-dark;
      transform: translateY(1rpx);
      box-shadow: 0 1rpx 2rpx rgba($primary-color, 0.3);
    }
  }

  &.btn-secondary {
    background-color: white;
    color: $text-primary;
    border: 1rpx solid $border-color;

    &:active {
      background-color: #f8f9fa;
      transform: translateY(1rpx);
    }
  }

  &.btn-danger {
    background-color: $error-color;
    color: white;
    box-shadow: 0 2rpx 4rpx rgba($error-color, 0.2);

    &:active {
      background-color: #d32f2f;
      transform: translateY(1rpx);
    }
  }

  &.btn-ghost {
    background-color: transparent;
    color: $primary-color;
    border: 1rpx solid $primary-color;

    &:active {
      background-color: $primary-light;
    }
  }

  &.btn-sm {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }

  &.btn-lg {
    padding: $spacing-lg $spacing-xl;
    font-size: $font-size-lg;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

// 标签样式
.tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx $spacing-sm;
  border-radius: $border-radius-xl;
  font-size: $font-size-xs;
  font-weight: 500;
  margin-right: $spacing-xs;
  margin-bottom: $spacing-xs;

  &.tag-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &.tag-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }

  &.tag-warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }

  &.tag-error {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }

  .tag-close {
    margin-left: $spacing-xs;
    font-size: 20rpx;
    cursor: pointer;
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }
  }
}

// 输入框样式
.input-group {
  margin-bottom: $spacing-md;

  .input-label {
    display: block;
    font-size: $font-size-md;
    color: $text-primary;
    margin-bottom: $spacing-xs;
    font-weight: 500;
  }

  .input-field {
    width: 100%;
    padding: $spacing-sm;
    border: 1rpx solid $border-color;
    border-radius: $border-radius-sm;
    font-size: $font-size-md;
    color: $text-primary;
    background-color: white;

    &:focus {
      border-color: $primary-color;
      outline: none;
      box-shadow: 0 0 0 2rpx rgba($primary-color, 0.2);
    }

    &::placeholder {
      color: $text-light;
    }
  }

  .textarea-field {
    min-height: 200rpx;
    resize: vertical;
  }
}

// 列表样式
.list-item {
  background-color: white;
  border-bottom: 1rpx solid $border-color;
  padding: $spacing-md;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;
  }

  .list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-xs;

    .list-item-title {
      font-size: $font-size-md;
      font-weight: 500;
      color: $text-primary;
      flex: 1;
    }

    .list-item-time {
      font-size: $font-size-xs;
      color: $text-light;
      margin-left: $spacing-sm;
    }
  }

  .list-item-content {
    font-size: $font-size-sm;
    color: $text-secondary;
    line-height: 1.5;
    margin-bottom: $spacing-xs;
  }

  .list-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .list-item-tags {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
    }

    .list-item-actions {
      display: flex;
      gap: $spacing-sm;
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    color: $text-light;
    margin-bottom: $spacing-md;
  }

  .empty-text {
    font-size: $font-size-md;
    color: $text-light;
    margin-bottom: $spacing-md;
  }
}

// 加载状态样式
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;

  .loading-text {
    margin-left: $spacing-sm;
    font-size: $font-size-sm;
    color: $text-light;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
