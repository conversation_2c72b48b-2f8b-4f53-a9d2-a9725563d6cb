<!--
 * @Description: 记忆列表页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
-->

<template>
  <view class="page-container">

    <!-- 搜索和筛选区域 -->
    <view class="search-section">
      <view class="search-container">
        <view class="search-bar">
          <view class="search-icon-wrapper">
            <text class="search-icon">🔍</text>
          </view>
          <input
            class="search-input"
            v-model="searchKeyword"
            placeholder="搜索记忆内容..."
            @input="onSearchInput"
            @confirm="searchMemories"
          />
          <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
            <text class="clear-icon">×</text>
          </view>
        </view>
      </view>

      <!-- 标签筛选 -->
      <view class="filter-section" v-if="tags.length > 0">
        <scroll-view class="tag-scroll" scroll-x show-scrollbar="false">
          <view class="tag-list">
            <view
              class="filter-tag"
              :class="{ active: selectedTags.length === 0 }"
              @click="clearTagFilter"
            >
              <text class="tag-text">全部</text>
            </view>
            <view
              v-for="tag in tags"
              :key="tag.id"
              class="filter-tag"
              :class="{ active: selectedTags.includes(tag.id) }"
              :style="{
                backgroundColor: selectedTags.includes(tag.id) ? tag.color : tag.color + '20',
                color: selectedTags.includes(tag.id) ? '#fff' : tag.color,
                borderColor: selectedTags.includes(tag.id) ? tag.color : tag.color + '40'
              }"
              @click="toggleTagFilter(tag.id)"
            >
              <text class="tag-text">{{ tag.tagName }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 记忆列表 -->
    <view class="content-container">
      <view class="memory-list">
        <view
          v-for="(memory, index) in memories"
          :key="memory.id"
          class="memory-card"
          @click="goToDetail(memory)"
        >
          <view class="memory-left">
            <view class="memory-index">{{ index + 1 }}</view>
          </view>
          <view class="memory-main">
            <view class="memory-header">
              <view class="memory-group-badge">{{ getGroupName(memory.groupId) }}</view>
              <text class="memory-time">{{ formatTime(memory.timeCreate) }}</text>
            </view>
            <view class="memory-content">{{ memory.content }}</view>
            <view class="memory-tags" v-if="getMemoryTags(memory.id).length > 0">
              <view
                v-for="tag in getMemoryTags(memory.id)"
                :key="tag.id"
                class="memory-tag"
                :style="{ backgroundColor: tag.color + '20', color: tag.color, borderColor: tag.color + '40' }"
              >
                {{ tag.tagName }}
              </view>
            </view>
          </view>
          <view class="memory-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="noMore && memories.length > 0" class="no-more">
        <text class="no-more-text">— 没有更多数据了 —</text>
      </view>

      <!-- 空状态 -->
      <view v-if="memories.length === 0 && !loading" class="empty-state">
        <view class="empty-icon">✨</view>
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">点击右下角按钮创建记忆</text>
      </view>
    </view>

    <!-- 添加按钮 -->
    <view class="fab" @click="addMemory">
      <text class="fab-icon">+</text>
    </view>
  </view>
</template>

<script>
import { colorUtils } from '../data/colors.js';

export default {
  name: 'MemoryList',
  data() {
    return {
      groupId: null,
      groupName: '',
      memories: [],
      tags: [],
      selectedTags: [],
      searchKeyword: '',
      loading: false,
      refreshing: false,
      noMore: false,
      page: 1,
      pageSize: 10,
      searchTimer: null,
      totalCount: 0
    };
  },

  onLoad(options) {
    // 只进行初始化赋值
    if (options.groupId) {
      this.groupId = parseInt(options.groupId);
      this.groupName = decodeURIComponent(options.groupName || '');
    }

    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: this.groupName || '全部记忆'
    });
  },

  onShow() {
    this.loadData();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.onRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore();
  },

  methods: {
    // ========== API调用方法 ==========

    // 获取标签列表
    async getTagList() {
      try {
        const res = await this.$ajax.get('/memory/tag/list');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取标签列表失败:', error);
        return [];
      }
    },

    // 获取记忆列表
    async getMemoryList(params = {}) {
      try {
        const requestParams = {
          page: params.page || 1,
          pageSize: params.pageSize || 10
        };

        if (params.categoryId) {
          requestParams.categoryId = params.categoryId;
        }

        if (params.tags) {
          requestParams.tags = params.tags;
        }

        if (params.keyword) {
          requestParams.keyword = params.keyword;
        }

        const res = await this.$ajax.get('/memory/data/list', requestParams);
        return {
          list: res?.rows || [],
          total: res?.total || 0
        };
      } catch (error) {
        console.error('获取记忆列表失败:', error);
        return { list: [], total: 0 };
      }
    },

    // ========== 数据格式化方法 ==========

    // 格式化标签数据
    formatTags(tags) {
      return tags.map(tag => ({
        id: tag.id,
        tagName: tag.tagName || tag.name,
        color: this.getColorValue(tag.color || 'success'),
        colorType: tag.color || 'success',
        timeCreate: tag.timeCreate || Date.now() / 1000
      }));
    },

    // 格式化记忆数据
    formatMemories(memories) {
      return memories.map(memory => ({
        id: memory.id,
        content: memory.content,
        title: memory.title || '',
        pics: this.parseImages(memory.images),
        groupId: memory.categoryId,
        categoryName: memory.categoryName || '',
        categoryColor: memory.categoryColor || 'primary',
        categoryIcon: memory.categoryIcon || '📁',
        timeCreate: memory.timeCreate,
        timeUpdate: memory.timeUpdate,
        tags: Array.isArray(memory.tags) ? memory.tags.map(tag => ({
          id: tag.id,
          tagName: tag.tagName || tag.name,
          color: this.getColorValue(tag.color || 'success'),
          colorType: tag.color || 'success'
        })) : []
      }));
    },

    // ========== 工具方法 ==========

    // 获取颜色值
    getColorValue(colorType) {
      return colorUtils.getTagColor(colorType);
    },

    // 处理图片数组
    parseImages(images) {
      if (!images) return [];
      if (Array.isArray(images)) return images;
      if (typeof images === 'string') {
        try {
          // 尝试解析JSON格式
          return JSON.parse(images);
        } catch {
          // 如果不是JSON，按逗号分割
          return images.split(',').filter(img => img.trim());
        }
      }
      return [];
    },

    // 处理标签字符串
    parseTagsToString(tags) {
      if (!tags || tags.length === 0) return '';
      if (Array.isArray(tags)) {
        return tags.map(tag => typeof tag === 'object' ? tag.tagName || tag.name : tag).join(',');
      }
      return tags;
    },

    // 格式化时间
    formatMemoryTime(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp * 1000);
      const now = new Date();
      const diff = now - date;

      // 小于1分钟
      if (diff < 60 * 1000) {
        return '刚刚';
      }

      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes}分钟前`;
      }

      // 小于1天
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours}小时前`;
      }

      // 小于7天
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days}天前`;
      }

      // 超过7天显示具体日期
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // ========== 页面逻辑方法 ==========

        // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 加载标签
        const tagsData = await this.getTagList();
        this.tags = this.formatTags(tagsData);

        // 加载记忆列表 - 使用forceLoad确保初始加载
        await this.loadMemories(false, true);
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载记忆列表
    async loadMemories(isRefresh = false, forceLoad = false) {
      // 如果正在加载中且不是强制加载，直接返回避免重复请求
      if (this.loading && !forceLoad) return;

      if (isRefresh || forceLoad) {
        this.page = 1;
        this.noMore = false;
        this.loading = true;
      }

      try {
        // 构建查询参数
        const params = {
          page: this.page,
          pageSize: this.pageSize
        };

        if (this.groupId) {
          params.categoryId = this.groupId;
        }

        if (this.searchKeyword) {
          params.keyword = this.searchKeyword;
        }

        if (this.selectedTags.length > 0) {
          // 将选中的标签ID转换为标签名称字符串
          const selectedTagNames = this.selectedTags.map(tagId => {
            const tag = this.tags.find(t => t.id === tagId);
            return tag ? tag.tagName : '';
          }).filter(name => name);
          params.tags = selectedTagNames.join(',');
        }

        // 调用API获取数据
        const result = await this.getMemoryList(params);
        const formattedMemories = this.formatMemories(result.list);

        if (isRefresh || this.page == 1) {
          this.memories = formattedMemories;
        } else {
          this.memories = [...this.memories, ...formattedMemories];
        }

        this.totalCount = result.total;
        this.noMore = this.memories.length >= this.totalCount;

      } catch (error) {
        console.error('加载记忆列表失败:', error);
        uni.showToast({
          title: '加载记忆失败',
          icon: 'none'
        });
      } finally {
        if (isRefresh) {
          this.loading = false;
        }
      }
    },

    // 获取分组名称
    getGroupName(groupId) {
      const memory = this.memories.find(m => m.groupId === groupId);
      return memory ? memory.categoryName : '未分组';
    },

    // 获取记忆标签
    getMemoryTags(memoryId) {
      const memory = this.memories.find(m => m.id === memoryId);
      return memory ? memory.tags : [];
    },

    // 格式化时间
    formatTime(timestamp) {
      return this.formatMemoryTime(timestamp);
    },

    // 搜索输入
    onSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        this.searchMemories();
      }, 500);
    },

    // 搜索记忆
    async searchMemories() {
      this.page = 1;
      await this.loadMemories(true);
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.searchMemories();
    },

    // 切换标签筛选
    toggleTagFilter(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
      } else {
        this.selectedTags.push(tagId);
      }
      this.searchMemories();
    },

    // 清除标签筛选
    clearTagFilter() {
      this.selectedTags = [];
      this.searchMemories();
    },

    // 下拉刷新
    async onRefresh() {
      this.refreshing = true;
      await this.loadMemories(true);
      this.refreshing = false;
      // 停止下拉刷新
      uni.stopPullDownRefresh();
    },

    // 上拉加载更多
    async loadMore() {
      if (this.loading || this.noMore) return;

      this.page++;
      await this.loadMemories();
    },

    // 跳转到详情
    goToDetail(memory) {
      uni.navigateTo({
        url: `/pages_memory/detail/index?id=${memory.id}`
      });
    },

    // 添加记忆
    addMemory() {
      uni.navigateTo({
        url: `/pages_memory/detail/index?groupId=${this.groupId || ''}`
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取空状态文本
    getEmptyText() {
      if (this.searchKeyword) {
        return `没有找到包含"${this.searchKeyword}"的记忆`;
      } else if (this.selectedTags.length > 0) {
        return '没有找到符合筛选条件的记忆';
      } else if (this.groupName) {
        return `"${this.groupName}"分组暂无记忆`;
      } else {
        return '暂无记忆内容';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.nav-bar {
  background-color: $card-background;
  box-shadow: $shadow-nav;

  .nav-content {
    display: flex;
    align-items: center;
    padding: $spacing-lg $spacing-md;

    .nav-left {
      display: flex;
      align-items: center;

      .nav-back {
        font-size: $font-size-xxl;
        color: $primary-color;
        margin-right: $spacing-md;
        font-weight: 300;
      }

      .nav-title {
        font-size: $font-size-xl;
        font-weight: 600;
        color: $text-primary;
      }
    }
  }
}

.search-section {
  background: rgba(255, 255, 255, 0.98);
  padding: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);

  .search-container {
    padding: 0 8rpx;

    .search-bar {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 28rpx;
      padding: 16rpx 20rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
      border: 1rpx solid rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:focus-within {
        box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
        border-color: rgba(102, 126, 234, 0.2);
      }

      .search-icon-wrapper {
        margin-right: 12rpx;

        .search-icon {
          font-size: 28rpx;
          color: #94a3b8;
        }
      }

      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #1a202c;
        background: transparent;
        border: none;
        outline: none;
        line-height: 1.4;

        &::placeholder {
          color: #94a3b8;
        }
      }

      .clear-btn {
        margin-left: 12rpx;
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 0, 0, 0.2);
          transform: scale(0.9);
        }

        .clear-icon {
          font-size: 24rpx;
          color: #64748b;
          font-weight: 300;
          line-height: 1;
        }
      }
    }
  }

  .filter-section {
    padding: 0 8rpx;

    .tag-scroll {
      white-space: nowrap;

      .tag-list {
        display: inline-flex;
        gap: 12rpx;
        padding: 8rpx 0;

        .filter-tag {
          display: inline-flex;
          align-items: center;
          padding: 10rpx 18rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 600;
          white-space: nowrap;
          border: 1rpx solid;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

          &.active {
            transform: translateY(-1rpx);
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
          }

          &:active {
            transform: scale(0.95);
          }

          .tag-text {
            line-height: 1;
          }
        }
      }
    }
  }
}

.content-container {
  padding: 0 20rpx;
  padding-bottom: 140rpx; // 为悬浮按钮留出空间
}

.memory-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 20rpx 0;

  .memory-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: flex-start;
    gap: 16rpx;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20rpx);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:active {
      transform: translateY(1rpx) scale(0.995);
      box-shadow: 0 1rpx 12rpx rgba(0, 0, 0, 0.08);

      &::before {
        opacity: 1;
      }
    }

    .memory-left {
      flex-shrink: 0;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 4rpx;

      .memory-index {
        width: 32rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: 600;
        color: #4a5568;
        border: 1rpx solid rgba(0, 0, 0, 0.08);
      }
    }

    .memory-main {
      flex: 1;
      min-width: 0;

      .memory-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8rpx;

        .memory-group-badge {
          background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
          color: #667eea;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          font-weight: 600;
          border: 1rpx solid #667eea20;
        }

        .memory-time {
          font-size: 20rpx;
          color: #a0aec0;
          font-weight: 500;
        }
      }

      .memory-content {
        font-size: 26rpx;
        color: #2d3748;
        line-height: 1.6;
        margin-bottom: 12rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        word-break: break-word;
      }

      .memory-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .memory-tag {
          font-size: 18rpx;
          padding: 4rpx 10rpx;
          border-radius: 10rpx;
          font-weight: 500;
          border: 1rpx solid;
          line-height: 1.2;
        }
      }
    }

    .memory-arrow {
      flex-shrink: 0;
      width: 24rpx;
      height: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8rpx;

      .arrow-icon {
        font-size: 32rpx;
        color: #cbd5e0;
        font-weight: 300;
        line-height: 1;
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  gap: 16rpx;

  .loading-spinner {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid #e2e8f0;
    border-top: 3rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 24rpx;
    color: #94a3b8;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-more {
  text-align: center;
  padding: 32rpx;

  .no-more-text {
    font-size: 24rpx;
    color: #cbd5e0;
    font-weight: 500;
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  margin-top: 40rpx;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    display: block;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #4a5568;
    margin-bottom: 8rpx;
    display: block;
    font-weight: 600;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #a0aec0;
    line-height: 1.4;
  }
}

.fab {
  position: fixed;
  right: 20rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
  }

  .fab-icon {
    font-size: 64rpx;
    color: white;
    font-weight: 300;
    line-height: 1;
  }
}
</style>
