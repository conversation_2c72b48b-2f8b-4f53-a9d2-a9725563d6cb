/**
 * @Description: 记忆模块颜色配置
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
 */

// 颜色类型枚举
export const COLOR_TYPES = {
  PRIMARY: 'primary',
  SUCCESS: 'success',
  WARNING: 'warning',
  DANGER: 'danger',
  INFO: 'info'
};

// 基础颜色映射
export const BASE_COLORS = {
  primary: '#667eea',
  success: '#4caf50',
  warning: '#ff9800',
  danger: '#f44336',
  info: '#607d8b'
};

// 首页分组卡片 - 浅色渐变背景
export const GROUP_CARD_GRADIENTS = {
  primary: 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.12) 100%)',
  success: 'linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(56, 178, 172, 0.12) 100%)',
  warning: 'linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  danger: 'linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  info: 'linear-gradient(135deg, rgba(129, 140, 248, 0.15) 0%, rgba(139, 92, 246, 0.12) 100%)'
};

// 标签 - 深色背景设计
export const TAG_COLORS = {
  primary: '#667eea',
  success: '#4caf50',
  warning: '#ff9800',
  danger: '#f44336',
  info: '#607d8b'
};

// 标签浅色背景（用于选择器等）
export const TAG_LIGHT_COLORS = {
  primary: 'rgba(102, 126, 234, 0.1)',
  success: 'rgba(76, 175, 80, 0.1)',
  warning: 'rgba(255, 152, 0, 0.1)',
  danger: 'rgba(244, 67, 54, 0.1)',
  info: 'rgba(96, 125, 139, 0.1)'
};

// 分组管理选择器 - 浅色渐变
export const GROUP_SELECTOR_GRADIENTS = {
  primary: 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.12) 100%)',
  success: 'linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(56, 178, 172, 0.12) 100%)',
  warning: 'linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  danger: 'linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  info: 'linear-gradient(135deg, rgba(129, 140, 248, 0.15) 0%, rgba(139, 92, 246, 0.12) 100%)'
};

// 标签选择器 - 浅色渐变
export const TAG_SELECTOR_GRADIENTS = {
  primary: 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.12) 100%)',
  success: 'linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(56, 178, 172, 0.12) 100%)',
  warning: 'linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  danger: 'linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(255, 142, 83, 0.12) 100%)',
  info: 'linear-gradient(135deg, rgba(129, 140, 248, 0.15) 0%, rgba(139, 92, 246, 0.12) 100%)'
};

// 颜色类型选项定义
export const COLOR_TYPE_OPTIONS = [
  { type: 'primary', name: '主色' },
  { type: 'success', name: '成功' },
  { type: 'warning', name: '警告' },
  { type: 'danger', name: '危险' },
  { type: 'info', name: '信息' }
];

// 工具函数
export const colorUtils = {
  // 获取基础颜色
  getBaseColor(type) {
    // 缺省处理：空值、undefined、null或不存在的类型都返回primary
    if (!type || typeof type !== 'string' || !BASE_COLORS[type]) {
      return BASE_COLORS.primary;
    }
    return BASE_COLORS[type];
  },

  // 获取分组卡片渐变
  getGroupCardGradient(type) {
    if (!type || typeof type !== 'string' || !GROUP_CARD_GRADIENTS[type]) {
      return GROUP_CARD_GRADIENTS.primary;
    }
    return GROUP_CARD_GRADIENTS[type];
  },

  // 获取标签颜色
  getTagColor(type) {
    if (!type || typeof type !== 'string' || !TAG_COLORS[type]) {
      return TAG_COLORS.primary;
    }
    return TAG_COLORS[type];
  },

  // 获取标签浅色背景
  getTagLightColor(type) {
    if (!type || typeof type !== 'string' || !TAG_LIGHT_COLORS[type]) {
      return TAG_LIGHT_COLORS.primary;
    }
    return TAG_LIGHT_COLORS[type];
  },

  // 获取分组选择器渐变
  getGroupSelectorGradient(type) {
    if (!type || typeof type !== 'string' || !GROUP_SELECTOR_GRADIENTS[type]) {
      return GROUP_SELECTOR_GRADIENTS.primary;
    }
    return GROUP_SELECTOR_GRADIENTS[type];
  },

  // 获取标签选择器渐变
  getTagSelectorGradient(type) {
    if (!type || typeof type !== 'string' || !TAG_SELECTOR_GRADIENTS[type]) {
      return TAG_SELECTOR_GRADIENTS.primary;
    }
    return TAG_SELECTOR_GRADIENTS[type];
  }
};

export default {
  COLOR_TYPES,
  BASE_COLORS,
  GROUP_CARD_GRADIENTS,
  TAG_COLORS,
  TAG_LIGHT_COLORS,
  GROUP_SELECTOR_GRADIENTS,
  TAG_SELECTOR_GRADIENTS,
  COLOR_TYPE_OPTIONS,
  colorUtils
};