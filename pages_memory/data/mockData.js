/**
 * @Description: 记忆系统演示数据
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
 */

// 用户标签表 - dede_memory_tag
export const mockTags = [
  {
    id: 1,
    userId: 1,
    tagName: 'web',
    color: '#2979ff',
    isDelete: 0,
    timeCreate: 1721030400
  },
  {
    id: 2,
    userId: 1,
    tagName: 'vue',
    color: '#4caf50',
    isDelete: 0,
    timeCreate: 1721030400
  },
  {
    id: 3,
    userId: 1,
    tagName: 'javascript',
    color: '#ff9800',
    isDelete: 0,
    timeCreate: 1721030400
  },
  {
    id: 4,
    userId: 1,
    tagName: 'uniapp',
    color: '#9c27b0',
    isDelete: 0,
    timeCreate: 1721030400
  },
  {
    id: 5,
    userId: 1,
    tagName: '学习',
    color: '#f44336',
    isDelete: 0,
    timeCreate: 1721030400
  }
];

// 记忆分组表 - dede_memory_group
export const mockGroups = [
  {
    id: 1,
    userId: 1,
    groupName: '技术笔记',
    weight: 100,
    isDelete: 0
  },
  {
    id: 2,
    userId: 1,
    groupName: '生活记录',
    weight: 90,
    isDelete: 0
  },
  {
    id: 3,
    userId: 1,
    groupName: '工作备忘',
    weight: 80,
    isDelete: 0
  },
  {
    id: 4,
    userId: 1,
    groupName: '学习心得',
    weight: 70,
    isDelete: 0
  }
];

// 记忆详情表 - dede_memory
export const mockMemories = [
  {
    id: 1,
    userId: 1,
    content: 'Vue3的Composition API使用心得：使用setup()函数可以更好地组织逻辑，reactive和ref的区别需要注意。',
    pics: '["https://example.com/pic1.jpg", "https://example.com/pic2.jpg"]',
    isDelete: 0,
    timeCreate: 1721030400,
    type: 'text',
    groupId: 1
  },
  {
    id: 2,
    userId: 1,
    content: 'uniapp开发小程序时遇到的坑：页面跳转时需要注意参数传递，使用uni.navigateTo时参数长度有限制。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721030300,
    type: 'text',
    groupId: 1
  },
  {
    id: 3,
    userId: 1,
    content: '今天学习了JavaScript的异步编程，Promise、async/await的使用场景和注意事项。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721030200,
    type: 'text',
    groupId: 4
  },
  {
    id: 4,
    userId: 1,
    content: '工作中需要优化的几个接口：用户登录接口响应时间过长，需要添加缓存机制。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721030100,
    type: 'text',
    groupId: 3
  },
  {
    id: 5,
    userId: 1,
    content: '生活小贴士：每天早上喝一杯温水有助于新陈代谢，记得要坚持运动。',
    pics: '["https://example.com/life1.jpg"]',
    isDelete: 0,
    timeCreate: 1721030000,
    type: 'text',
    groupId: 2
  },
  {
    id: 6,
    userId: 1,
    content: 'CSS Grid布局的使用技巧：grid-template-areas可以让布局更直观，配合媒体查询实现响应式设计。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721029900,
    type: 'text',
    groupId: 1
  },
  {
    id: 7,
    userId: 1,
    content: '项目部署注意事项：nginx配置文件需要正确设置，SSL证书要及时更新。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721029800,
    type: 'text',
    groupId: 3
  },
  {
    id: 8,
    userId: 1,
    content: '读书笔记：《JavaScript高级程序设计》第三章，变量声明和作用域的深入理解。',
    pics: '',
    isDelete: 0,
    timeCreate: 1721029700,
    type: 'text',
    groupId: 4
  }
];

// 记忆标签关联表 - dede_memory_tag_relation
export const mockMemoryTagRelations = [
  { memoryId: 1, tagId: 2 }, // Vue3心得 - vue
  { memoryId: 1, tagId: 1 }, // Vue3心得 - web
  { memoryId: 2, tagId: 4 }, // uniapp开发 - uniapp
  { memoryId: 2, tagId: 1 }, // uniapp开发 - web
  { memoryId: 3, tagId: 3 }, // JavaScript学习 - javascript
  { memoryId: 3, tagId: 5 }, // JavaScript学习 - 学习
  { memoryId: 6, tagId: 1 }, // CSS Grid - web
  { memoryId: 8, tagId: 3 }, // 读书笔记 - javascript
  { memoryId: 8, tagId: 5 }  // 读书笔记 - 学习
];

// 工具函数：根据记忆ID获取关联的标签
export function getTagsByMemoryId(memoryId) {
  const relationIds = mockMemoryTagRelations
    .filter(relation => relation.memoryId === memoryId)
    .map(relation => relation.tagId);
  
  return mockTags.filter(tag => relationIds.includes(tag.id));
}

// 工具函数：根据标签ID获取关联的记忆
export function getMemoriesByTagId(tagId) {
  const relationMemoryIds = mockMemoryTagRelations
    .filter(relation => relation.tagId === tagId)
    .map(relation => relation.memoryId);
  
  return mockMemories.filter(memory => relationMemoryIds.includes(memory.id));
}

// 工具函数：根据分组ID获取记忆列表
export function getMemoriesByGroupId(groupId) {
  return mockMemories.filter(memory => memory.groupId === groupId);
}

// 工具函数：格式化时间戳
export function formatTime(timestamp) {
  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前';
  } else if (diff < 86400000) { // 1天内
    return Math.floor(diff / 3600000) + '小时前';
  } else if (diff < 2592000000) { // 30天内
    return Math.floor(diff / 86400000) + '天前';
  } else {
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
           String(date.getDate()).padStart(2, '0');
  }
}

// 工具函数：搜索记忆
export function searchMemories(keyword, groupId = null, tagIds = []) {
  let results = mockMemories;
  
  // 按分组筛选
  if (groupId) {
    results = results.filter(memory => memory.groupId === groupId);
  }
  
  // 按标签筛选
  if (tagIds.length > 0) {
    const memoryIds = mockMemoryTagRelations
      .filter(relation => tagIds.includes(relation.tagId))
      .map(relation => relation.memoryId);
    results = results.filter(memory => memoryIds.includes(memory.id));
  }
  
  // 按关键词搜索
  if (keyword) {
    results = results.filter(memory => 
      memory.content.toLowerCase().includes(keyword.toLowerCase())
    );
  }
  
  return results.sort((a, b) => b.timeCreate - a.timeCreate);
}
