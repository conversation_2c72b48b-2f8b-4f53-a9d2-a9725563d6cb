/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-19 17:02:20
 */
/**
 * @Description: 记忆模块图标配置
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
 */

// 记忆碎片分组图标 - 精选30个实用图标
export const MEMORY_ICONS = [
  // 基础类
  '📁', '📂', '📋', '📌',

  // 思考学习类
  '💡', '🧠', '📚', '📖', '📝', '✏️',

  // 工作创意类
  '💼', '💻', '📊', '🎯', '🎨', '✨',

  // 生活日常类
  '🏠', '☕', '🍀', '🌸',

  // 情感心情类
  '❤️', '😊', '🌈', '⭐',

  // 时间计划类
  '⏰', '📅', '⌛',

  // 目标成就类
  '🏆', '🎁', '🔥', '🚀', '💎'
];

// 默认图标（最常用的10个）
export const DEFAULT_ICONS = [
  '📁', '💡', '📚', '🎯', '💼',
  '✨', '🔥', '💎', '🚀', '⭐'
];

// 根据关键词推荐图标
export const getRecommendedIcons = (keyword) => {
  const recommendations = {
    '工作': ['💼', '💻', '📊', '🎯'],
    '学习': ['📚', '📖', '📝', '💡', '🧠'],
    '生活': ['🏠', '☕', '🍀', '🌸'],
    '想法': ['💡', '🧠', '✨', '💎'],
    '创意': ['🎨', '✨', '💡', '🚀'],
    '计划': ['📅', '⏰', '🎯'],
    '目标': ['🎯', '🏆', '🔥'],
    '心情': ['😊', '❤️', '🌈'],
    '技术': ['💻', '📊']
  };

  const lowerKeyword = keyword.toLowerCase();
  for (const [key, icons] of Object.entries(recommendations)) {
    if (lowerKeyword.includes(key)) {
      return icons;
    }
  }

  return DEFAULT_ICONS;
};

// 图标工具函数
export const iconUtils = {
  // 获取有效图标，缺省返回📁
  getValidIcon(icon) {
    // 缺省处理：空值、undefined、null或不是字符串都返回📁
    if (!icon || typeof icon !== 'string' || icon.trim() === '') {
      return '📁';
    }
    // 检查是否是有效的emoji图标
    const trimmedIcon = icon.trim();
    if (MEMORY_ICONS.includes(trimmedIcon)) {
      return trimmedIcon;
    }
    // 如果不在预定义列表中，仍然返回（可能是新的emoji），但至少确保不为空
    return trimmedIcon || '📁';
  },

  // 获取随机图标
  getRandomIcon(index = 0) {
    if (typeof index !== 'number' || index < 0) {
      return '📁';
    }
    return MEMORY_ICONS[index % MEMORY_ICONS.length] || '📁';
  }
};

export default {
  MEMORY_ICONS,
  DEFAULT_ICONS,
  getRecommendedIcons,
  iconUtils
};