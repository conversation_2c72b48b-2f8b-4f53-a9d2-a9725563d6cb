<!--
 * @Description: 标签管理页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
-->

<template>
  <view class="page-container">
    <!-- 标签内容 -->
    <view class="content">
      <view v-if="tags.length === 0" class="empty-state">
        <text class="empty-text">暂无标签</text>
        <text class="empty-desc">点击右上角 + 创建标签</text>
      </view>

      <view v-else class="tag-container">
        <view
          v-for="tag in tags"
          :key="tag.id"
          class="tag-item"
          :style="{ backgroundColor: tag.color }"
          @tap="editTag(tag)"
        >
          <text class="tag-name">{{ tag.tagName }}</text>
          <view class="tag-delete" @tap.stop="deleteTag(tag)">
            <uni-icons type="close" size="12" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部添加按钮 -->
    <view class="add-btn" @click="addTag">
      <uni-icons type="plus" size="20" color="#fff"></uni-icons>
      <text class="add-text">新增标签</text>
    </view>

    <!-- 标签编辑弹窗 -->
    <u-popup :show="showEditModal" mode="center" border-radius="12">
      <view class="modal">
        <view class="modal-header">
          <text class="modal-title">{{ isEdit ? '编辑标签' : '新增标签' }}</text>
        </view>

        <view class="modal-body">
          <!-- 标签名称 -->
          <view class="form-item">
            <text class="form-label">标签名称</text>
            <input
              class="form-input"
              v-model="formData.tagName"
              placeholder="请输入标签名称"
              maxlength="8"
            />
          </view>

          <!-- 颜色选择 -->
          <view class="form-item">
            <text class="form-label">选择颜色</text>
            <view class="color-list">
              <view
                v-for="colorType in colorTypes"
                :key="colorType.type"
                class="color-item"
                :class="{ active: formData.color === colorType.type }"
                :style="{ backgroundColor: getColorValue(colorType.type) }"
                @click="formData.color = colorType.type"
              >
                <uni-icons v-if="formData.color === colorType.type" type="checkmarkempty" size="14" color="#fff"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 预览 -->
          <view class="form-item">
            <text class="form-label">预览</text>
            <view class="preview">
              <view
                class="preview-tag"
                :style="{ backgroundColor: getColorValue(formData.color) }"
              >
                <text class="preview-text">{{ formData.tagName || '标签名称' }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <view class="btn-cancel" @click="cancelEdit">取消</view>
          <view class="btn-confirm" @click="saveTag">{{ isEdit ? '保存' : '创建' }}</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { colorUtils, COLOR_TYPE_OPTIONS, TAG_SELECTOR_GRADIENTS } from '../data/colors.js';

export default {
  name: 'TagManage',



  data() {
    return {
      tags: [],
      showEditModal: false,
      isEdit: false,
      editingTagId: null,
      formData: {
        tagName: '',
        color: 'primary'
      },
      loading: false
    };
  },

  computed: {
    // 颜色类型选项
    colorTypes() {
      return COLOR_TYPE_OPTIONS.map(option => ({
        ...option,
        gradient: TAG_SELECTOR_GRADIENTS[option.type]
      }));
    }
  },

  onLoad() {
    // 只进行初始化赋值
  },

  onShow() {
    this.loadTags();
  },

  methods: {
    // ========== API调用方法 ==========

    // 获取标签列表
    async getTagList() {
      try {
        const res = await this.$ajax.get('/memory/tag/list');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取标签列表失败:', error);
        return [];
      }
    },

    // 保存标签
    async saveTagData(data) {
      try {
        const params = {
          name: data.tagName || data.name,
          color: data.color || 'success'
        };

        if (data.id) {
          params.id = data.id;
        }

        const res = await this.$ajax.post('/memory/tag/save', params);
        return res?.code === 200;
      } catch (error) {
        console.error('保存标签失败:', error);
        return false;
      }
    },

    // 删除标签
    async deleteTagData(id) {
      try {
        const res = await this.$ajax.post('/memory/tag/delete', { id });
        return res?.code === 200;
      } catch (error) {
        console.error('删除标签失败:', error);
        return false;
      }
    },

    // ========== 数据格式化方法 ==========

    // 格式化标签数据
    formatTags(tags) {
      return tags.map(tag => ({
        id: tag.id,
        tagName: tag.tagName || tag.name,
        color: this.getColorValue(tag.color || 'success'),
        colorType: tag.color || 'success',
        timeCreate: tag.timeCreate || Date.now() / 1000
      }));
    },

    // ========== 工具方法 ==========

    // 获取颜色值
    getColorValue(colorType) {
      return colorUtils.getTagColor(colorType);
    },

    // ========== 页面逻辑方法 ==========

    // 加载标签数据
    async loadTags() {
      this.loading = true;
      try {
        const tagsData = await this.getTagList();
        this.tags = this.formatTags(tagsData);
      } catch (error) {
        console.error('加载标签数据失败:', error);
        uni.showToast({
          title: '加载标签失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取标签使用次数
    getTagUsageCount(tagId) {
      return 0;
    },

    // 新增标签
    addTag() {
      this.formData = {
        tagName: '',
        color: 'primary'
      };
      this.isEdit = false;
      this.editingTagId = null;
      this.showEditModal = true;
    },

    // 编辑标签
    editTag(tag) {
      this.formData = {
        tagName: tag.tagName,
        color: tag.colorType
      };
      this.isEdit = true;
      this.editingTagId = tag.id;
      this.showEditModal = true;
    },

    // 删除标签
    async deleteTag(tag) {
      if(await this.$confirm1(`确定删除标签"${tag.tagName}"吗？`) == 1){
        this.loading = true;
        try {
          const result = await this.deleteTagData(tag.id);
          if (result) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            await this.loadTags();
          } else {
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('删除标签失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        } finally {
          this.loading = false;
        }
      }
    },

    // 保存标签
    async saveTag() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      try {
        const tagData = {
          tagName: this.formData.tagName.trim(),
          color: this.formData.color
        };

        if (this.isEdit && this.editingTagId) {
          tagData.id = this.editingTagId;
        }

        const result = await this.saveTagData(tagData);
        if (result) {
          const action = this.isEdit ? '保存' : '创建';
          uni.showToast({
            title: `${action}成功`,
            icon: 'success'
          });

          this.showEditModal = false;
          await this.loadTags();
        } else {
          const action = this.isEdit ? '保存' : '创建';
          uni.showToast({
            title: `${action}失败`,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('保存标签失败:', error);
        const action = this.isEdit ? '保存' : '创建';
        uni.showToast({
          title: `${action}失败`,
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 取消编辑
    cancelEdit() {
      this.showEditModal = false;
      this.formData = {
        tagName: '',
        color: 'primary'
      };
    },

    // 表单验证
    validateForm() {
      if (!this.formData.tagName.trim()) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        });
        return false;
      }

      // 检查标签名是否与其他标签重复
      if (this.tags.some(tag => tag.id !== this.editingTagId && tag.tagName === this.formData.tagName.trim())) {
        uni.showToast({
          title: '标签名称已存在',
          icon: 'none'
        });
        return false;
      }

      return true;
    },


  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f8f9fa;
  position: relative;
}

// 内容区域
.content {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 0;

  .empty-text {
    display: block;
    font-size: 32rpx;
    color: #999;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #ccc;
  }
}

// 标签容器
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

// 标签项
.tag-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  position: relative;

  .tag-name {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
  }

  .tag-delete {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 底部添加按钮
.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 32rpx;
  right: 32rpx;
  height: 88rpx;
  background: #5677fc;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(86, 119, 252, 0.3);

  &:active {
    background: #4054b2;
    transform: translateY(1rpx);
  }

  .add-text {
    font-size: 30rpx;
    color: #fff;
    font-weight: 500;
  }
}

// 弹窗
.modal {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;

  .modal-header {
    padding: 40rpx 40rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    text-align: center;

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .modal-body {
    padding: 40rpx;

    .form-item {
      margin-bottom: 40rpx;

      .form-label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        font-weight: 500;
      }

      .form-input {
        width: 100%;
        height: 80rpx;
        padding: 0 24rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333;
        background: #fff;
        box-sizing: border-box;

        &:focus {
          border-color: #5677fc;
        }
      }

      .color-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .color-item {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 4rpx solid transparent;

          &.active {
            border-color: #333;
          }
        }
      }

      .preview {
        .preview-tag {
          display: inline-flex;
          align-items: center;
          padding: 16rpx 24rpx;
          border-radius: 40rpx;

          .preview-text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 500;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .btn-cancel, .btn-confirm {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
      font-weight: 500;
    }

    .btn-cancel {
      color: #999;
      border-right: 1rpx solid #f0f0f0;

      &:active {
        background: #f8f9fa;
      }
    }

    .btn-confirm {
      color: #5677fc;

      &:active {
        background: rgba(86, 119, 252, 0.05);
      }
    }
  }
}
</style>
