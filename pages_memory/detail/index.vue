<!--
 * @Description: 记忆详情页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15
-->

<template>
  <view class="page-container">
    <!-- 编辑模式 -->
    <view v-if="isEditMode" class="edit-container">
      <view class="form-content">
        <!-- 分组选择 -->
        <view class="form-card">
          <view class="card-header">
            <text class="card-title">所属分组</text>
          </view>
          <picker
            :value="selectedGroupIndex"
            :range="groupOptions"
            range-key="groupName"
            @change="onGroupChange"
          >
            <view class="group-selector">
              <view class="group-info">
                <text class="group-icon">📁</text>
                <text class="group-name">{{ currentGroup.groupName || '请选择分组' }}</text>
              </view>
              <text class="selector-arrow">›</text>
            </view>
          </picker>
        </view>

        <!-- 记忆内容 -->
        <view class="form-card">
          <view class="card-header">
            <text class="card-title">记忆内容</text>
            <text class="required-mark">*</text>
          </view>
          <view class="textarea-wrapper">
            <textarea
              class="content-textarea"
              v-model="formData.content"
              placeholder="请输入记忆内容..."
              maxlength="1000"
              :show-confirm-bar="false"
              auto-height
            />
            <text class="char-counter">{{ formData.content.length }}/1000</text>
          </view>
        </view>

        <!-- 标签选择 -->
        <view class="form-card">
          <view class="card-header">
            <text class="card-title">选择标签</text>
            <view class="tag-manage-btn" @click="openTagSelector">
              <text class="manage-text">管理</text>
            </view>
          </view>

          <!-- 已选标签展示 -->
          <view class="selected-tags-display" v-if="selectedTags.length > 0">
            <view
              v-for="tag in selectedTags"
              :key="tag.id"
              class="selected-tag"
              :style="{ backgroundColor: tag.color + '20', color: tag.color, borderColor: tag.color + '40' }"
              @click="removeTag(tag.id)"
            >
              <text class="tag-name">{{ tag.tagName }}</text>
              <text class="tag-remove">×</text>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-tags" @click="openTagSelector">
            <text class="empty-icon">🏷️</text>
            <text class="empty-text">点击添加标签</text>
          </view>
        </view>
      </view>

      <!-- 固定底部按钮 -->
      <view class="fixed-actions">
        <view class="action-btn cancel-btn" @click="cancelEdit">
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn save-btn" @click="saveMemory">
          <text class="btn-text">保存</text>
        </view>
      </view>
    </view>

    <!-- 标签选择弹窗 -->
    <u-popup :show="showTagSelector" @close="showTagSelector = false" mode="bottom" border-radius="16">
      <view class="tag-selector-popup" v-if="showTagSelector">
        <view class="popup-header">
          <text class="popup-title">选择标签</text>
          <view class="close-btn" @click="showTagSelector = false">
            <text class="close-icon">×</text>
          </view>
        </view>

        <!-- 已选标签 -->
        <view class="popup-section" v-if="selectedTags.length > 0">
          <text class="section-title">已选标签</text>
          <view class="tag-list">
            <view
              v-for="tag in selectedTags"
              :key="tag.id"
              class="popup-tag selected"
              :style="{ backgroundColor: tag.color, color: '#fff' }"
              @click="removeTag(tag.id)"
            >
              <text class="tag-text">{{ tag.tagName }}</text>
              <text class="tag-action">×</text>
            </view>
          </view>
        </view>

        <!-- 可选标签 -->
        <view class="popup-section">
          <text class="section-title">可选标签</text>
          <view class="tag-list">
            <view
              v-for="tag in availableTags"
              :key="tag.id"
              class="popup-tag available"
              :style="{ backgroundColor: tag.color + '20', color: tag.color, borderColor: tag.color + '40' }"
              @click="addTag(tag)"
            >
              <text class="tag-text">{{ tag.tagName }}</text>
              <text class="tag-action">+</text>
            </view>
          </view>
        </view>

        <!-- 新增标签 -->
        <view class="popup-section">
          <text class="section-title">新增标签</text>
          <view class="new-tag-input">
            <input
              class="tag-name-input"
              v-model="customTagName"
              placeholder="输入新标签名称"
              maxlength="10"
              @confirm="addCustomTag"
            />
            <view class="add-tag-btn" @click="addCustomTag">
              <text class="add-text">添加</text>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 查看模式 -->
    <view v-if="!isEditMode" class="detail-container">
      <!-- 记忆信息 -->
      <view class="memory-info">
        <view class="memory-header">
          <text class="group-name">{{ currentGroup.groupName }}</text>
          <text class="create-time">{{ formatTime(memoryData.timeCreate) }}</text>
        </view>

        <view class="memory-content">{{ memoryData.content }}</view>

        <!-- 标签展示 -->
        <view class="memory-tags" v-if="selectedTags.length > 0">
          <view
            v-for="tag in selectedTags"
            :key="tag.id"
            class="tag"
            :style="{ backgroundColor: tag.color + '20', color: tag.color }"
          >
            {{ tag.tagName }}
          </view>
        </view>

        <!-- 图片展示 -->
        <view class="memory-images" v-if="imageList.length > 0">
          <image
            v-for="(img, index) in imageList"
            :key="index"
            :src="img"
            class="memory-image"
            mode="aspectFill"
            @click="previewImage(index)"
          />
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="btn btn-secondary" @click="editMemory">编辑</view>
        <view class="btn btn-danger ml-sm" @click="deleteMemory">删除</view>
      </view>
    </view>
  </view>
</template>

<script>
import { colorUtils, COLOR_TYPE_OPTIONS } from '../data/colors.js';

export default {
  name: 'MemoryDetail',
  data() {
    return {
      memoryId: null,
      groupId: null,
      isEditMode: false,
      isNewMemory: false,
      memoryData: {},
      formData: {
        content: '',
        groupId: null
      },
      groups: [],
      tags: [],
      selectedTags: [],
      customTagName: '',
      imageList: [],
      showTagSelector: false,
      loading: false
    };
  },

  computed: {
    // 当前分组
    currentGroup() {
      const targetGroupId = this.formData.groupId || this.memoryData.groupId;
      if (!targetGroupId) return {};

      // 尝试数字和字符串匹配
      const group = this.groups.find(g => g.id == targetGroupId || g.id === targetGroupId);
      console.log('寻找分组:', { targetGroupId, groups: this.groups, foundGroup: group });
      return group || {};
    },

    // 分组选项
    groupOptions() {
      return this.groups;
    },

    // 选中的分组索引
    selectedGroupIndex() {
      const targetGroupId = this.formData.groupId || this.memoryData.groupId;
      if (!targetGroupId || this.groups.length === 0) return 0;

      // 尝试数字和字符串匹配
      const index = this.groups.findIndex(g => g.id == targetGroupId || g.id === targetGroupId);
      console.log('计算分组索引:', { targetGroupId, index, groups: this.groups });
      return Math.max(0, index); // 确保返回值不为负数
    },

    // 可选标签（排除已选择的）
    availableTags() {
      const selectedIds = this.selectedTags.map(tag => tag.id);
      return this.tags.filter(tag => !selectedIds.includes(tag.id));
    },

    // 标签颜色类型选项
    tagColorTypes() {
      return COLOR_TYPE_OPTIONS.map(option => option.type);
    }
  },

  onLoad(options) {
    // 只进行初始化赋值
    if (options.id) {
      this.memoryId = parseInt(options.id);
    } else {
      // 新增模式
      this.isNewMemory = true;
      this.isEditMode = true;
      this.groupId = options.groupId ? parseInt(options.groupId) : null;
      this.formData.groupId = this.groupId;
    }

    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: this.isNewMemory ? '新增记忆' : '记忆详情'
    });
  },

  async onShow() {
    await this.loadBasicData();
    if (this.memoryId) {
      await this.loadMemoryData();
    }
  },

  methods: {
    // ========== API调用方法 ==========

    // 获取分类列表
    async getCategoryList() {
      try {
        const res = await this.$ajax.get('/memory/category/list');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取分类列表失败:', error);
        return [];
      }
    },

    // 获取标签列表
    async getTagList() {
      try {
        const res = await this.$ajax.get('/memory/tag/list');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取标签列表失败:', error);
        return [];
      }
    },

    // 获取记忆详情
    async getMemoryDetail(id) {
      try {
        const res = await this.$ajax.get('/memory/data/get', { id });
        return res?.code === 200 ? res.data : null;
      } catch (error) {
        console.error('获取记忆详情失败:', error);
        return null;
      }
    },

    // 保存记忆
    async saveMemoryData(data) {
      try {
        const params = {
          title: data.title || '',
          content: data.content || '',
          categoryId: data.groupId,
          tags: this.parseTagsToString(this.selectedTags),
          images: Array.isArray(data.images) ? data.images.join(',') : (data.images || '')
        };

        if (data.id) {
          params.id = data.id;
        }

        const res = await this.$ajax.post('/memory/data/save', params);
        if (res?.code == 200) {
          this.$toast(res?.msg || '保存成功');
          return true;
        } else {
          this.$toast(res?.msg || '保存失败');
          return false;
        }
      } catch (error) {
        console.error('保存记忆失败:', error);
        return false;
      }
    },

    // 删除记忆
    async deleteMemoryData(id) {
      try {
        const res = await this.$ajax.post('/memory/data/delete', { id });
        return res?.code === 200;
      } catch (error) {
        console.error('删除记忆失败:', error);
        return false;
      }
    },

    // 保存标签
    async saveTagData(data) {
      try {
        const params = {
          name: data.tagName || data.name,
          color: data.color || 'success'
        };

        if (data.id) {
          params.id = data.id;
        }

        const res = await this.$ajax.post('/memory/tag/save', params);
        return res?.code === 200;
      } catch (error) {
        console.error('保存标签失败:', error);
        return false;
      }
    },

    // ========== 数据格式化方法 ==========

    // 格式化分类数据
    formatCategories(categories) {
      return categories.map((category, index) => ({
        id: category.id,
        groupName: category.categoryName || category.name,
        weight: category.weight || 0,
        color: category.color || 'primary',
        colorValue: this.getColorValue(category.color || 'primary'),
        icon: category.icon || this.getRandomIcon(index),
        timeCreate: category.timeCreate || Date.now() / 1000
      }));
    },

    // 格式化标签数据
    formatTags(tags) {
      return tags.map(tag => ({
        id: tag.id,
        tagName: tag.tagName || tag.name,
        color: this.getColorValue(tag.color || 'success'),
        colorType: tag.color || 'success',
        timeCreate: tag.timeCreate || Date.now() / 1000
      }));
    },

    // 格式化记忆数据
    formatMemory(memory) {
      return {
        id: memory.id,
        content: memory.content,
        title: memory.title || '',
        pics: this.parseImages(memory.images),
        groupId: memory.categoryId,
        categoryName: memory.categoryName || '',
        categoryColor: memory.categoryColor || 'primary',
        categoryIcon: memory.categoryIcon || '📁',
        timeCreate: memory.timeCreate,
        timeUpdate: memory.timeUpdate,
        tags: Array.isArray(memory.tags) ? memory.tags.map(tag => ({
          id: tag.id,
          tagName: tag.tagName || tag.name,
          color: this.getColorValue(tag.color || 'success'),
          colorType: tag.color || 'success'
        })) : []
      };
    },

    // ========== 工具方法 ==========

    // 获取颜色值
    getColorValue(colorType) {
      return colorUtils.getTagColor(colorType);
    },

    // 获取随机图标
    getRandomIcon(index = 0) {
      const icons = ['📁', '💼', '📚', '💡', '🎯', '🌟', '🔥', '💎', '🚀', '✨'];
      return icons[index % icons.length];
    },

    // 处理图片数组
    parseImages(images) {
      if (!images) return [];
      if (Array.isArray(images)) return images;
      if (typeof images === 'string') {
        try {
          // 尝试解析JSON格式
          return JSON.parse(images);
        } catch {
          // 如果不是JSON，按逗号分割
          return images.split(',').filter(img => img.trim());
        }
      }
      return [];
    },

    // 处理标签字符串
    parseTagsToString(tags) {
      if (!tags || tags.length === 0) return '';
      if (Array.isArray(tags)) {
        return tags.map(tag => typeof tag === 'object' ? tag.tagName || tag.name : tag).join(',');
      }
      return tags;
    },

    // 格式化时间
    formatMemoryTime(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp * 1000);
      const now = new Date();
      const diff = now - date;

      // 小于1分钟
      if (diff < 60 * 1000) {
        return '刚刚';
      }

      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes}分钟前`;
      }

      // 小于1天
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours}小时前`;
      }

      // 小于7天
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days}天前`;
      }

      // 超过7天显示具体日期
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // ========== 页面逻辑方法 ==========

    // 加载基础数据
    async loadBasicData() {
      try {
        const [categoriesData, tagsData] = await Promise.all([
          this.getCategoryList(),
          this.getTagList()
        ]);

        this.groups = this.formatCategories(categoriesData);
        this.tags = this.formatTags(tagsData);
      } catch (error) {
        console.error('加载基础数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      }
    },

    // 加载记忆数据
    async loadMemoryData() {
      if (!this.memoryId) return;

      this.loading = true;
      try {
        const memoryData = await this.getMemoryDetail(this.memoryId);
        if (memoryData) {
          const formattedMemory = this.formatMemory(memoryData);
          this.memoryData = formattedMemory;
          this.formData = {
            content: formattedMemory.content,
            groupId: formattedMemory.groupId
          };

          // 加载标签
          this.selectedTags = formattedMemory.tags || [];

          // 加载图片
          this.imageList = formattedMemory.pics || [];
        } else {
          uni.showToast({
            title: '记忆不存在',
            icon: 'none'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 200);
        }
      } catch (error) {
        console.error('加载记忆数据失败:', error);
        uni.showToast({
          title: '加载记忆失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      return this.formatMemoryTime(timestamp);
    },

    // 分组选择改变
    onGroupChange(e) {
      const index = e.detail.value;
      this.formData.groupId = this.groups[index].id;
    },

    // 添加标签
    addTag(tag) {
      if (!this.selectedTags.find(t => t.id === tag.id)) {
        this.selectedTags.push(tag);
      }
    },

    // 移除标签
    removeTag(tagId) {
      const index = this.selectedTags.findIndex(tag => tag.id === tagId);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
      }
    },

    // 打开标签选择器
    openTagSelector() {
      this.showTagSelector = true;
    },

    // 添加自定义标签
    async addCustomTag() {
      if (!this.customTagName.trim()) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        });
        return;
      }

      // 检查是否已存在
      if (this.tags.some(tag => tag.tagName === this.customTagName.trim())) {
        uni.showToast({
          title: '标签已存在',
          icon: 'none'
        });
        return;
      }

      try {
        // 创建新标签
        const randomColorType = this.tagColorTypes[Math.floor(Math.random() * this.tagColorTypes.length)];
        const newTagData = {
          tagName: this.customTagName.trim(),
          color: randomColorType
        };

        const result = await this.saveTagData(newTagData);
        if (result) {
          // 重新加载标签列表以获取新创建的标签ID
          const tagsData = await this.getTagList();
          this.tags = this.formatTags(tagsData);

          // 找到新创建的标签并添加到已选列表
          const newTag = this.tags.find(tag => tag.tagName === this.customTagName.trim());
          if (newTag) {
            this.addTag(newTag);
          }

          this.customTagName = '';
          uni.showToast({
            title: '标签创建成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '标签创建失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('创建标签失败:', error);
        uni.showToast({
          title: '标签创建失败',
          icon: 'none'
        });
      }
    },

    // 编辑记忆
    editMemory() {
      this.isEditMode = true;
    },

    // 取消编辑
    cancelEdit() {
      if (this.isNewMemory) {
        uni.navigateBack();
      } else {
        this.isEditMode = false;
        this.loadMemoryData(); // 重新加载数据
      }
    },

    // 保存记忆
    async saveMemory() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      try {
        const memoryData = {
          content: this.formData.content,
          groupId: this.formData.groupId,
          images: this.imageList
        };

        if (!this.isNewMemory && this.memoryData.id) {
          memoryData.id = this.memoryData.id;
        }

        const result = await this.saveMemoryData(memoryData);
        if (result) {
          const action = this.isNewMemory ? '创建' : '更新';
          console.log("🚀 -> saveMemory -> action:", action)

          setTimeout(() => {
            if (this.isNewMemory) {
              uni.navigateBack();
            } else {
              this.isEditMode = false;
              this.loadMemoryData();
            }
          }, 300);
        }
      } catch (error) {
        console.error('保存记忆失败:', error);
        const action = this.isNewMemory ? '创建' : '更新';
        uni.showToast({
          title: `${action}失败`,
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 表单验证
    validateForm() {
      if (!this.formData.content.trim()) {
        uni.showToast({
          title: '请输入记忆内容',
          icon: 'none'
        });
        return false;
      }

      if (!this.formData.groupId) {
        uni.showToast({
          title: '请选择分组',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 删除记忆
    async deleteMemory() {
      if(await this.$confirm1('您确定删除这条记忆吗？删除后无法恢复。') == 1){
        this.loading = true;
        try {
          const result = await this.deleteMemoryData(this.memoryData.id);
          if (result) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1500
            });

            setTimeout(() => {
              uni.navigateBack();
            }, 300);
          } else {
            uni.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('删除记忆失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        } finally {
          this.loading = false;
        }
      }
    },

    // 预览图片
    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.edit-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding-bottom: 120rpx; // 为固定按钮留出空间
}

.form-content {
  padding: 20rpx;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(20rpx);

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 24rpx 16rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);

    .card-title {
      font-size: 28rpx;
      font-weight: 700;
      color: #1a202c;
    }

    .required-mark {
      color: #e53e3e;
      font-size: 28rpx;
      font-weight: 600;
    }

    .tag-manage-btn {
      background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
      border: 1rpx solid #667eea30;
      border-radius: 16rpx;
      padding: 8rpx 16rpx;

      .manage-text {
        color: #667eea;
        font-size: 24rpx;
        font-weight: 600;
      }
    }
  }

  // 分组选择器
  .group-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 0, 0, 0.02);
    }

    .group-info {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .group-icon {
        font-size: 32rpx;
      }

      .group-name {
        font-size: 28rpx;
        color: #2d3748;
        font-weight: 500;
      }
    }

    .selector-arrow {
      font-size: 32rpx;
      color: #cbd5e0;
      font-weight: 300;
    }
  }

  // 文本域包装器
  .textarea-wrapper {
    padding: 20rpx 24rpx;
    position: relative;

    .content-textarea {
      width: 100%;
      min-height: 200rpx;
      font-size: 28rpx;
      color: #2d3748;
      line-height: 1.6;
      background: transparent;
      border: none;
      outline: none;
      resize: none;

      &::placeholder {
        color: #a0aec0;
      }
    }

    .char-counter {
      position: absolute;
      bottom: 24rpx;
      right: 24rpx;
      font-size: 20rpx;
      color: #a0aec0;
      background: rgba(255, 255, 255, 0.8);
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
    }
  }

  // 已选标签展示
  .selected-tags-display {
    padding: 20rpx 24rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .selected-tag {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 24rpx;
      font-weight: 600;
      border: 1rpx solid;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .tag-name {
        line-height: 1;
      }

      .tag-remove {
        font-size: 28rpx;
        font-weight: 300;
        opacity: 0.7;
        line-height: 1;
      }
    }
  }

  // 空状态
  .empty-tags {
    padding: 40rpx 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 0, 0, 0.02);
    }

    .empty-icon {
      font-size: 48rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 24rpx;
      color: #a0aec0;
      font-weight: 500;
    }
  }
}

// 固定底部按钮
.fixed-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  padding: 20rpx;
  display: flex;
  gap: 16rpx;
  z-index: 999;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.cancel-btn {
      background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
      border: 1rpx solid rgba(0, 0, 0, 0.1);

      .btn-text {
        color: #4a5568;
        font-size: 30rpx;
      }

      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
      }
    }

    &.save-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);

      .btn-text {
        color: white;
        font-size: 30rpx;
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 12rpx rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 标签选择弹窗
.tag-selector-popup {
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 80vh;
  overflow: hidden;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    .popup-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a202c;
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      background: rgba(0, 0, 0, 0.06);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(0, 0, 0, 0.12);
        transform: scale(0.9);
      }

      .close-icon {
        font-size: 32rpx;
        color: #64748b;
        font-weight: 300;
        line-height: 1;
      }
    }
  }

  .popup-section {
    padding: 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);

    &:last-child {
      border-bottom: none;
    }

    .section-title {
      font-size: 26rpx;
      font-weight: 600;
      color: #4a5568;
      margin-bottom: 16rpx;
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .popup-tag {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 10rpx 16rpx;
        border-radius: 16rpx;
        font-size: 24rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        &.selected {
          .tag-action {
            font-size: 28rpx;
            font-weight: 300;
            opacity: 0.8;
          }
        }

        &.available {
          border: 1rpx solid;

          .tag-action {
            font-size: 24rpx;
            font-weight: 300;
          }
        }

        &:active {
          transform: scale(0.95);
        }

        .tag-text {
          line-height: 1;
        }
      }
    }

    .new-tag-input {
      display: flex;
      align-items: center;
      gap: 8rpx;
      background: rgba(0, 0, 0, 0.02);
      border-radius: 16rpx;
      padding: 8rpx;

      .tag-name-input {
        flex: 1;
        padding: 12rpx 16rpx;
        font-size: 26rpx;
        color: #2d3748;
        background: transparent;
        border: none;
        outline: none;
        min-height: 60rpx;

        &::placeholder {
          color: #a0aec0;
        }
      }

      .add-tag-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12rpx;
        padding: 16rpx 24rpx;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .add-text {
          color: white;
          font-size: 24rpx;
          font-weight: 600;
        }
      }
    }
  }
}

.detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.memory-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(20rpx);

  .memory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    .group-name {
      font-size: 28rpx;
      font-weight: 700;
      color: #667eea;
    }

    .create-time {
      font-size: 24rpx;
      color: #a0aec0;
      font-weight: 500;
    }
  }

  .memory-content {
    font-size: 28rpx;
    color: #2d3748;
    line-height: 1.6;
    padding: 24rpx;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .memory-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    padding: 0 24rpx 24rpx;

    .tag {
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 24rpx;
      font-weight: 600;
      border: 1rpx solid;
    }
  }

  .memory-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12rpx;
    padding: 0 24rpx 24rpx;

    .memory-image {
      width: 100%;
      height: 200rpx;
      border-radius: 12rpx;
      background-color: #f7fafc;
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  z-index: 999;
}
</style>
