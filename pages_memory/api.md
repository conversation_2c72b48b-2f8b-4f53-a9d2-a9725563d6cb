# 记忆碎片模块 API 文档

## 模块概述

记忆碎片模块用于存储用户灵光一闪的想法和创意，支持分类和标签管理，提供强大的搜索和组织功能。

## 数据表结构

- `memory_category`：分类表（包含权重、颜色、图标字段）
- `memory_tag`：标签表（包含颜色字段）
- `memory_data`：记忆碎片数据表（包含图片数组字段）
- `memory_data_tag`：记忆碎片标签关联表

## 接口列表

### 1. 分类管理

#### 1.1 保存分类（新增或编辑）
**接口地址：** `POST /memory/category/save`

**新增请求参数：**
```json
{
  "name": "工作灵感",
  "weight": 100,
  "color": "primary",
  "icon": "💡"
}
```

**编辑请求参数：**
```json
{
  "id": 5,
  "name": "工作创意",
  "weight": 200,
  "color": "success",
  "icon": "🚀"
}
```

**响应示例（新增）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存分类成功",
  "data": 5
}
```

**响应示例（编辑）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存分类成功",
  "data": true
}
```

#### 1.2 删除分类
**接口地址：** `POST /memory/category/delete`

**请求参数：**
```json
{
  "id": 5
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除分类成功"
}
```

#### 1.3 获取分类列表
**接口地址：** `GET /memory/category/list`

**请求参数：** 无

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类列表成功",
  "data": [
    {
      "id": 1,
      "name": "默认分类",
      "weight": 0,
      "color": "primary",
      "icon": "📁",
      "timeCreate": 1704067200
    },
    {
      "id": 2,
      "name": "工作灵感",
      "weight": 100,
      "color": "success",
      "icon": "💡",
      "timeCreate": 1704067300
    }
  ]
}
```

#### 1.4 获取分类详情
**接口地址：** `GET /memory/category/get`

**请求参数：**
```json
{
  "id": 2
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类详情成功",
  "data": {
    "id": 2,
    "name": "工作灵感",
    "weight": 100,
    "color": "success",
    "icon": "💡",
    "timeCreate": 1704067300
  }
}
```

### 2. 标签管理

#### 2.1 保存标签（新增或编辑）
**接口地址：** `POST /memory/tag/save`

**新增请求参数：**
```json
{
  "name": "创新",
  "color": "success"
}
```

**编辑请求参数：**
```json
{
  "id": 10,
  "name": "创新思维",
  "color": "warning"
}
```

**响应示例（新增）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存标签成功",
  "data": 10
}
```

**响应示例（编辑）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存标签成功",
  "data": true
}
```

#### 2.2 删除标签
**接口地址：** `POST /memory/tag/delete`

**请求参数：**
```json
{
  "id": 10
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除标签成功"
}
```

#### 2.3 获取标签列表
**接口地址：** `GET /memory/tag/list`

**请求参数：** 无

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取标签列表成功",
  "data": [
    {
      "id": 1,
      "name": "创新",
      "color": "success",
      "timeCreate": 1704067200
    },
    {
      "id": 2,
      "name": "技术",
      "color": "info",
      "timeCreate": 1704067300
    }
  ]
}
```

#### 2.4 搜索标签
**接口地址：** `GET /memory/tag/search`

**请求参数：**
```json
{
  "keyword": "创"
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "搜索标签成功",
  "data": [
    {
      "id": 1,
      "name": "创新",
      "color": "success",
      "timeCreate": 1704067200
    },
    {
      "id": 3,
      "name": "创意",
      "color": "warning",
      "timeCreate": 1704067400
    }
  ]
}
```

### 3. 记忆碎片管理

#### 3.1 保存记忆碎片（新增或编辑）
**接口地址：** `POST /memory/data/save`

**新增请求参数：**
```json
{
  "title": "AI产品创新想法",
  "content": "基于用户行为数据，开发个性化的AI助手功能...",
  "categoryId": 2,
  "tags": "创新,AI,产品",
  "images": "https://example.com/img1.jpg,https://example.com/img2.png"
}
```

**编辑请求参数：**
```json
{
  "id": 15,
  "title": "AI产品创新想法（更新）",
  "content": "基于用户行为数据，开发个性化的AI助手功能，增加语音交互...",
  "categoryId": 2,
  "tags": "创新,AI,产品,语音",
  "images": "https://example.com/img1.jpg,https://example.com/img3.jpg"
}
```

**响应示例（新增）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存记忆碎片成功",
  "data": 15
}
```

**响应示例（编辑）：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存记忆碎片成功",
  "data": true
}
```

#### 3.2 删除记忆碎片
**接口地址：** `POST /memory/data/delete`

**请求参数：**
```json
{
  "id": 15
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除记忆碎片成功"
}
```

#### 3.3 获取记忆碎片详情
**接口地址：** `GET /memory/data/get`

**请求参数：**
```json
{
  "id": 15
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取记忆碎片详情成功",
  "data": {
    "id": 15,
    "title": "AI产品创新想法",
    "content": "基于用户行为数据，开发个性化的AI助手功能...",
    "images": [
      "https://example.com/img1.jpg",
      "https://example.com/img2.png"
    ],
    "categoryId": 2,
    "categoryName": "工作灵感",
    "categoryColor": "success",
    "categoryIcon": "💡",
    "timeCreate": 1704067500,
    "timeUpdate": 1704067500,
    "tags": [
      {
        "id": 1,
        "name": "创新",
        "color": "success"
      },
      {
        "id": 5,
        "name": "AI",
        "color": "info"
      },
      {
        "id": 8,
        "name": "产品",
        "color": "warning"
      }
    ]
  }
}
```

#### 3.4 获取记忆碎片列表
**接口地址：** `GET /memory/data/list`

**请求参数：**
```json
{
  "page": 1,
  "pageSize": 20,
  "categoryId": 2,
  "tags": "创新,AI",
  "keyword": "AI"
}
```

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "rows": [
    {
      "id": 15,
      "title": "AI产品创新想法",
      "content": "基于用户行为数据，开发个性化的AI助手功能...",
      "images": [
        "https://example.com/img1.jpg"
      ],
      "categoryId": 2,
      "categoryName": "工作灵感",
      "categoryColor": "success",
      "categoryIcon": "💡",
      "timeCreate": 1704067500,
      "timeUpdate": 1704067500,
      "tags": [
        {
          "id": 1,
          "name": "创新",
          "color": "success"
        },
        {
          "id": 5,
          "name": "AI",
          "color": "info"
        }
      ]
    }
  ],
  "total": 1
}
```

### 4. 统计信息

#### 4.1 获取统计信息
**接口地址：** `GET /memory/statistics`

**请求参数：** 无

**响应示例：**
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取统计信息成功",
  "data": {
    "categoryCount": 5,
    "tagCount": 12,
    "dataCount": 25
  }
}
```

## 字段说明

### 分类字段
- `weight`: 权重排序，数值越大越靠前，默认0
- `color`: 分类颜色类型，支持：`primary`、`success`、`warning`、`danger`、`info`，默认`primary`
- `icon`: 分类图标，使用emoji表情，默认`📁`

### 标签字段
- `color`: 标签颜色类型，支持：`primary`、`success`、`warning`、`danger`、`info`，默认`success`

### 记忆数据字段
- `images`: 图片数组，存储图片URL的字符串数组

## 颜色类型说明

系统支持以下固定颜色类型：
- `primary`: 主色调（蓝色系）
- `success`: 成功色（绿色系）
- `warning`: 警告色（橙色系）
- `danger`: 危险色（红色系）
- `info`: 信息色（灰蓝色系）

## 特性说明

### 1. 统一鉴权框架
- 所有接口均使用统一的`base.InitBaseCtx(r)`鉴权框架
- 无需手动传入`user_id`参数，系统自动获取当前登录用户信息
- 自动处理分页参数`page`和`pageSize`

### 2. 接口简化设计
- 新增和编辑操作合并为单个`save`接口
- 通过是否传入`id`参数来区分新增（无id）和编辑（有id）操作
- 减少接口数量，提高开发效率

### 3. 参数命名规范
- 前端参数统一使用小驼峰命名：`categoryId`、`tags`、`pageSize`
- 标签参数传入标签名称字符串，系统自动转换为对应ID进行查询
- 响应数据字段自动转换为小驼峰格式（通过ToSnake转换）

### 4. 自动分类管理
- 用户首次添加记忆碎片时，如果没有指定分类，系统会自动创建"默认分类"
- 分类名称不能重复（基于用户维度）
- 分类按权重值排序，权重值相同时按ID倒序

### 5. 智能标签系统
- 标签支持自动创建，用户输入不存在的标签时会自动创建
- 标签名称不能重复（基于用户维度）
- 支持标签搜索功能
- 新建标签时自动设置默认颜色类型

### 6. 图片管理
- 支持多图片上传，通过OSS完成图片存储
- 前端传入图片URL字符串数组，以逗号分隔
- 系统自动处理图片数组的存储和查询

### 7. 性能优化
- 查询时根据是否传入分类ID进行优化：
  - 有分类ID：不进行JOIN操作，后续补充分类名称
  - 无分类ID：LEFT JOIN分类表获取分类名称
- 批量获取标签信息，减少数据库查询次数

### 8. 数据安全
- 所有操作都基于用户ID进行权限控制
- 支持软删除，数据不会真正丢失
- 输入数据会进行trim处理，防止空格干扰

### 9. 搜索功能
- 支持按分类筛选
- 支持按标签筛选（可多选）
- 支持关键词搜索（标题和内容）
- 支持分页查询

## 错误码说明

- `code: 200` - 操作成功
- `code: -1` - 参数错误
- `code: -2` - 数据不存在

## 注意事项

1. 所有接口均使用统一鉴权，无需传入`user_id`参数
2. 保存接口通过`id`参数区分新增和编辑操作
3. 标签参数使用逗号分隔的标签名称字符串格式（如："创新,AI,产品"）
4. 图片参数使用逗号分隔的图片URL字符串格式
5. 颜色字段使用固定类型：primary/success/warning/danger/info等
6. 图标字段使用emoji表情符号
7. 标签查询时传入标签名称，系统自动转换为对应ID进行查询
8. 时间字段使用10位时间戳格式
9. 响应数据字段名使用小驼峰格式（通过ToSnake转换）
10. 分类和标签的名称会自动去除首尾空格
11. 分页参数在列表接口中自动处理，无需手动传入（可选传入覆盖默认值）
12. 分类列表按权重值降序排列，权重值相同时按ID降序排列