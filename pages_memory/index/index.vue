<!--
 * @Description: 记忆首页
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-15 20:30:43
-->

<template>
  <view class="page-container">
    <view class="content-container">
      <!-- 分组管理区域 -->
      <view class="groups-section">
        <view class="section-header">
          <text class="section-title">我的分组</text>
          <view class="manage-btn" @click="goToGroupManage">
            <text class="manage-btn-icon">⚙</text>
            <text class="manage-btn-text">管理分组</text>
          </view>
        </view>

        <view class="group-grid">
          <view
            v-for="group in groups"
            :key="group.id"
            class="group-card"
            @click="goToGroupList(group)"
          >
            <view class="group-icon-wrapper">
              <text class="group-icon">{{ group.icon }}</text>
            </view>
            <view class="group-info">
              <text class="group-name">{{ group.groupName }}</text>
              <text class="group-count">{{ group.dataCount || 0 }}条记忆</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 最新记忆 -->
      <view class="memories-section">
        <view class="section-header">
          <text class="section-title">最新记忆</text>
          <text class="section-subtitle">最近记录</text>
        </view>

        <view class="memory-list">
          <view
            v-for="(memory, index) in recentMemories"
            :key="memory.id"
            class="memory-card"
            @click="goToMemoryDetail(memory)"
          >
            <view class="memory-left">
              <view class="memory-index">{{ index + 1 }}</view>
            </view>
            <view class="memory-main">
              <view class="memory-header">
                <view class="memory-group-badge">{{ memory.categoryName }}</view>
                <text class="memory-time">{{ formatTime(memory.timeCreate) }}</text>
              </view>
              <view class="memory-content">{{ memory.content }}</view>
              <view class="memory-tags" v-if="memory.tags.length > 0">
                <view
                  v-for="tag in memory.tags"
                  :key="tag.id"
                  class="memory-tag"
                  :style="{ backgroundColor: tag.color + '20', color: tag.color, borderColor: tag.color + '40' }"
                >
                  {{ tag.tagName }}
                </view>
              </view>
            </view>
            <view class="memory-arrow">
              <text class="arrow-icon">›</text>
            </view>
          </view>
        </view>

        <view v-if="recentMemories.length === 0" class="empty-state">
          <view class="empty-icon">✨</view>
          <text class="empty-text">还没有记忆</text>
          <text class="empty-desc">点击右下角按钮开始记录</text>
        </view>
      </view>
    </view>

    <!-- 创建按钮 -->
    <view class="fab" @click="createMemory">
      <text class="fab-icon">+</text>
    </view>

    <!-- 菜单弹窗 -->
    <u-popup :show="showMenu" mode="bottom" @close="showMenu = false" duration="200">
      <view class="menu-popup" v-if="showMenu">
        <view class="menu-header">
          <text class="menu-title">菜单</text>
        </view>
        <view class="menu-list">
          <view class="menu-item" @click="goToGroupManage">
            <view class="menu-item-icon">📁</view>
            <text class="menu-item-text e1">分类管理</text>
            <view class="menu-item-arrow">›</view>
          </view>
          <view class="menu-item" @click="goToTagManage">
            <view class="menu-item-icon">🏷️</view>
            <text class="menu-item-text e1">标签管理</text>
            <view class="menu-item-arrow">›</view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { colorUtils } from '../data/colors.js';
import { iconUtils } from '../data/icons.js';

export default {
  name: 'MemoryIndex',
  data() {
    return {
      groups: [],
      recentMemories: [],
      tags: [],
      showMenu: false,
      loading: true,
      groupMemoryCount: {}
    };
  },

  onLoad() {
    // 只进行初始化赋值
  },

  onShow() {
    this.loadData();
  },
  onNavigationBarButtonTap(e) {
    if (e.index === 0) {
      this.showMenu = true;
    }
  },

  methods: {
    // ========== API调用方法 ==========

    // 获取分类列表
    async getCategoryList() {
      try {
        const res = await this.$ajax.get('/memory/category/list?withCount=1');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取分类列表失败:', error);
        return [];
      }
    },

    // 获取标签列表
    async getTagList() {
      try {
        const res = await this.$ajax.get('/memory/tag/list');
        return res?.code === 200 ? res.data || [] : [];
      } catch (error) {
        console.error('获取标签列表失败:', error);
        return [];
      }
    },

    // 获取记忆列表
    async getMemoryList(params = {}) {
      try {
        const requestParams = {
          page: params.page || 1,
          pageSize: params.pageSize || 20
        };

        if (params.categoryId) {
          requestParams.categoryId = params.categoryId;
        }

        if (params.tags) {
          requestParams.tags = params.tags;
        }

        if (params.keyword) {
          requestParams.keyword = params.keyword;
        }

        const res = await this.$ajax.get('/memory/data/list', requestParams);
        return {
          list: res?.rows || [],
          total: res?.total || 0
        };
      } catch (error) {
        console.error('获取记忆列表失败:', error);
        return { list: [], total: 0 };
      }
    },

    // ========== 数据格式化方法 ==========

    // 格式化分类数据
    formatCategories(categories) {
      return categories.map((category, index) => ({
        id: category.id,
        groupName: category.categoryName || category.name,
        dataCount: category.dataCount || 0,
        weight: category.weight || 0,
        color: category.color || 'primary',
        colorValue: this.getColorValue(category.color),
        icon: iconUtils.getValidIcon(category.icon) || this.getRandomIcon(index),
        timeCreate: category.timeCreate || Date.now() / 1000
      }));
    },

    // 格式化标签数据
    formatTags(tags) {
      return tags.map(tag => ({
        id: tag.id,
        tagName: tag.tagName || tag.name,
        color: this.getColorValue(tag.color || 'success'),
        colorType: tag.color || 'success',
        timeCreate: tag.timeCreate || Date.now() / 1000
      }));
    },

    // 格式化记忆数据
    formatMemories(memories) {
      return memories.map(memory => ({
        id: memory.id,
        content: memory.content,
        title: memory.title || '',
        pics: this.parseImages(memory.images),
        groupId: memory.categoryId,
        categoryName: memory.categoryName || '',
        categoryColor: memory.categoryColor || 'primary',
        categoryIcon: memory.categoryIcon || '📁',
        timeCreate: memory.timeCreate,
        timeUpdate: memory.timeUpdate,
        tags: Array.isArray(memory.tags) ? memory.tags.map(tag => ({
          id: tag.id,
          tagName: tag.tagName || tag.name,
          color: this.getColorValue(tag.color || 'success'),
          colorType: tag.color || 'success'
        })) : []
      }));
    },

    // ========== 工具方法 ==========

    // 获取颜色值
    getColorValue(colorType) {
      return colorUtils.getTagColor(colorType);
    },

    // 获取随机图标
    getRandomIcon(index = 0) {
      return iconUtils.getRandomIcon(index);
    },

    // 处理图片数组
    parseImages(images) {
      if (!images) return [];
      if (Array.isArray(images)) return images;
      if (typeof images === 'string') {
        try {
          // 尝试解析JSON格式
          return JSON.parse(images);
        } catch {
          // 如果不是JSON，按逗号分割
          return images.split(',').filter(img => img.trim());
        }
      }
      return [];
    },

    // 格式化时间
    formatMemoryTime(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp * 1000);
      const now = new Date();
      const diff = now - date;

      // 小于1分钟
      if (diff < 60 * 1000) {
        return '刚刚';
      }

      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes}分钟前`;
      }

      // 小于1天
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours}小时前`;
      }

      // 小于7天
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days}天前`;
      }

      // 超过7天显示具体日期
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // ========== 页面逻辑方法 ==========

    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 并行加载所有数据
        const [categoriesData, memoryResult, tagsData] = await Promise.all([
          this.getCategoryList(),
          this.getMemoryList({ page: 1, pageSize: 5 }),
          this.getTagList()
        ]);

        this.groups = this.formatCategories(categoriesData).sort((a, b) => b.weight - a.weight);
        this.recentMemories = this.formatMemories(memoryResult.list);
        this.tags = this.formatTags(tagsData);


      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    // 格式化时间
    formatTime(timestamp) {
      return this.formatMemoryTime(timestamp);
    },

    // 跳转到标签管理
    goToTagManage() {
      this.showMenu = false;
      uni.navigateTo({
        url: '/pages_memory/tag-manage/index'
      });
    },

    // 跳转到分组列表
    goToGroupList(group) {
      uni.navigateTo({
        url: `/pages_memory/list/index?groupId=${group.id}&groupName=${encodeURIComponent(group.groupName)}`
      });
    },

    // 跳转到记忆详情
    goToMemoryDetail(memory) {
      uni.navigateTo({
        url: `/pages_memory/detail/index?id=${memory.id}`
      });
    },

    // 跳转到分组管理
    goToGroupManage() {
      this.showMenu = false;
      uni.navigateTo({
        url: '/pages_memory/group-manage/index'
      });
    },

    // 创建记忆
    createMemory() {
      uni.navigateTo({
        url: '/pages_memory/detail/index'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

// 分组卡片渐变色变量
$group-gradients: (
  1: (
    from: rgba(102, 126, 234, 0.15),
    to: rgba(118, 75, 162, 0.12)
  ),
  2: (
    from: rgba(255, 107, 107, 0.15),
    to: rgba(255, 142, 83, 0.12)
  ),
  3: (
    from: rgba(72, 187, 120, 0.15),
    to: rgba(56, 178, 172, 0.12)
  ),
  4: (
    from: rgba(129, 140, 248, 0.15),
    to: rgba(139, 92, 246, 0.12)
  )
);

// 分组区域样式
.groups-section {
  margin-bottom: 40rpx;
  padding: 0 $spacing-md;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding: 0 8rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a1a1a;
      letter-spacing: 0.5rpx;
    }

    .manage-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 12rpx 20rpx;
      border-radius: 24rpx;
      box-shadow: 0 6rpx 16rpx rgba(102, 126, 234, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 8rpx;
      position: relative;
      overflow: hidden;

      // 光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:active {
        transform: translateY(2rpx) scale(0.96);
        box-shadow: 0 3rpx 12rpx rgba(102, 126, 234, 0.4);

        &::before {
          left: 100%;
        }
      }

      .manage-btn-icon {
        font-size: 26rpx;
        line-height: 1;
        color: white;
        font-weight: 400;
      }

      .manage-btn-text {
        color: white;
        font-size: 26rpx;
        font-weight: 600;
        line-height: 1;
        letter-spacing: 0.5rpx;
      }
    }
  }
}

.group-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 0 8rpx;

  .group-card {
    flex: 0 0 calc(50% - 6rpx);
    border-radius: 18rpx;
    padding: 20rpx;
    box-shadow: 0 3rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20rpx);
    display: flex;
    align-items: center;
    gap: 16rpx;

    // 使用变量定义不同卡片的渐变背景
    &:nth-child(4n+1) {
      background: linear-gradient(135deg,
        map-get(map-get($group-gradients, 1), from) 0%,
        map-get(map-get($group-gradients, 1), to) 100%);
    }

    &:nth-child(4n+2) {
      background: linear-gradient(135deg,
        map-get(map-get($group-gradients, 2), from) 0%,
        map-get(map-get($group-gradients, 2), to) 100%);
    }

    &:nth-child(4n+3) {
      background: linear-gradient(135deg,
        map-get(map-get($group-gradients, 3), from) 0%,
        map-get(map-get($group-gradients, 3), to) 100%);
    }

    &:nth-child(4n) {
      background: linear-gradient(135deg,
        map-get(map-get($group-gradients, 4), from) 0%,
        map-get(map-get($group-gradients, 4), to) 100%);
    }

    // 悬停效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.3);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:active {
      transform: translateY(1rpx) scale(0.98);
      box-shadow: 0 1rpx 12rpx rgba(0, 0, 0, 0.12);

      &::before {
        opacity: 1;
      }
    }

    .group-icon-wrapper {
      width: 56rpx;
      height: 56rpx;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.12);
      border: 1rpx solid rgba(255, 255, 255, 0.9);
      flex-shrink: 0;
      position: relative;
      z-index: 1;

      .group-icon {
        font-size: 30rpx;
        line-height: 1;
      }
    }

    .group-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2rpx;
      position: relative;
      z-index: 1;

      .group-name {
        font-size: 26rpx;
        font-weight: 700;
        color: #1a202c;
        line-height: 1.2;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .group-count {
        font-size: 20rpx;
        color: #64748b;
        font-weight: 500;
        line-height: 1;
      }
    }
  }
}

// 记忆区域样式
.memories-section {
  padding: 0 $spacing-md;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding: 0 8rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a1a1a;
      letter-spacing: 0.5rpx;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #718096;
      font-weight: 500;
    }
  }
}

.memory-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;

  .memory-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: flex-start;
    gap: 16rpx;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20rpx);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:active {
      transform: translateY(1rpx) scale(0.995);
      box-shadow: 0 1rpx 12rpx rgba(0, 0, 0, 0.08);

      &::before {
        opacity: 1;
      }
    }

    .memory-left {
      flex-shrink: 0;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 4rpx;

      .memory-index {
        width: 32rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-weight: 600;
        color: #4a5568;
        border: 1rpx solid rgba(0, 0, 0, 0.08);
      }
    }

    .memory-main {
      flex: 1;
      min-width: 0;

      .memory-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8rpx;

        .memory-group-badge {
          background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
          color: #667eea;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          font-weight: 600;
          border: 1rpx solid #667eea20;
        }

        .memory-time {
          font-size: 20rpx;
          color: #a0aec0;
          font-weight: 500;
        }
      }

      .memory-content {
        font-size: 26rpx;
        color: #2d3748;
        line-height: 1.6;
        margin-bottom: 12rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        word-break: break-word;
      }

      .memory-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .memory-tag {
          font-size: 18rpx;
          padding: 4rpx 10rpx;
          border-radius: 10rpx;
          font-weight: 500;
          border: 1rpx solid;
          line-height: 1.2;
        }
      }
    }

    .memory-arrow {
      flex-shrink: 0;
      width: 24rpx;
      height: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8rpx;

      .arrow-icon {
        font-size: 32rpx;
        color: #cbd5e0;
        font-weight: 300;
        line-height: 1;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  margin-top: 40rpx;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    display: block;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #4a5568;
    margin-bottom: 8rpx;
    display: block;
    font-weight: 600;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #a0aec0;
    line-height: 1.4;
  }
}

.fab {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  z-index: 999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
  }

  .fab-icon {
    font-size: 64rpx;
    color: white;
    font-weight: 300;
    line-height: 1;
  }
}

// 菜单弹窗样式
.menu-popup {
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24rpx;
  overflow: hidden;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .menu-header {
    padding: 24rpx;
    text-align: center;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

    .menu-title {
      font-size: 30rpx;
      font-weight: 700;
      color: #1a202c;
    }
  }

  .menu-list {
    padding: 12rpx 0;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 20rpx 32rpx;
      transition: all 0.3s ease;
      position: relative;

      &:active {
        background: linear-gradient(135deg, #667eea08 0%, #764ba208 100%);
      }

      .menu-item-icon {
        font-size: 32rpx;
        margin-right: 20rpx;
        width: 40rpx;
        text-align: center;
      }

      .menu-item-text {
        flex: 1;
        font-size: 28rpx;
        color: #2d3748;
        font-weight: 600;
      }

      .menu-item-arrow {
        font-size: 36rpx;
        color: #cbd5e0;
        font-weight: 300;
        line-height: 1;
      }
    }
  }
}
</style>
