参考网址： https://ext.dcloud.net.cn/plugin?id=4317

传输加密，AES对称加密

使用说明
1.main.js引入js文件注册.当然也可以哪里需要哪里引入
```
import AES from '@/js_sdk/ar-aes/ar-aes.js'
Vue.prototype.AES = AES.AES
```
2.使用示例
```
//加密值
let e = this.AES.encrypt('123','1234567891234567','1234567891234567')

//解密值
this.AES.decrypt(e,'1234567891234567','1234567891234567')
```


方法名	参数	类型	返回值	说明
encrypt	参数1 string	string		加密字符串
参数2 key	string		加密密码KEY,16位
参数3 iv	string		偏移量IV,16位
string	返回加密后的字符串	返回值加密后的
decrypt	参数1 string	string		解密字符串
参数2 key	string		加密密码KEY,16位
参数3 iv	string		偏移量IV,16位
string 或 ''	返回解密后的字符串，解密失败返回空字符串	返回值