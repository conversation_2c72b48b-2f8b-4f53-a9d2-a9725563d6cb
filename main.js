/*
 * @Description:
 * @Author: 徐静(<EMAIL>)
 * @Date: 2021-07-27 07:22:16
 * @LastEditTime: 2025-07-31 20:03:24
 * @Copyright: (c) 2011-2021 http://www.winksoft.net All rights reserved.
 */
import App from './App'

// 考试

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'

// 引入uView
import uView from '@/uni_modules/uview-ui'
// uni.$u.config.unit = 'rpx'
import './rhecs'


//
Vue.use(uView)


const app = new Vue({
	...App
})
app.$mount()
export default app
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import uView from "@/uni_modules/uview-ui";
Vue.use(uView);
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif

// #ifdef H5
// 提交前需要注释  本地调试使用
const vconsole = require('vconsole')
Vue.prototype.$vconsole = new vconsole() // 使用vconsole

// #endif
