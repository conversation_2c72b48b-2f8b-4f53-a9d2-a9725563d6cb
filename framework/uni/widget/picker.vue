<!-- // 选择器  选择专业-->
<!--

<div> <easy-picker :show="openExpect" v-model="expectCurrent" :list="expectLict" @close="openExpect = false"></easy-picker> </div> 

<div> <my-picker :show="openExpect" v-model="expectCurrent" :list="expectLict" @close="openExpect = false"></my-picker> </div> -->

<template>
	<view>
		<div class="">
			<u-picker :show="openPicker" :columns="checkList" :defaultIndex="defaultIndex" @cancel="cancel"
				@confirm="confirm" @change="change"></u-picker>
		</div>
	</view>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: false,
			},
			list: {
				type: Array,
				default: () => [],
			},
			value: {
				type: String | Number | Object,
				default: "",
			},
			close: {
				type: Function,
			}
		},
		watch: {
			show(n, o) {
				this.openPicker = n;
			},
			list(n, o) {
				this.initData();
			},
			value(n, o) {
				this.checkCurrent = n;
			}
		},
		mounted() {
			this.init();
		},
		methods: {
			init() {
				// 获取基本列表
				this.initData();
				// 关闭开启
				this.openPicker = this.show;
			},
			// 初始化数据
			initData() {
				this.listNames = [];
				this.listValues = [];
				this.list.map((e, index) => {
					if (this.$ecs.isMap(e)) {
						this.isMap = true;
						this.listNames.push(e.label)
						this.listValues.push(e.value)
					} else {
						this.isMap = false;
						this.listNames.push(e)
						this.listValues.push(e)
					}

					// 设置 默认值
					if (this.checkCurrent != undefined && this.checkCurrent != '' && this.checkCurrent != {}) {
						if (this.isMap) {
							if (this.checkCurrent == e.value) this.defaultIndex = [index];
						} else {
							if (this.checkCurrent == e) this.defaultIndex = [index];
						}
					}

				})
				this.checkList = [this.listNames];
				this.$forceUpdate()
			},
			cancel() {
				this.closePicker();
			},
			// 选择项目
			confirm(e) {
				console.log(e);
				let _index = e.indexs[0]; //
				if (_index > -1) {
					console.log(this.list[_index]);
					this.checkCurrent = this.list[_index];
					this.$emit("input", this.isMap ? this.list[_index].value : this.list[_index]);
					this.$emit("confirm", this.list[_index]);
				}
				this.closePicker();
			},
			change(e) {
				//


			},
			//
			closePicker() {
				this.openPicker = false;
				this.$emit("close");
				// this.$emit('update:show',false)
				// this.$parent.show= false;
			}
		},
		data() {
			return {
				openPicker: false,
				listNames: [],
				listValues: [],
				checkList: [],
				isMap: false,
				checkCurrent: {},
				defaultIndex: [0],
			};
		}
	}
</script>

<style lang="scss">

</style>
