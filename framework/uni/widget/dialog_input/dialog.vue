<!--
 * @Description:  命令式的编辑框 这个只在uni下生效
 * @Author: 徐静(parkhansung)
 * @Date: 2023-09-01 09:15:56
 * @LastEditTime: 2023-10-10 15:14:03
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
-->
<template>
	<transition name="fade">
		<div v-show="showDialog" class="modal" @click.self="maskClick">
			<div class="dialog modal-content" style="width:80vw">
				<div class="f-32 weight" style="font-size:16px">{{ title }}</div>
				<div class="" style="margin:30px 0 ">
					<input
						v-focus
						v-if="showDialog"
						ref="getFocus"
						type="text"
						v-model="inputValue"
						:placeholder="placeholder"
						class="f-30 input-style w100"
						style="font-size:14px"
					/>
				</div>
				<div class="flex-row-center">
					<div class="h-1 expanded"></div>
					<div @click="closeDialog" style="font-size:14px;margin-right:15px" class="bg-water f-30 c777 pv-10">关闭</div>
					<div @click="confirm" style="font-size:14px" class="weight-6 bg-water f-30 text-primary pl-10 pv-10">确认</div>
				</div>
			</div>
		</div>
	</transition>
</template>

<script>
export default {
	props: ["value", "allowModelClose", "title", "placeholder"],
	data() {
		return {
			showDialog: false,
			inputValue: this.value,
		};
	},
	methods: {
		closeDialog() {
			this.$emit("close", this.inputValue);
		},
		confirm() {
			this.$emit("confirm", this.inputValue);
			this.closeDialog();
		},
		maskClick() {
			if (this.allowModelClose) {
				this.closeDialog();
			}
		},
	},
	mounted() {
		this.showDialog = true;
	},
    // 注册一个局部的自定义指令 v-focus
	directives: {
		focus: {
			// 指令的定义
			inserted: function (el) {
				el.querySelector("input").focus();
			},
		},
	},
};
</script>

<style lang="scss" scoped>
.modal {
	position: fixed;
	z-index: 9999;
	left: 0;
	top: 0;
	width: 100%;
	min-width: 300px;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.4);
	display: flex;
	align-items: center;
	justify-content: center;
}

.input-style {
	border: none;
	border-bottom: 1px solid #ccc;
	transition: border-color 0.3s ease;
}

.input-style:focus {
	border-color: #888;
	outline: none;
}

.modal-content {
	background-color: #fefefe;
	padding: 20px;
	border-radius: 5px;
	position: relative;
}

.close {
	color: #aaa;
	float: right;
	font-size: 28px;
	font-weight: bold;
	position: absolute;
	right: 10px;
	top: 0;
}

.close:hover,
.close:focus {
	color: black;
	text-decoration: none;
	cursor: pointer;
}

.modal-actions {
	margin-top: 20px;
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 400ms;
}

.fade-enter,
.fade-leave-to {
	opacity: 0;
}
</style>