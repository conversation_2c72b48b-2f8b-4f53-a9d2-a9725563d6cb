/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2023-09-01 09:22:59
 * @LastEditTime: 2023-10-10 15:13:39
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
import Vue from 'vue';
import Dialog from './dialog.vue';
import ecs from '../../../script/ecs'
/*
    //
    var _b = await uniDialogFields("张三");
    //
    var _b = await uniDialogFields({
    value: "张三",
    allowModelClose: true,
    title: "编辑姓名",
    placeholder: "请输入姓名"
    });
*/

export default function uniDialogFields(...a) {
  let options = ecs.args(a, {
    value: '',
    title: {
        type:"string",
        value: "提示",
        items:1,
    },
});

  return new Promise((resolve) => {
    const DialogComponent = Vue.extend(Dialog);

    const instance = new DialogComponent({
      el: document.createElement('div'),
      propsData: {
        value: options.value || '',
        allowModelClose: options.allowModelClose || false,
        title: options.title || '编辑',
        placeholder: options.placeholder || '请输入内容'
      }
    });
    instance.$on('close', () => {
        instance.showDialog = false;
        instance.$destroy();
        document.body.removeChild(instance.$el);
      });

    instance.$on('confirm', (v) => {
        resolve(v);
        instance.$emit('close');
    });

      document.body.appendChild(instance.$el);
      setTimeout(() => {
        instance.showDialog = true;
      }, 0);
  });
}
