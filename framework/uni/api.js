// import { getConfig } from "framework/config/config.js";
/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-18 16:15:32
 * @LastEditTime: 2025-07-19 17:25:36
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */

import ecs from "./main.js"
import md5 from 'js-md5';
import { Config, getConfig } from '../config/config'; // 配置文件
import _uto from './router.js'
import _toast from '../widget/util/toast/toast.js'
import UniDialog from "./dialog.js";

import JSONbig from 'json-bigint';


const _safeAuth = () => {
	// const substitution = {
	// 	'M': '@', 'R': '#', 'G': '$', 'J': '%', '-': '&', '2': '!', '0': '*', '4': '^', '9': '(', '1': ')'
	// };
	// const encodeKey = (key) => {
	// 	return key.split('').map(char => substitution[char] || char).join('');
	// }
	let _key = "@#$%&!*!^*(!*";
	// // const _key = "MRGJ-20240920";
	// _key = encodeKey(_key);
	//
	_key = "KQYY-20240920";
	const timestamp = Date.now(); // 获取当前毫秒时间戳
	const randomNum = Math.floor(Math.random() * (99999 - 10000 + 1)) + 10000; // 生成一个随机数
	const _auth = md5(`${timestamp}${randomNum}${_key}`); // 使用时间戳、随机数和密钥生成 MD5 签名

	return `${timestamp}_${_auth}_${randomNum}`; // 返回格式：毫秒_签名_随机数
}

const userOrigin = () => {
	let _timer = ecs.timestamp()
	const _timeTemp = parseInt(`${_timer / 1000}`)
	const _timerRest = `${_timer}`.split('').reverse().join('')
	return `web.1${_timerRest}.${md5(`winksoft-${_timeTemp * 4}`)}`

}

const BaseUrl = getConfig().baseURL

// 修复大整数精度丢失的工具函数
const fixBigIntegerFields = (data, fields = ['userId', 'cid', 'id', 'parentId', 'roleId', 'deptId', 'menuId']) => {
	if (!data || typeof data !== 'object') return data

	// 如果是数组，递归处理每个元素
	if (Array.isArray(data)) {
		return data.map(item => fixBigIntegerFields(item, fields))
	}

	// 处理对象
	const result = { ...data }
	fields.forEach(field => {
		if (result[field] && typeof result[field] === 'number') {
			result[field] = result[field].toString()
		}
	})

	// 递归处理嵌套对象
	Object.keys(result).forEach(key => {
		if (result[key] && typeof result[key] === 'object') {
			result[key] = fixBigIntegerFields(result[key], fields)
		}
	})

	return result
}

// 封装请求
let api = {
	// 获取token的name
	getTokenName() {
		let _tokenName = getConfig().tokenName || "Authorization"
		return _tokenName;
	},
	// 检查是否有中文的数据
	checkChinses(v) {
		if (!v || v == '') return false
		return /[\u4e00-\u9fa5]/.test(v || "")
	},
	// 封装Get
	async get(url, data, load, timeout) {
		if (ecs.isNull(url)) return; // lock
		let loading = load != undefined ? load : true
		if (loading) ecs.loading()
		let _url = this.buildUrl(url);

		let _timeout = timeout || getConfig().apiTimeout || 16000
		// 获取
		// alert(_url);
		return await new Promise((r, er) => {
			uni.request({
				url: _url,
				data: data,
				header: this.header(),
				method: "GET",
				timeout: _timeout, // 延长超时机制  timeout || 10000 => timeout ?  timeout : 10000
			}).then(response => {
				// console.log("🚀 -> returnawaitnewPromise -> response:", response)
				ecs.loading(1) // 关闭loading
				let [error, res] = response;
				if (res?.statusCode == 200) {
					this.checkApiLogin(res?.data) // 掉出登录检测
					const fixedData = fixBigIntegerFields(res?.data) // 修复大整数精度丢失
					r(fixedData); // 返回数据
				} else if (res?.statusCode == 403) {
					UniDialog.toast("您无权访问本页面")
					r(res?.data)
				} else {
					UniDialog.toast("服务异常，请稍后再试")
					r(res?.data)
				}
				// r(_data); // 返回数据
			}).catch(error => {
				ecs.loading(1) // 关闭loading
				let [err, res] = error;
				UniDialog.toast( "请求异常，请稍后再试")
				er(err) // 返回结果
			})

		});
	},
	// 封装post
	async post(url, data, load, timeout) {
		if (ecs.isNull(url)) return; // lock
		let loading = load != undefined ? load : true
		if (loading) ecs.loading()
		let _url = this.buildUrl(url);
		// alert(_url);
		let _timeout = timeout || getConfig().apiTimeout || 16000

		return await new Promise((r, er) => {
			uni.request({
				url: _url,
				data: data,
				header: this.header(),
				method: "POST",
				timeout: _timeout, // 延长超时机制
			}).then(response => {
				// console.log(response);
				ecs.loading(1) // 关闭loading
				let [error, res] = response;
				// 给张源的接口做的特殊处理
				if (res.statusCode == 200) {
					let _data = res?.data;
					this.checkApiLogin(_data) // 掉出登录检测
					const fixedData = fixBigIntegerFields(_data) // 修复大整数精度丢失
					r(fixedData); // 返回数据
				} else if (res.statusCode == 403) {
					UniDialog.toast("您无权访问本页面")
					r(res?.data)
				} else {
					UniDialog.toast("服务异常，请稍后再试")
					r(res?.data)
				}
			}).catch(error => {
				ecs.loading(1) // 关闭loading
				// console.log("🚀 ->  -> error:", error)
				let [err, res] = error;
				UniDialog.toast("网络异常，请稍后再试")
				er(err) // 返回结果
			})
		});
	},
	// 封装header的挂载
	header() {
		let timer = Math.round(new Date() / 1000)
		const _need = getConfig().wkAjaxHeader || false;
		if (_need) {
			return {
				// "rh-user-token": ecs.localStrorage("userToken"), // token
				"Authorization": getConfig().headerTokenPrefix + ecs.localStrorage(this.getTokenName()), // ruoyi的header
				"wk-time": timer, // 当前时间戳
				// "rh-device-id": plus.device.uuid, // 设备id
				"wk-device-id": "", // 设备id
				"wk-device-code": this.deviceCode(timer), // 标识码
				"device": "",
				"language": "cn", // 当前语言
				// "Cookie":"" //
			}
		} else {
			// 返回值
			const _data = {}
			_data[getConfig().headerTokenName] = getConfig().headerTokenPrefix + ecs.localStrorage(this.getTokenName())
			// _data[getConfig().authKey] = _safeAuth1()
			_data[getConfig().authKey] = userOrigin()
			return _data
		}
	},
	// 构建请求地址
	buildUrl(url) {
		if (ecs.isNull(url)) return;
		// 检查是否包含http
		if (url.substr(0, 4) == "http") {
			return url;
		} else {
			if (BaseUrl.substr(-1) == "/" || url.substr(0, 1) == "/") {
				return BaseUrl + url;
			}
			return BaseUrl + "/" + url;
		}
	},
	// 检查是否掉出登录
	checkApiError(response) {
		if (response?.statusCode == 502) {

		}
		return;
	},
	// 检查是否掉出登录
	checkApiLogin(data) {
		// console.log("🚀 -> checkApiLogin -> data:", data)
		if (data?.code == getConfig().apiErrorNeedLoginCode) {
			const _pages = getCurrentPages();
			if (getConfig().uniGoIndex === true) {
				_uto.setIndexTab("/pages/index/index")  // 禁止死循环
			}
			// 调用方法
			// getConfig().toLogin() // 调用配置文件的登录方法
			const _loginUrl = getConfig().uniLoginUrl;
			console.log("🚀 -> checkApiLogin -> _loginUrl:", _loginUrl)
			_uto.to(_loginUrl)  // 跳转登录
			// 无权访问 执行后退
		} else if (data?.code == 403) {
			UniDialog.toast(data?.msg || data?.message || "您无权访问本页面")
		} else if (data?.code == 503) {
			UniDialog.toast(data?.msg || data?.message || "票务系统异常")
		} else if (data?.code == 412) {
			UniDialog.toast(data?.msg || data?.message || "请求有误，请重新尝试")
		} else if (data?.code == 509) {
			UniDialog.toast(data?.msg || data?.message || "当前请求人数过多，请稍后再试") // 熔断操作
		}


	},
	// 检查登录Token是否存在
	checkToken() {
		let token = ecs.localStrorage(this.getTokenName())
		if (ecs.isNull(token)) {
			console.log("Token不存在...")
		}
	},
	// 登录保存登录Token为全局
	SaveToken(value) {
		if (ecs.isNull(value)) return;
		// console.log("🚀 -> file: api.js:141 -> SaveToken -> getTokenName:", this.getTokenName())

		ecs.localStrorage(this.getTokenName(), value) // 保存Token
	},
	// 根据Timer生成code
	deviceCode(timer) {
		// return "device" + ecs.rand(1000, 9999) + (timer * 3)
		let _str = "winksoft-" + (timer * 4);
		// md5加密_str
		return md5(_str)
	},
	// 修复大整数精度丢失的工具函数（导出供其他地方使用）
	fixBigIntegerFields(data, fields = ['userId', 'cid', 'id', 'parentId', 'roleId', 'deptId', 'menuId']) {
		return fixBigIntegerFields(data, fields)
	}
}

export default api
