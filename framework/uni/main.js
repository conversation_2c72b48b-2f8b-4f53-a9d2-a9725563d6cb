/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2021-09-10 08:57:45
 * @LastEditTime: 2025-05-30 15:23:17
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
let uniUtil = {
	// 是否是小程序
	isMPAPP() {
		const _system = uni.getSystemInfoSync().uniPlatform;
		// console.log("🚀 -> isMPAPP -> _system:", _system, uni.getSystemInfoSync())
		return _system === "mp-weixin";
	},
	// 是否是网页
	isWeb() {
		const _system = uni.getSystemInfoSync().uniPlatform;
		return _system === "web";
	},
	//
	getPlatform() {
		return uni.getSystemInfoSync().uniPlatform
	},
	// 加载的进度设置
	loading(close) {
		if (close == undefined) {
			uni.showLoading({
				title: '加载中'
			});
			setTimeout(() => uni.hideLoading(), 120000)
		} else {
			uni.hideLoading();
		}
	},
	// toast基本使用
	toast(msg, timer) {
		timer = timer || 3000 // 持续时间
		if (this.isNull(msg)) return; // lock
		uni.showToast({
			title: msg,
			duration: timer
		});
	},
	timestamp() {
		// 获取当前13位时间戳
		const currentTimestamp = new Date().getTime();

		// 提取前11位（移除最后两位）
		const timestampWithoutCheck = Math.floor(currentTimestamp / 100);

		const calculateCheckDigit = (timestampWithoutCheck) => {
			// 转为字符串便于处理各位数字
			const timestampStr = String(timestampWithoutCheck);

			// 使用素数和位置权重因子增加复杂度
			const primeFactors = [7, 3, 11, 5, 13, 2, 17, 19, 23, 29, 31, 37];
			let sum = 0;

			// 计算加权和
			for (let i = 0; i < timestampStr.length; i++) {
				const digit = parseInt(timestampStr[i], 10);
				// 使用位置和素数进行加权
				sum += (digit * primeFactors[i % primeFactors.length]) % 97;
			}

			// 附加一个混淆常数
			const obscurityFactor = 42;

			// 计算最终校验位（2位校验码，范围00-99）
			return ((sum + obscurityFactor) % 100);
		}
		// 计算校验位 - 使用混淆算法增加反编译难度
		const checkBit = calculateCheckDigit(timestampWithoutCheck);

		// 拼接时间戳和校验位（2位）
		return timestampWithoutCheck * 100 + checkBit;
	},
	// 封装uni.storage
	localStrorage(key, value) {
		if (key == undefined || key == '') return ""
		if (value == undefined) {
			return uni.getStorageSync(key);// 读
		} else {
			if (value == "clear") {
				uni.removeStorageSync(key);
			} else {
				uni.setStorageSync(key, value);
			}
		}
	},
	// 检查文本是否为空
	isNull(value) {
		return value == undefined || value.length == "" || value.length == 0
	},
	// 生成随机数
	rand(min, max) {
		return Math.floor(Math.random() * (max - min)) + min;
	},
	// 打开新窗口
	openWin(path, query) {
		uni.navigateTo({
			url: path,
		})
	},
	// 关闭当前页面
	closeWin(delta) {
		let _d = delta || 1
		uni.navigateBack({
			delta: _d
		})
	}
}


export default uniUtil
