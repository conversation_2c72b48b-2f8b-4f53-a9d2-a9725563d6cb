/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-07-10 23:18:40
 * @LastEditTime: 2023-12-13 16:05:20
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
let myStorage = {
	// 获取storage
	getItem(key) {
		if(key == undefined || key == '') return "";
		// 同步写法
		let value = uni.getStorageSync(key);
		return value;
	},
	// 设置某个 storage
	setItem(key,value){
		if(key == undefined || key == '') return "";
		uni.setStorageSync({key: key,data: value});
	},
	// 移除或者置空某个storage
	removeItem(key){
		if(key == undefined || key == '') return "";
		uni.removeStorageSync({key: key});
	},
	// 清空所有的storage
	clear(){
		uni.clearStorageSync();
	}

}

export default myStorage
