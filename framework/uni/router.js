/*
 * @Description:  路由管理
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-18 15:34:59
 * @LastEditTime: 2025-03-20 16:45:01
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */

let GetRouter = {
	// 将data参数 转变成pathinfo
	// data: {id:1, name: "test"}
	changePath(url, data) {
		let path = "";
		for (let key in data) {
			if (path == "") {
				path = key + "=" + data[key];
			} else {
				path += "&" + key + "=" + data[key];
			}
		}
		url += (url.indexOf("?") != -1 ? "&" : "?") + path;
		return url;
	},
	//
	to(path, data ,events) {
		if (!path || path == "") return;
		if (data != undefined) {
			path = this.changePath(path, data);
		}
		// console.log("🚀 -> file: router.js:27 -> to -> path:", path)
		return uni.navigateTo({
			url: path,
			events: events
		})
	},
	//
	replace(path,data) {
		if (!path || path == "") return;
		if (data != undefined) {
			path = this.changePath(path, data);
		}
		uni.redirectTo({
			url: path
		});
	},
	// 关闭 返回
	back(delta) {
		let _d = delta || 1
		return uni.navigateBack({
			delta: _d
		})
	},
	timeOutBack() {
		setTimeout(() => {
			GetRouter.back();
		}, 600);
	},
	// 切换首页Tabs
	setIndexTab(url) {
		if (!url || url == "") return;
		uni.switchTab({
			url,
		})
	},
	//
	reLaunch(url) {
		if (!url || url == "") return;
		uni.reLaunch({
			url,
		})
	}

}

export default GetRouter
