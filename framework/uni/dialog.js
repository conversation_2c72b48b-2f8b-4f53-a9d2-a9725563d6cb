/*
 * @Description: 封装Uni的Dialog
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-18 15:38:10
 * @LastEditTime: 2025-03-20 16:44:51
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
import _util from '../script/ecs'
let UniDialog = {
	// 加载的进度设置
	loading(close) {
		if (close == undefined) {
			uni.showLoading({
				title: '加载中'
			});
			setTimeout(() => uni.hideLoading(), 6000)
		} else {
			uni.hideLoading();
		}
	},
	// toast基本使用
	toast(msg, icon, timer) {
		let _timer = timer || 3000 // 持续时间
		let _icon = icon || "none" // 图标 :"success,error,fail,exception,loading,none"
		if (_util.isEmpty(msg)) return; // lock
		let _model = {
			title: msg,
			duration: _timer,
			icon: _icon,
			position: 'bottom'
		}
		if (_icon == "none") _model.position = 'bottom'
		// console.log(_model);
		uni.showToast(_model);
	},
	// policy
	confirm(obj, callback) {
		let _obj = {
			title: "提示",
			content: ""
		}
		if (typeof obj == "string") {
			_obj.content = obj
		} else {
			_obj = obj
		}
		return new Promise((resolve, reject) => {
			uni.showModal({
				title: _obj.title,
				content: _obj.content,
				success: function(res) {
					if (res.confirm) {
						resolve(1);
						_obj.success?.call()
					} else if (res.cancel) {
						reject(0);
						_obj.cancel?.call()
					}
				},
				fail: function (res) {
					reject(-1);
				}
			});
		 })
	},
	//
	alert(obj) {
		let _obj = {
			title: "提示",
			content: ""
		}
		if (typeof obj == "string") {
			_obj.content = obj
		} else {
			_obj = obj
		}
		uni.showModal({
			title: _obj.title,
			content: _obj.content,
			showCancel: false,
			success: function(res) {
				if (res.confirm) {
					_obj.success?.call()
				}
			},
			fail: function(res) {
				// console.log(res.errMsg);
				_obj.fail?.call()
			}
		});
	},
	//  底部弹出选择
	action(obj) {
		uni.showActionSheet({
			title: obj.title,
			itemList: obj.list,
			success: function(res) {
				let _index = res.tapIndex // 选择的index
				// console.log(_index,'点击的actionIndex')
				obj.success(_index)

			},
			fail: function(res) {
				// console.log(res.errMsg);
			}
		});
	},
	// 检查文本是否为空
	isNull(value) {
		return value == undefined || value.length == "" || value.length == 0
	},
}

export default UniDialog
