/*
 * @Description: 缓存及Uni的LocalStorage
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-18 15:40:50
 * @LastEditTime: 2022-10-18 15:42:21
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */

//
let UniCache = {
    // localStorage
    localStrorage(key, value) {
        if (key == undefined || key == '') return ""
        if (value == undefined) {
            // 读
            return uni.getStorageSync(key);
        } else {
            if (value == "clear") {
                uni.removeStorageSync(key);
            } else {
                uni.setStorageSync(key, value);
            }
        }
    },
}

export default UniCache