/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-11-06 21:42:23
 * @LastEditTime: 2025-05-30 16:54:40
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
let _config = {
    baseURL: "/",  //

    tokenName: "userToken",
    isRouyiAdmin: false,
    uniGoIndex: false,  // 当uniapp未登录时是否跳转首页
    uniLoginUrl: "/pages_login/login/login", // 文件上传字段名
    headerTokenName: "Authorization",
    headerTokenPrefix: "Bearer ", // 若依框架header->token需要拼接字符串
    apiErrorNeedLoginCode: 403,  // 当请求拿到的code为-10001时，表示token失效，需要重新登录
    fileUploadPath: "zhjy/company-api/upload/post-file", // 文件上传路径
    fileUploadBaseUrl: null, // 文件上传字段名
    // fileUploadBaseUrl:"http://**************/", // 文件上传字段名
    fileUploadField: "file", // 文件上传字段名
    closeLoading: false,
    toLogin: () => { },
    toErrorPage: () => { },
    authKey:"X-MRGJ-REQ"
}


export const Config = _config;

// 初始化设置
export function setConfig(fields) {
    for (let key in fields) {
        // if (_config.hasOwnProperty(key)) {
        _config[key] = fields[key];
        // }
    }
}

// 获取设置
export function getConfig() {
    return _config;
}
