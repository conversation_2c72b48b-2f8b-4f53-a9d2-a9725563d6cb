@charset "UTF-8";
/*  #ifndef  APP-PLUS  */
/*  #endif  */
/*  #ifdef  APP-PLUS  */
/*  #endif  */
* { -webkit-box-sizing: border-box; box-sizing: border-box; -webkit-tap-highlight-color: transparent; outline: none; margin: 0; border: 0; padding: 0; }

.work-page { padding: 10px 20px; }

.wk-page { padding: 10px 20px; }

view { box-sizing: border-box; margin: 0; border: 0; padding: 0; }

/* 不显示滚动条 */
.scrollBox::-webkit-scrollbar { display: none; width: 0; }

.bs { box-sizing: border-box; }

ul { list-style: none; }

li { list-style: none; }

a { text-decoration: none; }

a:link { text-decoration: none; }

a:visited { text-decoration: none; }

a:active { text-decoration: none; }

html { font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, "Microsoft YaHei", "微软雅黑"; }

/* vue 初始化隐藏 */
[v-cloak] { display: none; }

.oneline { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; -o-text-overflow: ellipsis; }

.oneline2 { white-space: nowrap; overflow: hidden; }

.over-hidden { display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; }

.overhidden { overflow: hidden; white-space: nowrap; }

.inline-block { display: inline-block; }

.imgcol { max-width: 100%; max-height: 100%; }

.imgcol image { max-width: 100%; max-height: 100%; }

.imgcol * { max-width: 100%; max-height: 100%; }

.img { max-width: 100%; max-height: 100%; }

.img * { max-width: 100%; max-height: 100%; }

.aaa { color: #aaa; }

.one { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

.hidden { display: none !important; }

.show { display: block !important; }

.slide-left-enter { opacity: 0; -webkit-transform: translate(80px, 0px); transform: translate(-80px, 0px); }

.slide-left-enter-active { transition: all 0.1s linear; }

.slide-left-leave-to { opacity: 0; -webkit-transform: translate(-80px, 0px); transform: translate(80px, 0px); }

.slide-left-leave-active { transition: all 0.1s linear; }

.slide-right-enter-active, .slide-right-leave-active { will-change: transform; transition: all 500ms; position: absolute; }

.slide-right-enter { opacity: 0; transform: translate3d(-100%, 0, 0); }

.slide-right-leave-active { opacity: 0; transform: translate3d(100%, 0, 0); }

/* fade */
.fade-enter-active, .fade-leave-active { transition: opacity 0.28s; }

.fade-enter, .fade-leave-active { opacity: 0; }

/* fade-transform */
.fade-transform--move, .fade-transform-leave-active, .fade-transform-enter-active { transition: all 0.3s; }

.fade-transform-leave-active { position: absolute; }

.fade-transform-enter { opacity: 0; transform: translateX(-30px); }

.fade-transform-leave-to { opacity: 0; transform: translateX(30px); }

/* breadcrumb transition */
.breadcrumb-enter-active, .breadcrumb-leave-active { transition: all 0.5s; }

.breadcrumb-enter, .breadcrumb-leave-active { opacity: 0; transform: translateX(20px); }

.breadcrumb-move { transition: all 0.5s; }

.breadcrumb-leave-active { position: absolute; }

.flex-row { display: flex; }

.flex-box { display: flex; }

.flex { display: flex; }

.flex-column { display: flex; flex-direction: column; }

.wrap { flex-wrap: wrap !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-right { flex-grow: 1; }

.flex-left { flex-grow: 1; }

.expanded { flex-grow: 1; }

.e1 { flex-grow: 1; }

.flex-stop { flex-shrink: 0; }

.fs { flex-shrink: 0; }

.expanded-1 { flex-grow: 1; }

.expanded-2 { flex-grow: 2; }

.expanded-3 { flex-grow: 3; }

.expanded-4 { flex-grow: 4; }

.expanded-5 { flex-grow: 5; }

.flex-row-center { display: flex; flex-direction: row; justify-content: center; align-items: center; }

.fc { display: flex; flex-direction: row; justify-content: center; align-items: center; }

.flc { display: flex; flex-direction: row; justify-content: flex-start; align-items: center; }

.flex-row-left { display: flex; flex-direction: row; justify-content: left; align-items: center; }

.flex-row-right { display: flex; flex-direction: row; justify-content: right; align-items: center; }

.flex-space { display: flex; flex-direction: row; justify-content: space-around; align-items: center; }

.flex-between { display: flex; flex-direction: row; justify-content: space-between; align-items: center; }

.flex-column-center { display: flex; flex-direction: column; justify-content: center; }

.flex-row-start-stretch { display: flex; flex-direction: row; justify-content: flex-start; align-items: stretch; }

.flex-rowr-start-stretch { display: flex; flex-direction: row-reverse; justify-content: flex-start; align-items: stretch; }

.flex-column-start-stretch { display: flex; flex-direction: column; justify-content: flex-start; align-items: stretch; }

.flex-columnr-start-stretch { display: flex; flex-direction: column-reverse; justify-content: flex-start; align-items: stretch; }

.flex-row-start-start { display: flex; flex-direction: row; justify-content: flex-start; align-items: flex-start; }

.flex-rowr-start-start { display: flex; flex-direction: row-reverse; justify-content: flex-start; align-items: flex-start; }

.flex-column-start-start { display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start; }

.flex-columnr-start-start { display: flex; flex-direction: column-reverse; justify-content: flex-start; align-items: flex-start; }

.flex-row-start-end { display: flex; flex-direction: row; justify-content: flex-start; align-items: flex-end; }

.flex-rowr-start-end { display: flex; flex-direction: row-reverse; justify-content: flex-start; align-items: flex-end; }

.flex-column-start-end { display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-end; }

.flex-columnr-start-end { display: flex; flex-direction: column-reverse; justify-content: flex-start; align-items: flex-end; }

.flex-row-start-center { display: flex; flex-direction: row; justify-content: flex-start; align-items: center; }

.flex-rowr-start-center { display: flex; flex-direction: row-reverse; justify-content: flex-start; align-items: center; }

.flex-column-start-center { display: flex; flex-direction: column; justify-content: flex-start; align-items: center; }

.flex-columnr-start-center { display: flex; flex-direction: column-reverse; justify-content: flex-start; align-items: center; }

.flex-row-start-baseline { display: flex; flex-direction: row; justify-content: flex-start; align-items: baseline; }

.flex-rowr-start-baseline { display: flex; flex-direction: row-reverse; justify-content: flex-start; align-items: baseline; }

.flex-column-start-baseline { display: flex; flex-direction: column; justify-content: flex-start; align-items: baseline; }

.flex-columnr-start-baseline { display: flex; flex-direction: column-reverse; justify-content: flex-start; align-items: baseline; }

.flex-row-end-stretch { display: flex; flex-direction: row; justify-content: flex-end; align-items: stretch; }

.flex-rowr-end-stretch { display: flex; flex-direction: row-reverse; justify-content: flex-end; align-items: stretch; }

.flex-column-end-stretch { display: flex; flex-direction: column; justify-content: flex-end; align-items: stretch; }

.flex-columnr-end-stretch { display: flex; flex-direction: column-reverse; justify-content: flex-end; align-items: stretch; }

.flex-row-end-start { display: flex; flex-direction: row; justify-content: flex-end; align-items: flex-start; }

.flex-rowr-end-start { display: flex; flex-direction: row-reverse; justify-content: flex-end; align-items: flex-start; }

.flex-column-end-start { display: flex; flex-direction: column; justify-content: flex-end; align-items: flex-start; }

.flex-columnr-end-start { display: flex; flex-direction: column-reverse; justify-content: flex-end; align-items: flex-start; }

.flex-row-end-end { display: flex; flex-direction: row; justify-content: flex-end; align-items: flex-end; }

.flex-rowr-end-end { display: flex; flex-direction: row-reverse; justify-content: flex-end; align-items: flex-end; }

.flex-column-end-end { display: flex; flex-direction: column; justify-content: flex-end; align-items: flex-end; }

.flex-columnr-end-end { display: flex; flex-direction: column-reverse; justify-content: flex-end; align-items: flex-end; }

.flex-row-end-center { display: flex; flex-direction: row; justify-content: flex-end; align-items: center; }

.flex-rowr-end-center { display: flex; flex-direction: row-reverse; justify-content: flex-end; align-items: center; }

.flex-column-end-center { display: flex; flex-direction: column; justify-content: flex-end; align-items: center; }

.flex-columnr-end-center { display: flex; flex-direction: column-reverse; justify-content: flex-end; align-items: center; }

.flex-row-end-baseline { display: flex; flex-direction: row; justify-content: flex-end; align-items: baseline; }

.flex-rowr-end-baseline { display: flex; flex-direction: row-reverse; justify-content: flex-end; align-items: baseline; }

.flex-column-end-baseline { display: flex; flex-direction: column; justify-content: flex-end; align-items: baseline; }

.flex-columnr-end-baseline { display: flex; flex-direction: column-reverse; justify-content: flex-end; align-items: baseline; }

.flex-row-center-stretch { display: flex; flex-direction: row; justify-content: center; align-items: stretch; }

.flex-rowr-center-stretch { display: flex; flex-direction: row-reverse; justify-content: center; align-items: stretch; }

.flex-column-center-stretch { display: flex; flex-direction: column; justify-content: center; align-items: stretch; }

.flex-columnr-center-stretch { display: flex; flex-direction: column-reverse; justify-content: center; align-items: stretch; }

.flex-row-center-start { display: flex; flex-direction: row; justify-content: center; align-items: flex-start; }

.flex-rowr-center-start { display: flex; flex-direction: row-reverse; justify-content: center; align-items: flex-start; }

.flex-column-center-start { display: flex; flex-direction: column; justify-content: center; align-items: flex-start; }

.flex-columnr-center-start { display: flex; flex-direction: column-reverse; justify-content: center; align-items: flex-start; }

.flex-row-center-end { display: flex; flex-direction: row; justify-content: center; align-items: flex-end; }

.flex-rowr-center-end { display: flex; flex-direction: row-reverse; justify-content: center; align-items: flex-end; }

.flex-column-center-end { display: flex; flex-direction: column; justify-content: center; align-items: flex-end; }

.flex-columnr-center-end { display: flex; flex-direction: column-reverse; justify-content: center; align-items: flex-end; }

.flex-row-center-center { display: flex; flex-direction: row; justify-content: center; align-items: center; }

.flex-rowr-center-center { display: flex; flex-direction: row-reverse; justify-content: center; align-items: center; }

.flex-column-center-center { display: flex; flex-direction: column; justify-content: center; align-items: center; }

.flex-columnr-center-center { display: flex; flex-direction: column-reverse; justify-content: center; align-items: center; }

.flex-row-center-baseline { display: flex; flex-direction: row; justify-content: center; align-items: baseline; }

.flex-rowr-center-baseline { display: flex; flex-direction: row-reverse; justify-content: center; align-items: baseline; }

.flex-column-center-baseline { display: flex; flex-direction: column; justify-content: center; align-items: baseline; }

.flex-columnr-center-baseline { display: flex; flex-direction: column-reverse; justify-content: center; align-items: baseline; }

.flex-row-between-stretch { display: flex; flex-direction: row; justify-content: space-between; align-items: stretch; }

.flex-rowr-between-stretch { display: flex; flex-direction: row-reverse; justify-content: space-between; align-items: stretch; }

.flex-column-between-stretch { display: flex; flex-direction: column; justify-content: space-between; align-items: stretch; }

.flex-columnr-between-stretch { display: flex; flex-direction: column-reverse; justify-content: space-between; align-items: stretch; }

.flex-row-between-start { display: flex; flex-direction: row; justify-content: space-between; align-items: flex-start; }

.flex-rowr-between-start { display: flex; flex-direction: row-reverse; justify-content: space-between; align-items: flex-start; }

.flex-column-between-start { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-start; }

.flex-columnr-between-start { display: flex; flex-direction: column-reverse; justify-content: space-between; align-items: flex-start; }

.flex-row-between-end { display: flex; flex-direction: row; justify-content: space-between; align-items: flex-end; }

.flex-rowr-between-end { display: flex; flex-direction: row-reverse; justify-content: space-between; align-items: flex-end; }

.flex-column-between-end { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; }

.flex-columnr-between-end { display: flex; flex-direction: column-reverse; justify-content: space-between; align-items: flex-end; }

.flex-row-between-center { display: flex; flex-direction: row; justify-content: space-between; align-items: center; }

.flex-rowr-between-center { display: flex; flex-direction: row-reverse; justify-content: space-between; align-items: center; }

.flex-column-between-center { display: flex; flex-direction: column; justify-content: space-between; align-items: center; }

.flex-columnr-between-center { display: flex; flex-direction: column-reverse; justify-content: space-between; align-items: center; }

.flex-row-between-baseline { display: flex; flex-direction: row; justify-content: space-between; align-items: baseline; }

.flex-rowr-between-baseline { display: flex; flex-direction: row-reverse; justify-content: space-between; align-items: baseline; }

.flex-column-between-baseline { display: flex; flex-direction: column; justify-content: space-between; align-items: baseline; }

.flex-columnr-between-baseline { display: flex; flex-direction: column-reverse; justify-content: space-between; align-items: baseline; }

.flex-row-around-stretch { display: flex; flex-direction: row; justify-content: space-around; align-items: stretch; }

.flex-rowr-around-stretch { display: flex; flex-direction: row-reverse; justify-content: space-around; align-items: stretch; }

.flex-column-around-stretch { display: flex; flex-direction: column; justify-content: space-around; align-items: stretch; }

.flex-columnr-around-stretch { display: flex; flex-direction: column-reverse; justify-content: space-around; align-items: stretch; }

.flex-row-around-start { display: flex; flex-direction: row; justify-content: space-around; align-items: flex-start; }

.flex-rowr-around-start { display: flex; flex-direction: row-reverse; justify-content: space-around; align-items: flex-start; }

.flex-column-around-start { display: flex; flex-direction: column; justify-content: space-around; align-items: flex-start; }

.flex-columnr-around-start { display: flex; flex-direction: column-reverse; justify-content: space-around; align-items: flex-start; }

.flex-row-around-end { display: flex; flex-direction: row; justify-content: space-around; align-items: flex-end; }

.flex-rowr-around-end { display: flex; flex-direction: row-reverse; justify-content: space-around; align-items: flex-end; }

.flex-column-around-end { display: flex; flex-direction: column; justify-content: space-around; align-items: flex-end; }

.flex-columnr-around-end { display: flex; flex-direction: column-reverse; justify-content: space-around; align-items: flex-end; }

.flex-row-around-center { display: flex; flex-direction: row; justify-content: space-around; align-items: center; }

.flex-rowr-around-center { display: flex; flex-direction: row-reverse; justify-content: space-around; align-items: center; }

.flex-column-around-center { display: flex; flex-direction: column; justify-content: space-around; align-items: center; }

.flex-columnr-around-center { display: flex; flex-direction: column-reverse; justify-content: space-around; align-items: center; }

.flex-row-around-baseline { display: flex; flex-direction: row; justify-content: space-around; align-items: baseline; }

.flex-rowr-around-baseline { display: flex; flex-direction: row-reverse; justify-content: space-around; align-items: baseline; }

.flex-column-around-baseline { display: flex; flex-direction: column; justify-content: space-around; align-items: baseline; }

.flex-columnr-around-baseline { display: flex; flex-direction: column-reverse; justify-content: space-around; align-items: baseline; }

.indent { text-indent: 2em; }

.indent1 { text-indent: 1em; }

.inline { display: inline; }

.scroll-y { overflow-y: scroll; overflow-x: hidden; }

.scroll-y::-webkit-scrollbar { width: 1px; }

.scrollablewithout { overflow-y: scroll; /* 允许上下滚动 */ -ms-overflow-style: none; /* IE 和 Edge 隐藏滚动条 */ scrollbar-width: none; /* Firefox 隐藏滚动条 */ }

.scrollablewithout::-webkit-scrollbar { display: none; /* Chrome, Safari 和 Opera 隐藏滚动条 */ }

.scroll-widget::-webkit-scrollbar { width: 1px; }

.scroll-x { overflow-x: scroll; overflow-y: hidden; }

.i { font-style: italic; }

.del { text-decoration: line-through; }

.u { text-decoration: underline; }

.text-left { text-align: left; }

.text-right { text-align: right; }

.text-center { text-align: center; }

.text-justify { text-align: justify; }

.text-nowrap { white-space: nowrap; }

.text-el { line-height: 28px; font-size: 14px; color: #606266; font-weight: 600; }

.text-lowercase { text-transform: lowercase; }

.text-uppercase { text-transform: uppercase; }

.text-capitalize { text-transform: capitalize; }

.sk-1 { transform: scale(0.9); }

.sk-2 { transform: scale(0.8); }

.sk-3 { transform: scale(0.7); }

.sk-4 { transform: scale(0.6); }

.sk-5 { transform: scale(0.5); }

.sk-6 { transform: scale(0.4); }

.sk-7 { transform: scale(0.3); }

.sk-8 { transform: scale(0.2); }

.sk-9 { transform: scale(0.1); }

.weight-1 { font-weight: 100; }

.max-line-1 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm1 { transform: scale(0.1); }

.weight-2 { font-weight: 200; }

.max-line-2 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm2 { transform: scale(0.2); }

.weight-3 { font-weight: 300; }

.max-line-3 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm3 { transform: scale(0.3); }

.weight-4 { font-weight: 400; }

.max-line-4 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 4; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm4 { transform: scale(0.4); }

.weight-5 { font-weight: 500; }

.max-line-5 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 5; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm5 { transform: scale(0.5); }

.weight-6 { font-weight: 600; }

.max-line-6 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 6; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm6 { transform: scale(0.6); }

.weight-7 { font-weight: 700; }

.max-line-7 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 7; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm7 { transform: scale(0.7); }

.weight-8 { font-weight: 800; }

.max-line-8 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 8; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm8 { transform: scale(0.8); }

.weight-9 { font-weight: 900; }

.max-line-9 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 9; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm9 { transform: scale(0.9); }

.weight-10 { font-weight: 1000; }

.max-line-10 { overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 10; text-overflow: ellipsis; -o-text-overflow: ellipsis; word-break: break-all; }

.tm10 { transform: scale(0.10); }

.mark-right-top-primary { background-image: linear-gradient(225deg, #1890ff 6px, #fff 6px); }

.mark-left-top-primary { background-image: linear-gradient(135deg, #1890ff 6px, #fff 6px); }

.mark-left-bottom-primary { background-image: linear-gradient(45deg, #1890ff 6px, #fff 6px); }

.mark-right-bottom-primary { background-image: linear-gradient(315deg, #1890ff 6px, #fff 6px); }

.point-l-primary, .point-r-primary { position: relative; font-size: 14px; color: #333; }

.point-l-primary:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #1890ff; }

.point-r-primary:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #1890ff; }

.mark-right-top-success { background-image: linear-gradient(225deg, #52c41a 6px, #fff 6px); }

.mark-left-top-success { background-image: linear-gradient(135deg, #52c41a 6px, #fff 6px); }

.mark-left-bottom-success { background-image: linear-gradient(45deg, #52c41a 6px, #fff 6px); }

.mark-right-bottom-success { background-image: linear-gradient(315deg, #52c41a 6px, #fff 6px); }

.point-l-success, .point-r-success { position: relative; font-size: 14px; color: #333; }

.point-l-success:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #52c41a; }

.point-r-success:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #52c41a; }

.mark-right-top-warning { background-image: linear-gradient(225deg, #faad14 6px, #fff 6px); }

.mark-left-top-warning { background-image: linear-gradient(135deg, #faad14 6px, #fff 6px); }

.mark-left-bottom-warning { background-image: linear-gradient(45deg, #faad14 6px, #fff 6px); }

.mark-right-bottom-warning { background-image: linear-gradient(315deg, #faad14 6px, #fff 6px); }

.point-l-warning, .point-r-warning { position: relative; font-size: 14px; color: #333; }

.point-l-warning:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #faad14; }

.point-r-warning:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #faad14; }

.mark-right-top-info { background-image: linear-gradient(225deg, #909399 6px, #fff 6px); }

.mark-left-top-info { background-image: linear-gradient(135deg, #909399 6px, #fff 6px); }

.mark-left-bottom-info { background-image: linear-gradient(45deg, #909399 6px, #fff 6px); }

.mark-right-bottom-info { background-image: linear-gradient(315deg, #909399 6px, #fff 6px); }

.point-l-info, .point-r-info { position: relative; font-size: 14px; color: #333; }

.point-l-info:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #909399; }

.point-r-info:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #909399; }

.mark-right-top-danger { background-image: linear-gradient(225deg, #ff4949 6px, #fff 6px); }

.mark-left-top-danger { background-image: linear-gradient(135deg, #ff4949 6px, #fff 6px); }

.mark-left-bottom-danger { background-image: linear-gradient(45deg, #ff4949 6px, #fff 6px); }

.mark-right-bottom-danger { background-image: linear-gradient(315deg, #ff4949 6px, #fff 6px); }

.point-l-danger, .point-r-danger { position: relative; font-size: 14px; color: #333; }

.point-l-danger:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #ff4949; }

.point-r-danger:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #ff4949; }

.mark-right-top-gov { background-image: linear-gradient(225deg, #cd3a38 6px, #fff 6px); }

.mark-left-top-gov { background-image: linear-gradient(135deg, #cd3a38 6px, #fff 6px); }

.mark-left-bottom-gov { background-image: linear-gradient(45deg, #cd3a38 6px, #fff 6px); }

.mark-right-bottom-gov { background-image: linear-gradient(315deg, #cd3a38 6px, #fff 6px); }

.point-l-gov, .point-r-gov { position: relative; font-size: 14px; color: #333; }

.point-l-gov:before { content: " "; width: 6px; height: 6px; border-radius: 6px; display: inline-block; position: relative; top: -3px; margin-right: 6px; background-color: #cd3a38; }

.point-r-gov:after { content: " "; width: 4px; height: 4px; border-radius: 4px; display: inline-block; position: relative; top: -3px; margin-left: 6px; background-color: #cd3a38; }

.bg-hidden { background-color: Transparent !important; }

.bg-white { background-color: #ffffff; }

.bg-white-rever { background: linear-gradient(to right, #ffffff, rgba(255, 255, 255, 0.8)); }

.bg-white-liner { background: linear-gradient(to right, rgba(255, 255, 255, 0.8), #ffffff); }

.color-white { color: #ffffff; }

.text-white { color: #ffffff; }

.text-hover-white:hover { color: #ffffff !important; }

.bg-hover-white:hover { background-color: #ffffff !important; }

.bg-light { background-color: #f2f6fc; }

.bg-light-rever { background: linear-gradient(to right, #f2f6fc, rgba(242, 246, 252, 0.8)); }

.bg-light-liner { background: linear-gradient(to right, rgba(242, 246, 252, 0.8), #f2f6fc); }

.color-light { color: #f2f6fc; }

.text-light { color: #f2f6fc; }

.text-hover-light:hover { color: #f2f6fc !important; }

.bg-hover-light:hover { background-color: #f2f6fc !important; }

.bg-f7 { background-color: #f7f7f7; }

.bg-f7-rever { background: linear-gradient(to right, #f7f7f7, rgba(247, 247, 247, 0.8)); }

.bg-f7-liner { background: linear-gradient(to right, rgba(247, 247, 247, 0.8), #f7f7f7); }

.color-f7 { color: #f7f7f7; }

.text-f7 { color: #f7f7f7; }

.text-hover-f7:hover { color: #f7f7f7 !important; }

.bg-hover-f7:hover { background-color: #f7f7f7 !important; }

.bg-f0 { background-color: #f0f0f0; }

.bg-f0-rever { background: linear-gradient(to right, #f0f0f0, rgba(240, 240, 240, 0.8)); }

.bg-f0-liner { background: linear-gradient(to right, rgba(240, 240, 240, 0.8), #f0f0f0); }

.color-f0 { color: #f0f0f0; }

.text-f0 { color: #f0f0f0; }

.text-hover-f0:hover { color: #f0f0f0 !important; }

.bg-hover-f0:hover { background-color: #f0f0f0 !important; }

.bg-eee { background-color: #eee; }

.bg-eee-rever { background: linear-gradient(to right, #eee, rgba(238, 238, 238, 0.8)); }

.bg-eee-liner { background: linear-gradient(to right, rgba(238, 238, 238, 0.8), #eee); }

.color-eee { color: #eee; }

.text-eee { color: #eee; }

.text-hover-eee:hover { color: #eee !important; }

.bg-hover-eee:hover { background-color: #eee !important; }

.bg-ccc { background-color: #ccc; }

.bg-ccc-rever { background: linear-gradient(to right, #ccc, rgba(204, 204, 204, 0.8)); }

.bg-ccc-liner { background: linear-gradient(to right, rgba(204, 204, 204, 0.8), #ccc); }

.color-ccc { color: #ccc; }

.text-ccc { color: #ccc; }

.text-hover-ccc:hover { color: #ccc !important; }

.bg-hover-ccc:hover { background-color: #ccc !important; }

.bg-danger { background-color: #ff4949; }

.bg-danger-rever { background: linear-gradient(to right, #ff4949, rgba(255, 73, 73, 0.8)); }

.bg-danger-liner { background: linear-gradient(to right, rgba(255, 73, 73, 0.8), #ff4949); }

.color-danger { color: #ff4949; }

.text-danger { color: #ff4949; }

.text-hover-danger:hover { color: #ff4949 !important; }

.bg-hover-danger:hover { background-color: #ff4949 !important; }

.bg-primary { background-color: #1890ff; }

.bg-primary-rever { background: linear-gradient(to right, #1890ff, rgba(24, 144, 255, 0.8)); }

.bg-primary-liner { background: linear-gradient(to right, rgba(24, 144, 255, 0.8), #1890ff); }

.color-primary { color: #1890ff; }

.text-primary { color: #1890ff; }

.text-hover-primary:hover { color: #1890ff !important; }

.bg-hover-primary:hover { background-color: #1890ff !important; }

.bg-success { background-color: #52c41a; }

.bg-success-rever { background: linear-gradient(to right, #52c41a, rgba(82, 196, 26, 0.8)); }

.bg-success-liner { background: linear-gradient(to right, rgba(82, 196, 26, 0.8), #52c41a); }

.color-success { color: #52c41a; }

.text-success { color: #52c41a; }

.text-hover-success:hover { color: #52c41a !important; }

.bg-hover-success:hover { background-color: #52c41a !important; }

.bg-warning { background-color: #faad14; }

.bg-warning-rever { background: linear-gradient(to right, #faad14, rgba(250, 173, 20, 0.8)); }

.bg-warning-liner { background: linear-gradient(to right, rgba(250, 173, 20, 0.8), #faad14); }

.color-warning { color: #faad14; }

.text-warning { color: #faad14; }

.text-hover-warning:hover { color: #faad14 !important; }

.bg-hover-warning:hover { background-color: #faad14 !important; }

.bg-info { background-color: #909399; }

.bg-info-rever { background: linear-gradient(to right, #909399, rgba(144, 147, 153, 0.8)); }

.bg-info-liner { background: linear-gradient(to right, rgba(144, 147, 153, 0.8), #909399); }

.color-info { color: #909399; }

.text-info { color: #909399; }

.text-hover-info:hover { color: #909399 !important; }

.bg-hover-info:hover { background-color: #909399 !important; }

.bg-lightblue { background-color: #ecf9ff; }

.bg-lightblue-rever { background: linear-gradient(to right, #ecf9ff, rgba(236, 249, 255, 0.8)); }

.bg-lightblue-liner { background: linear-gradient(to right, rgba(236, 249, 255, 0.8), #ecf9ff); }

.color-lightblue { color: #ecf9ff; }

.text-lightblue { color: #ecf9ff; }

.text-hover-lightblue:hover { color: #ecf9ff !important; }

.bg-hover-lightblue:hover { background-color: #ecf9ff !important; }

.bg-lightred { background-color: #fde2e2; }

.bg-lightred-rever { background: linear-gradient(to right, #fde2e2, rgba(253, 226, 226, 0.8)); }

.bg-lightred-liner { background: linear-gradient(to right, rgba(253, 226, 226, 0.8), #fde2e2); }

.color-lightred { color: #fde2e2; }

.text-lightred { color: #fde2e2; }

.text-hover-lightred:hover { color: #fde2e2 !important; }

.bg-hover-lightred:hover { background-color: #fde2e2 !important; }

.bg-lightyellow { background-color: #f9ecd8; }

.bg-lightyellow-rever { background: linear-gradient(to right, #f9ecd8, rgba(249, 236, 216, 0.8)); }

.bg-lightyellow-liner { background: linear-gradient(to right, rgba(249, 236, 216, 0.8), #f9ecd8); }

.color-lightyellow { color: #f9ecd8; }

.text-lightyellow { color: #f9ecd8; }

.text-hover-lightyellow:hover { color: #f9ecd8 !important; }

.bg-hover-lightyellow:hover { background-color: #f9ecd8 !important; }

.bg-lightgreen { background-color: #e1f3d8; }

.bg-lightgreen-rever { background: linear-gradient(to right, #e1f3d8, rgba(225, 243, 216, 0.8)); }

.bg-lightgreen-liner { background: linear-gradient(to right, rgba(225, 243, 216, 0.8), #e1f3d8); }

.color-lightgreen { color: #e1f3d8; }

.text-lightgreen { color: #e1f3d8; }

.text-hover-lightgreen:hover { color: #e1f3d8 !important; }

.bg-hover-lightgreen:hover { background-color: #e1f3d8 !important; }

.bg-30 { background-color: #303133; }

.bg-30-rever { background: linear-gradient(to right, #303133, rgba(48, 49, 51, 0.8)); }

.bg-30-liner { background: linear-gradient(to right, rgba(48, 49, 51, 0.8), #303133); }

.color-30 { color: #303133; }

.text-30 { color: #303133; }

.text-hover-30:hover { color: #303133 !important; }

.bg-hover-30:hover { background-color: #303133 !important; }

.bg-60 { background-color: #606266; }

.bg-60-rever { background: linear-gradient(to right, #606266, rgba(96, 98, 102, 0.8)); }

.bg-60-liner { background: linear-gradient(to right, rgba(96, 98, 102, 0.8), #606266); }

.color-60 { color: #606266; }

.text-60 { color: #606266; }

.text-hover-60:hover { color: #606266 !important; }

.bg-hover-60:hover { background-color: #606266 !important; }

.bg-90 { background-color: #909399; }

.bg-90-rever { background: linear-gradient(to right, #909399, rgba(144, 147, 153, 0.8)); }

.bg-90-liner { background: linear-gradient(to right, rgba(144, 147, 153, 0.8), #909399); }

.color-90 { color: #909399; }

.text-90 { color: #909399; }

.text-hover-90:hover { color: #909399 !important; }

.bg-hover-90:hover { background-color: #909399 !important; }

.bg-c0 { background-color: #C0C4CC; }

.bg-c0-rever { background: linear-gradient(to right, #C0C4CC, rgba(192, 196, 204, 0.8)); }

.bg-c0-liner { background: linear-gradient(to right, rgba(192, 196, 204, 0.8), #C0C4CC); }

.color-c0 { color: #C0C4CC; }

.text-c0 { color: #C0C4CC; }

.text-hover-c0:hover { color: #C0C4CC !important; }

.bg-hover-c0:hover { background-color: #C0C4CC !important; }

.bg-eb { background-color: #EBEEF5; }

.bg-eb-rever { background: linear-gradient(to right, #EBEEF5, rgba(235, 238, 245, 0.8)); }

.bg-eb-liner { background: linear-gradient(to right, rgba(235, 238, 245, 0.8), #EBEEF5); }

.color-eb { color: #EBEEF5; }

.text-eb { color: #EBEEF5; }

.text-hover-eb:hover { color: #EBEEF5 !important; }

.bg-hover-eb:hover { background-color: #EBEEF5 !important; }

.bg-e4 { background-color: #E4E7ED; }

.bg-e4-rever { background: linear-gradient(to right, #E4E7ED, rgba(228, 231, 237, 0.8)); }

.bg-e4-liner { background: linear-gradient(to right, rgba(228, 231, 237, 0.8), #E4E7ED); }

.color-e4 { color: #E4E7ED; }

.text-e4 { color: #E4E7ED; }

.text-hover-e4:hover { color: #E4E7ED !important; }

.bg-hover-e4:hover { background-color: #E4E7ED !important; }

.bg-dc { background-color: #DCDFE6; }

.bg-dc-rever { background: linear-gradient(to right, #DCDFE6, rgba(220, 223, 230, 0.8)); }

.bg-dc-liner { background: linear-gradient(to right, rgba(220, 223, 230, 0.8), #DCDFE6); }

.color-dc { color: #DCDFE6; }

.text-dc { color: #DCDFE6; }

.text-hover-dc:hover { color: #DCDFE6 !important; }

.bg-hover-dc:hover { background-color: #DCDFE6 !important; }

.bg-000 { background-color: #000; }

.bg-000-rever { background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.8)); }

.bg-000-liner { background: linear-gradient(to right, rgba(0, 0, 0, 0.8), #000); }

.color-000 { color: #000; }

.text-000 { color: #000; }

.text-hover-000:hover { color: #000 !important; }

.bg-hover-000:hover { background-color: #000 !important; }

.bg-gov { background-color: #cd3a38; }

.bg-gov-rever { background: linear-gradient(to right, #cd3a38, rgba(205, 58, 56, 0.8)); }

.bg-gov-liner { background: linear-gradient(to right, rgba(205, 58, 56, 0.8), #cd3a38); }

.color-gov { color: #cd3a38; }

.text-gov { color: #cd3a38; }

.text-hover-gov:hover { color: #cd3a38 !important; }

.bg-hover-gov:hover { background-color: #cd3a38 !important; }

.bg-purpleblue { background-color: #bae0ff; }

.bg-purpleblue-rever { background: linear-gradient(to right, #bae0ff, rgba(186, 224, 255, 0.8)); }

.bg-purpleblue-liner { background: linear-gradient(to right, rgba(186, 224, 255, 0.8), #bae0ff); }

.color-purpleblue { color: #bae0ff; }

.text-purpleblue { color: #bae0ff; }

.text-hover-purpleblue:hover { color: #bae0ff !important; }

.bg-hover-purpleblue:hover { background-color: #bae0ff !important; }

.bg-grey { background-color: #e6ebf5; }

.bg-grey-rever { background: linear-gradient(to right, #e6ebf5, rgba(230, 235, 245, 0.8)); }

.bg-grey-liner { background: linear-gradient(to right, rgba(230, 235, 245, 0.8), #e6ebf5); }

.color-grey { color: #e6ebf5; }

.text-grey { color: #e6ebf5; }

.text-hover-grey:hover { color: #e6ebf5 !important; }

.bg-hover-grey:hover { background-color: #e6ebf5 !important; }

.bg-cblue { background-color: #f2f5fe; }

.bg-cblue-rever { background: linear-gradient(to right, #f2f5fe, rgba(242, 245, 254, 0.8)); }

.bg-cblue-liner { background: linear-gradient(to right, rgba(242, 245, 254, 0.8), #f2f5fe); }

.color-cblue { color: #f2f5fe; }

.text-cblue { color: #f2f5fe; }

.text-hover-cblue:hover { color: #f2f5fe !important; }

.bg-hover-cblue:hover { background-color: #f2f5fe !important; }

.bg-lilac { background-color: #f0e2ef; }

.bg-lilac-rever { background: linear-gradient(to right, #f0e2ef, rgba(240, 226, 239, 0.8)); }

.bg-lilac-liner { background: linear-gradient(to right, rgba(240, 226, 239, 0.8), #f0e2ef); }

.color-lilac { color: #f0e2ef; }

.text-lilac { color: #f0e2ef; }

.text-hover-lilac:hover { color: #f0e2ef !important; }

.bg-hover-lilac:hover { background-color: #f0e2ef !important; }

.bg-grassgreen { background-color: #42ccb3; }

.bg-grassgreen-rever { background: linear-gradient(to right, #42ccb3, rgba(66, 204, 179, 0.8)); }

.bg-grassgreen-liner { background: linear-gradient(to right, rgba(66, 204, 179, 0.8), #42ccb3); }

.color-grassgreen { color: #42ccb3; }

.text-grassgreen { color: #42ccb3; }

.text-hover-grassgreen:hover { color: #42ccb3 !important; }

.bg-hover-grassgreen:hover { background-color: #42ccb3 !important; }

.text-label { font-size: 12px; color: #888; }

.c000 { color: #000; }

.bg000 { background-color: #000; }

.c111 { color: #111; }

.bg111 { background-color: #111; }

.c222 { color: #222; }

.bg222 { background-color: #222; }

.c333 { color: #333; }

.bg333 { background-color: #333; }

.c444 { color: #444; }

.bg444 { background-color: #444; }

.c555 { color: #555; }

.bg555 { background-color: #555; }

.c666 { color: #666; }

.bg666 { background-color: #666; }

.c777 { color: #777; }

.bg777 { background-color: #777; }

.c888 { color: #888; }

.bg888 { background-color: #888; }

.c999 { color: #999; }

.bg999 { background-color: #999; }

.caaa { color: #aaa; }

.bgaaa { background-color: #aaa; }

.cbbb { color: #bbb; }

.bgbbb { background-color: #bbb; }

.cccc { color: #ccc; }

.bgccc { background-color: #ccc; }

.cddd { color: #ddd; }

.bgddd { background-color: #ddd; }

.ceee { color: #eee; }

.bgeee { background-color: #eee; }

.cfff { color: #fff; }

.bgfff { background-color: #fff; }

.cf0f0f0 { color: #f0f0f0; }

.bgf0f0f0 { background-color: #f0f0f0; }

.cf7f7f7 { color: #f7f7f7; }

.bgf7f7f7 { background-color: #f7f7f7; }

.cfcfcfc { color: #fcfcfc; }

.bgfcfcfc { background-color: #fcfcfc; }

.cfefefe { color: #fefefe; }

.bgfefefe { background-color: #fefefe; }

.c69b1ff { color: #69b1ff; }

.bg69b1ff { background-color: #69b1ff; }

.cf02937 { color: #f02937; }

.bgf02937 { background-color: #f02937; }

.c1890ff { color: #1890ff; }

.bg1890ff { background-color: #1890ff; }

.c1890ff { color: #1890ff; }

.bg1890ff { background-color: #1890ff; }

.c3389ff { color: #3389ff; }

.bg3389ff { background-color: #3389ff; }

.c0ec170 { color: #0ec170; }

.bg0ec170 { background-color: #0ec170; }

.cc59fc6 { color: #c59fc6; }

.bgc59fc6 { background-color: #c59fc6; }

.c46a6ff { color: #46a6ff; }

.bg46a6ff { background-color: #46a6ff; }

.ceaba19 { color: #eaba19; }

.bgeaba19 { background-color: #eaba19; }

.white { color: #fff; }

.bg-danger { color: #fff; background-color: #ff4949; }

.bg-primary { color: #fff; background-color: #1890ff; }

.bg-success { color: #fff; background-color: #52c41a; }

.bg-warning { color: #fff; background-color: #faad14; }

.bg-info { color: #fff; background-color: #909399; }

.iconfont { font-size: 1rem; }

.tc { text-align: center !important; }

.tl { text-align: left; }

.tr { text-align: right; }

.tj { text-align: justify; }

.border-white-1 { border: 1rpx #fff solid; }

.border-info-1 { border: 1rpx #e8eaec solid; }

.border-grey-1 { border: 1rpx #dcdee2 solid; }

.border-danger-1 { border: 1rpx #ff4949 solid; }

.border-primary-1 { border: 1rpx #1890ff solid; }

.border-success-1 { border: 1rpx #52c41a solid; }

.border-warning-1 { border: 1rpx #faad14 solid; }

.border-gov-1 { border: 1rpx #cd3a38 solid; }

.border-top-info-1 { border-top: 1rpx #e8eaec solid; }

.border-top-grey-1 { border-top: 1rpx #dcdee2 solid; }

.border-top-danger-1 { border-top: 1rpx #ff4949 solid; }

.border-top-gov-1 { border-top: 1rpx #cd3a38 solid; }

.border-top-primary-1 { border-top: 1rpx #1890ff solid; }

.border-top-success-1 { border-top: 1rpx #52c41a solid; }

.border-top-warning-1 { border-top: 1rpx #faad14 solid; }

.border-left-info-1 { border-left: 1rpx #e8eaec solid; }

.border-left-grey-1 { border-left: 1rpx #dcdee2 solid; }

.border-left-danger-1 { border-left: 1rpx #ff4949 solid; }

.border-left-gov-1 { border-left: 1rpx #cd3a38 solid; }

.border-left-primary-1 { border-left: 1rpx #1890ff solid; }

.border-left-success-1 { border-left: 1rpx #52c41a solid; }

.border-left-warning-1 { border-left: 1rpx #faad14 solid; }

.border-bottom-info-1 { border-bottom: 1rpx #e8eaec solid; }

.border-bottom-grey-1 { border-bottom: 1rpx #dcdee2 solid; }

.border-bottom-danger-1 { border-bottom: 1rpx #ff4949 solid; }

.border-bottom-primary-1 { border-bottom: 1rpx #1890ff solid; }

.border-bottom-success-1 { border-bottom: 1rpx #52c41a solid; }

.border-bottom-warning-1 { border-bottom: 1rpx #faad14 solid; }

.border-bottom-gov-1 { border-bottom: 1rpx #cd3a38 solid; }

.border-right-info-1 { border-right: 1rpx #e8eaec solid; }

.border-right-grey-1 { border-right: 1rpx #dcdee2 solid; }

.border-right-danger-1 { border-right: 1rpx #ff4949 solid; }

.border-right-gov-1 { border-right: 1rpx #cd3a38 solid; }

.border-right-primary-1 { border-right: 1rpx #1890ff solid; }

.border-right-success-1 { border-right: 1rpx #52c41a solid; }

.border-right-warning-1 { border-right: 1rpx #faad14 solid; }

.border-white-2 { border: 2rpx #fff solid; }

.border-info-2 { border: 2rpx #e8eaec solid; }

.border-grey-2 { border: 2rpx #dcdee2 solid; }

.border-danger-2 { border: 2rpx #ff4949 solid; }

.border-primary-2 { border: 2rpx #1890ff solid; }

.border-success-2 { border: 2rpx #52c41a solid; }

.border-warning-2 { border: 2rpx #faad14 solid; }

.border-gov-2 { border: 2rpx #cd3a38 solid; }

.border-top-info-2 { border-top: 2rpx #e8eaec solid; }

.border-top-grey-2 { border-top: 2rpx #dcdee2 solid; }

.border-top-danger-2 { border-top: 2rpx #ff4949 solid; }

.border-top-gov-2 { border-top: 2rpx #cd3a38 solid; }

.border-top-primary-2 { border-top: 2rpx #1890ff solid; }

.border-top-success-2 { border-top: 2rpx #52c41a solid; }

.border-top-warning-2 { border-top: 2rpx #faad14 solid; }

.border-left-info-2 { border-left: 2rpx #e8eaec solid; }

.border-left-grey-2 { border-left: 2rpx #dcdee2 solid; }

.border-left-danger-2 { border-left: 2rpx #ff4949 solid; }

.border-left-gov-2 { border-left: 2rpx #cd3a38 solid; }

.border-left-primary-2 { border-left: 2rpx #1890ff solid; }

.border-left-success-2 { border-left: 2rpx #52c41a solid; }

.border-left-warning-2 { border-left: 2rpx #faad14 solid; }

.border-bottom-info-2 { border-bottom: 2rpx #e8eaec solid; }

.border-bottom-grey-2 { border-bottom: 2rpx #dcdee2 solid; }

.border-bottom-danger-2 { border-bottom: 2rpx #ff4949 solid; }

.border-bottom-primary-2 { border-bottom: 2rpx #1890ff solid; }

.border-bottom-success-2 { border-bottom: 2rpx #52c41a solid; }

.border-bottom-warning-2 { border-bottom: 2rpx #faad14 solid; }

.border-bottom-gov-2 { border-bottom: 2rpx #cd3a38 solid; }

.border-right-info-2 { border-right: 2rpx #e8eaec solid; }

.border-right-grey-2 { border-right: 2rpx #dcdee2 solid; }

.border-right-danger-2 { border-right: 2rpx #ff4949 solid; }

.border-right-gov-2 { border-right: 2rpx #cd3a38 solid; }

.border-right-primary-2 { border-right: 2rpx #1890ff solid; }

.border-right-success-2 { border-right: 2rpx #52c41a solid; }

.border-right-warning-2 { border-right: 2rpx #faad14 solid; }

.border-white-3 { border: 3rpx #fff solid; }

.border-info-3 { border: 3rpx #e8eaec solid; }

.border-grey-3 { border: 3rpx #dcdee2 solid; }

.border-danger-3 { border: 3rpx #ff4949 solid; }

.border-primary-3 { border: 3rpx #1890ff solid; }

.border-success-3 { border: 3rpx #52c41a solid; }

.border-warning-3 { border: 3rpx #faad14 solid; }

.border-gov-3 { border: 3rpx #cd3a38 solid; }

.border-top-info-3 { border-top: 3rpx #e8eaec solid; }

.border-top-grey-3 { border-top: 3rpx #dcdee2 solid; }

.border-top-danger-3 { border-top: 3rpx #ff4949 solid; }

.border-top-gov-3 { border-top: 3rpx #cd3a38 solid; }

.border-top-primary-3 { border-top: 3rpx #1890ff solid; }

.border-top-success-3 { border-top: 3rpx #52c41a solid; }

.border-top-warning-3 { border-top: 3rpx #faad14 solid; }

.border-left-info-3 { border-left: 3rpx #e8eaec solid; }

.border-left-grey-3 { border-left: 3rpx #dcdee2 solid; }

.border-left-danger-3 { border-left: 3rpx #ff4949 solid; }

.border-left-gov-3 { border-left: 3rpx #cd3a38 solid; }

.border-left-primary-3 { border-left: 3rpx #1890ff solid; }

.border-left-success-3 { border-left: 3rpx #52c41a solid; }

.border-left-warning-3 { border-left: 3rpx #faad14 solid; }

.border-bottom-info-3 { border-bottom: 3rpx #e8eaec solid; }

.border-bottom-grey-3 { border-bottom: 3rpx #dcdee2 solid; }

.border-bottom-danger-3 { border-bottom: 3rpx #ff4949 solid; }

.border-bottom-primary-3 { border-bottom: 3rpx #1890ff solid; }

.border-bottom-success-3 { border-bottom: 3rpx #52c41a solid; }

.border-bottom-warning-3 { border-bottom: 3rpx #faad14 solid; }

.border-bottom-gov-3 { border-bottom: 3rpx #cd3a38 solid; }

.border-right-info-3 { border-right: 3rpx #e8eaec solid; }

.border-right-grey-3 { border-right: 3rpx #dcdee2 solid; }

.border-right-danger-3 { border-right: 3rpx #ff4949 solid; }

.border-right-gov-3 { border-right: 3rpx #cd3a38 solid; }

.border-right-primary-3 { border-right: 3rpx #1890ff solid; }

.border-right-success-3 { border-right: 3rpx #52c41a solid; }

.border-right-warning-3 { border-right: 3rpx #faad14 solid; }

.border-white-4 { border: 4rpx #fff solid; }

.border-info-4 { border: 4rpx #e8eaec solid; }

.border-grey-4 { border: 4rpx #dcdee2 solid; }

.border-danger-4 { border: 4rpx #ff4949 solid; }

.border-primary-4 { border: 4rpx #1890ff solid; }

.border-success-4 { border: 4rpx #52c41a solid; }

.border-warning-4 { border: 4rpx #faad14 solid; }

.border-gov-4 { border: 4rpx #cd3a38 solid; }

.border-top-info-4 { border-top: 4rpx #e8eaec solid; }

.border-top-grey-4 { border-top: 4rpx #dcdee2 solid; }

.border-top-danger-4 { border-top: 4rpx #ff4949 solid; }

.border-top-gov-4 { border-top: 4rpx #cd3a38 solid; }

.border-top-primary-4 { border-top: 4rpx #1890ff solid; }

.border-top-success-4 { border-top: 4rpx #52c41a solid; }

.border-top-warning-4 { border-top: 4rpx #faad14 solid; }

.border-left-info-4 { border-left: 4rpx #e8eaec solid; }

.border-left-grey-4 { border-left: 4rpx #dcdee2 solid; }

.border-left-danger-4 { border-left: 4rpx #ff4949 solid; }

.border-left-gov-4 { border-left: 4rpx #cd3a38 solid; }

.border-left-primary-4 { border-left: 4rpx #1890ff solid; }

.border-left-success-4 { border-left: 4rpx #52c41a solid; }

.border-left-warning-4 { border-left: 4rpx #faad14 solid; }

.border-bottom-info-4 { border-bottom: 4rpx #e8eaec solid; }

.border-bottom-grey-4 { border-bottom: 4rpx #dcdee2 solid; }

.border-bottom-danger-4 { border-bottom: 4rpx #ff4949 solid; }

.border-bottom-primary-4 { border-bottom: 4rpx #1890ff solid; }

.border-bottom-success-4 { border-bottom: 4rpx #52c41a solid; }

.border-bottom-warning-4 { border-bottom: 4rpx #faad14 solid; }

.border-bottom-gov-4 { border-bottom: 4rpx #cd3a38 solid; }

.border-right-info-4 { border-right: 4rpx #e8eaec solid; }

.border-right-grey-4 { border-right: 4rpx #dcdee2 solid; }

.border-right-danger-4 { border-right: 4rpx #ff4949 solid; }

.border-right-gov-4 { border-right: 4rpx #cd3a38 solid; }

.border-right-primary-4 { border-right: 4rpx #1890ff solid; }

.border-right-success-4 { border-right: 4rpx #52c41a solid; }

.border-right-warning-4 { border-right: 4rpx #faad14 solid; }

.border-white-5 { border: 5rpx #fff solid; }

.border-info-5 { border: 5rpx #e8eaec solid; }

.border-grey-5 { border: 5rpx #dcdee2 solid; }

.border-danger-5 { border: 5rpx #ff4949 solid; }

.border-primary-5 { border: 5rpx #1890ff solid; }

.border-success-5 { border: 5rpx #52c41a solid; }

.border-warning-5 { border: 5rpx #faad14 solid; }

.border-gov-5 { border: 5rpx #cd3a38 solid; }

.border-top-info-5 { border-top: 5rpx #e8eaec solid; }

.border-top-grey-5 { border-top: 5rpx #dcdee2 solid; }

.border-top-danger-5 { border-top: 5rpx #ff4949 solid; }

.border-top-gov-5 { border-top: 5rpx #cd3a38 solid; }

.border-top-primary-5 { border-top: 5rpx #1890ff solid; }

.border-top-success-5 { border-top: 5rpx #52c41a solid; }

.border-top-warning-5 { border-top: 5rpx #faad14 solid; }

.border-left-info-5 { border-left: 5rpx #e8eaec solid; }

.border-left-grey-5 { border-left: 5rpx #dcdee2 solid; }

.border-left-danger-5 { border-left: 5rpx #ff4949 solid; }

.border-left-gov-5 { border-left: 5rpx #cd3a38 solid; }

.border-left-primary-5 { border-left: 5rpx #1890ff solid; }

.border-left-success-5 { border-left: 5rpx #52c41a solid; }

.border-left-warning-5 { border-left: 5rpx #faad14 solid; }

.border-bottom-info-5 { border-bottom: 5rpx #e8eaec solid; }

.border-bottom-grey-5 { border-bottom: 5rpx #dcdee2 solid; }

.border-bottom-danger-5 { border-bottom: 5rpx #ff4949 solid; }

.border-bottom-primary-5 { border-bottom: 5rpx #1890ff solid; }

.border-bottom-success-5 { border-bottom: 5rpx #52c41a solid; }

.border-bottom-warning-5 { border-bottom: 5rpx #faad14 solid; }

.border-bottom-gov-5 { border-bottom: 5rpx #cd3a38 solid; }

.border-right-info-5 { border-right: 5rpx #e8eaec solid; }

.border-right-grey-5 { border-right: 5rpx #dcdee2 solid; }

.border-right-danger-5 { border-right: 5rpx #ff4949 solid; }

.border-right-gov-5 { border-right: 5rpx #cd3a38 solid; }

.border-right-primary-5 { border-right: 5rpx #1890ff solid; }

.border-right-success-5 { border-right: 5rpx #52c41a solid; }

.border-right-warning-5 { border-right: 5rpx #faad14 solid; }

.border-white-6 { border: 6rpx #fff solid; }

.border-info-6 { border: 6rpx #e8eaec solid; }

.border-grey-6 { border: 6rpx #dcdee2 solid; }

.border-danger-6 { border: 6rpx #ff4949 solid; }

.border-primary-6 { border: 6rpx #1890ff solid; }

.border-success-6 { border: 6rpx #52c41a solid; }

.border-warning-6 { border: 6rpx #faad14 solid; }

.border-gov-6 { border: 6rpx #cd3a38 solid; }

.border-top-info-6 { border-top: 6rpx #e8eaec solid; }

.border-top-grey-6 { border-top: 6rpx #dcdee2 solid; }

.border-top-danger-6 { border-top: 6rpx #ff4949 solid; }

.border-top-gov-6 { border-top: 6rpx #cd3a38 solid; }

.border-top-primary-6 { border-top: 6rpx #1890ff solid; }

.border-top-success-6 { border-top: 6rpx #52c41a solid; }

.border-top-warning-6 { border-top: 6rpx #faad14 solid; }

.border-left-info-6 { border-left: 6rpx #e8eaec solid; }

.border-left-grey-6 { border-left: 6rpx #dcdee2 solid; }

.border-left-danger-6 { border-left: 6rpx #ff4949 solid; }

.border-left-gov-6 { border-left: 6rpx #cd3a38 solid; }

.border-left-primary-6 { border-left: 6rpx #1890ff solid; }

.border-left-success-6 { border-left: 6rpx #52c41a solid; }

.border-left-warning-6 { border-left: 6rpx #faad14 solid; }

.border-bottom-info-6 { border-bottom: 6rpx #e8eaec solid; }

.border-bottom-grey-6 { border-bottom: 6rpx #dcdee2 solid; }

.border-bottom-danger-6 { border-bottom: 6rpx #ff4949 solid; }

.border-bottom-primary-6 { border-bottom: 6rpx #1890ff solid; }

.border-bottom-success-6 { border-bottom: 6rpx #52c41a solid; }

.border-bottom-warning-6 { border-bottom: 6rpx #faad14 solid; }

.border-bottom-gov-6 { border-bottom: 6rpx #cd3a38 solid; }

.border-right-info-6 { border-right: 6rpx #e8eaec solid; }

.border-right-grey-6 { border-right: 6rpx #dcdee2 solid; }

.border-right-danger-6 { border-right: 6rpx #ff4949 solid; }

.border-right-gov-6 { border-right: 6rpx #cd3a38 solid; }

.border-right-primary-6 { border-right: 6rpx #1890ff solid; }

.border-right-success-6 { border-right: 6rpx #52c41a solid; }

.border-right-warning-6 { border-right: 6rpx #faad14 solid; }

.border-white-7 { border: 7rpx #fff solid; }

.border-info-7 { border: 7rpx #e8eaec solid; }

.border-grey-7 { border: 7rpx #dcdee2 solid; }

.border-danger-7 { border: 7rpx #ff4949 solid; }

.border-primary-7 { border: 7rpx #1890ff solid; }

.border-success-7 { border: 7rpx #52c41a solid; }

.border-warning-7 { border: 7rpx #faad14 solid; }

.border-gov-7 { border: 7rpx #cd3a38 solid; }

.border-top-info-7 { border-top: 7rpx #e8eaec solid; }

.border-top-grey-7 { border-top: 7rpx #dcdee2 solid; }

.border-top-danger-7 { border-top: 7rpx #ff4949 solid; }

.border-top-gov-7 { border-top: 7rpx #cd3a38 solid; }

.border-top-primary-7 { border-top: 7rpx #1890ff solid; }

.border-top-success-7 { border-top: 7rpx #52c41a solid; }

.border-top-warning-7 { border-top: 7rpx #faad14 solid; }

.border-left-info-7 { border-left: 7rpx #e8eaec solid; }

.border-left-grey-7 { border-left: 7rpx #dcdee2 solid; }

.border-left-danger-7 { border-left: 7rpx #ff4949 solid; }

.border-left-gov-7 { border-left: 7rpx #cd3a38 solid; }

.border-left-primary-7 { border-left: 7rpx #1890ff solid; }

.border-left-success-7 { border-left: 7rpx #52c41a solid; }

.border-left-warning-7 { border-left: 7rpx #faad14 solid; }

.border-bottom-info-7 { border-bottom: 7rpx #e8eaec solid; }

.border-bottom-grey-7 { border-bottom: 7rpx #dcdee2 solid; }

.border-bottom-danger-7 { border-bottom: 7rpx #ff4949 solid; }

.border-bottom-primary-7 { border-bottom: 7rpx #1890ff solid; }

.border-bottom-success-7 { border-bottom: 7rpx #52c41a solid; }

.border-bottom-warning-7 { border-bottom: 7rpx #faad14 solid; }

.border-bottom-gov-7 { border-bottom: 7rpx #cd3a38 solid; }

.border-right-info-7 { border-right: 7rpx #e8eaec solid; }

.border-right-grey-7 { border-right: 7rpx #dcdee2 solid; }

.border-right-danger-7 { border-right: 7rpx #ff4949 solid; }

.border-right-gov-7 { border-right: 7rpx #cd3a38 solid; }

.border-right-primary-7 { border-right: 7rpx #1890ff solid; }

.border-right-success-7 { border-right: 7rpx #52c41a solid; }

.border-right-warning-7 { border-right: 7rpx #faad14 solid; }

.border-white-8 { border: 8rpx #fff solid; }

.border-info-8 { border: 8rpx #e8eaec solid; }

.border-grey-8 { border: 8rpx #dcdee2 solid; }

.border-danger-8 { border: 8rpx #ff4949 solid; }

.border-primary-8 { border: 8rpx #1890ff solid; }

.border-success-8 { border: 8rpx #52c41a solid; }

.border-warning-8 { border: 8rpx #faad14 solid; }

.border-gov-8 { border: 8rpx #cd3a38 solid; }

.border-top-info-8 { border-top: 8rpx #e8eaec solid; }

.border-top-grey-8 { border-top: 8rpx #dcdee2 solid; }

.border-top-danger-8 { border-top: 8rpx #ff4949 solid; }

.border-top-gov-8 { border-top: 8rpx #cd3a38 solid; }

.border-top-primary-8 { border-top: 8rpx #1890ff solid; }

.border-top-success-8 { border-top: 8rpx #52c41a solid; }

.border-top-warning-8 { border-top: 8rpx #faad14 solid; }

.border-left-info-8 { border-left: 8rpx #e8eaec solid; }

.border-left-grey-8 { border-left: 8rpx #dcdee2 solid; }

.border-left-danger-8 { border-left: 8rpx #ff4949 solid; }

.border-left-gov-8 { border-left: 8rpx #cd3a38 solid; }

.border-left-primary-8 { border-left: 8rpx #1890ff solid; }

.border-left-success-8 { border-left: 8rpx #52c41a solid; }

.border-left-warning-8 { border-left: 8rpx #faad14 solid; }

.border-bottom-info-8 { border-bottom: 8rpx #e8eaec solid; }

.border-bottom-grey-8 { border-bottom: 8rpx #dcdee2 solid; }

.border-bottom-danger-8 { border-bottom: 8rpx #ff4949 solid; }

.border-bottom-primary-8 { border-bottom: 8rpx #1890ff solid; }

.border-bottom-success-8 { border-bottom: 8rpx #52c41a solid; }

.border-bottom-warning-8 { border-bottom: 8rpx #faad14 solid; }

.border-bottom-gov-8 { border-bottom: 8rpx #cd3a38 solid; }

.border-right-info-8 { border-right: 8rpx #e8eaec solid; }

.border-right-grey-8 { border-right: 8rpx #dcdee2 solid; }

.border-right-danger-8 { border-right: 8rpx #ff4949 solid; }

.border-right-gov-8 { border-right: 8rpx #cd3a38 solid; }

.border-right-primary-8 { border-right: 8rpx #1890ff solid; }

.border-right-success-8 { border-right: 8rpx #52c41a solid; }

.border-right-warning-8 { border-right: 8rpx #faad14 solid; }

.border-white-9 { border: 9rpx #fff solid; }

.border-info-9 { border: 9rpx #e8eaec solid; }

.border-grey-9 { border: 9rpx #dcdee2 solid; }

.border-danger-9 { border: 9rpx #ff4949 solid; }

.border-primary-9 { border: 9rpx #1890ff solid; }

.border-success-9 { border: 9rpx #52c41a solid; }

.border-warning-9 { border: 9rpx #faad14 solid; }

.border-gov-9 { border: 9rpx #cd3a38 solid; }

.border-top-info-9 { border-top: 9rpx #e8eaec solid; }

.border-top-grey-9 { border-top: 9rpx #dcdee2 solid; }

.border-top-danger-9 { border-top: 9rpx #ff4949 solid; }

.border-top-gov-9 { border-top: 9rpx #cd3a38 solid; }

.border-top-primary-9 { border-top: 9rpx #1890ff solid; }

.border-top-success-9 { border-top: 9rpx #52c41a solid; }

.border-top-warning-9 { border-top: 9rpx #faad14 solid; }

.border-left-info-9 { border-left: 9rpx #e8eaec solid; }

.border-left-grey-9 { border-left: 9rpx #dcdee2 solid; }

.border-left-danger-9 { border-left: 9rpx #ff4949 solid; }

.border-left-gov-9 { border-left: 9rpx #cd3a38 solid; }

.border-left-primary-9 { border-left: 9rpx #1890ff solid; }

.border-left-success-9 { border-left: 9rpx #52c41a solid; }

.border-left-warning-9 { border-left: 9rpx #faad14 solid; }

.border-bottom-info-9 { border-bottom: 9rpx #e8eaec solid; }

.border-bottom-grey-9 { border-bottom: 9rpx #dcdee2 solid; }

.border-bottom-danger-9 { border-bottom: 9rpx #ff4949 solid; }

.border-bottom-primary-9 { border-bottom: 9rpx #1890ff solid; }

.border-bottom-success-9 { border-bottom: 9rpx #52c41a solid; }

.border-bottom-warning-9 { border-bottom: 9rpx #faad14 solid; }

.border-bottom-gov-9 { border-bottom: 9rpx #cd3a38 solid; }

.border-right-info-9 { border-right: 9rpx #e8eaec solid; }

.border-right-grey-9 { border-right: 9rpx #dcdee2 solid; }

.border-right-danger-9 { border-right: 9rpx #ff4949 solid; }

.border-right-gov-9 { border-right: 9rpx #cd3a38 solid; }

.border-right-primary-9 { border-right: 9rpx #1890ff solid; }

.border-right-success-9 { border-right: 9rpx #52c41a solid; }

.border-right-warning-9 { border-right: 9rpx #faad14 solid; }

.border-white-10 { border: 10rpx #fff solid; }

.border-info-10 { border: 10rpx #e8eaec solid; }

.border-grey-10 { border: 10rpx #dcdee2 solid; }

.border-danger-10 { border: 10rpx #ff4949 solid; }

.border-primary-10 { border: 10rpx #1890ff solid; }

.border-success-10 { border: 10rpx #52c41a solid; }

.border-warning-10 { border: 10rpx #faad14 solid; }

.border-gov-10 { border: 10rpx #cd3a38 solid; }

.border-top-info-10 { border-top: 10rpx #e8eaec solid; }

.border-top-grey-10 { border-top: 10rpx #dcdee2 solid; }

.border-top-danger-10 { border-top: 10rpx #ff4949 solid; }

.border-top-gov-10 { border-top: 10rpx #cd3a38 solid; }

.border-top-primary-10 { border-top: 10rpx #1890ff solid; }

.border-top-success-10 { border-top: 10rpx #52c41a solid; }

.border-top-warning-10 { border-top: 10rpx #faad14 solid; }

.border-left-info-10 { border-left: 10rpx #e8eaec solid; }

.border-left-grey-10 { border-left: 10rpx #dcdee2 solid; }

.border-left-danger-10 { border-left: 10rpx #ff4949 solid; }

.border-left-gov-10 { border-left: 10rpx #cd3a38 solid; }

.border-left-primary-10 { border-left: 10rpx #1890ff solid; }

.border-left-success-10 { border-left: 10rpx #52c41a solid; }

.border-left-warning-10 { border-left: 10rpx #faad14 solid; }

.border-bottom-info-10 { border-bottom: 10rpx #e8eaec solid; }

.border-bottom-grey-10 { border-bottom: 10rpx #dcdee2 solid; }

.border-bottom-danger-10 { border-bottom: 10rpx #ff4949 solid; }

.border-bottom-primary-10 { border-bottom: 10rpx #1890ff solid; }

.border-bottom-success-10 { border-bottom: 10rpx #52c41a solid; }

.border-bottom-warning-10 { border-bottom: 10rpx #faad14 solid; }

.border-bottom-gov-10 { border-bottom: 10rpx #cd3a38 solid; }

.border-right-info-10 { border-right: 10rpx #e8eaec solid; }

.border-right-grey-10 { border-right: 10rpx #dcdee2 solid; }

.border-right-danger-10 { border-right: 10rpx #ff4949 solid; }

.border-right-gov-10 { border-right: 10rpx #cd3a38 solid; }

.border-right-primary-10 { border-right: 10rpx #1890ff solid; }

.border-right-success-10 { border-right: 10rpx #52c41a solid; }

.border-right-warning-10 { border-right: 10rpx #faad14 solid; }

/* 移动端实现加粗 */
.weight { text-shadow: 0px 0px #333; }

.hr { width: 100%; height: 1px; clear: both; }

.hr { position: relative; border-bottom: 0 !important; }

.line1 { position: relative; border-bottom: 0 !important; }

.hr:after { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; border-bottom: 1px solid #eee; -webkit-transform: scaleY(0.5); transform: scaleY(0.5); }

.line1:after { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; border-bottom: 1px solid #eee; -webkit-transform: scaleY(0.5); transform: scaleY(0.5); }

.border1 { position: relative; }

.border1::before { width: 200%; height: 200%; border: 1px solid #eee; transform: scale(0.5); content: ""; position: absolute; top: 0px; right: 0px; transform-origin: left top; box-sizing: border-box; pointer-events: none; }

.line-top { position: relative; border-top: 0 !important; }

.line-top:after { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; border-top: 1px solid #eee; -webkit-transform: scaleY(0.5); transform: scaleY(0.5); }

.uni-body, .my-app-page { max-width: 960px; margin: 0 auto; }

body, html { -webkit-overflow-scrolling: touch; }

/** media 变更适配 */
/* #ifndef MP */
@media (max-width: 767px) { .w-xs-0, .h-xs-0 { display: hidden; } }

@media (min-width: 768px) and (max-width: 991px) { .w-sm-0, .h-sm-0 { display: hidden; } }

@media (min-width: 992px) and (max-width: 1199px) { .w-md-0, .h-md-0 { display: hidden; } }

@media (min-width: 1200px) { .w-lg-0, .h-lg-0 { display: hidden; } }

/* #endif */
.o-0 { opacity: calc(0/ 10); }

.o-1 { opacity: calc(1/ 10); }

.o-2 { opacity: calc(2/ 10); }

.o-3 { opacity: calc(3/ 10); }

.o-4 { opacity: calc(4/ 10); }

.o-5 { opacity: calc(5/ 10); }

.o-6 { opacity: calc(6/ 10); }

.o-7 { opacity: calc(7/ 10); }

.o-8 { opacity: calc(8/ 10); }

.o-9 { opacity: calc(9/ 10); }

.w-1400 { width: 1400rpx; }

.h-1400 { height: 1400rpx; }

.h-max-1400 { max-height: 1400rpx; }

.w-max-1400 { max-width: 1400rpx; }

.w-min-1400 { min-width: 1400rpx; }

.h-min-1400 { min-height: 1400rpx; }

.w-1300 { width: 1300rpx; }

.h-1300 { height: 1300rpx; }

.h-max-1300 { max-height: 1300rpx; }

.w-max-1300 { max-width: 1300rpx; }

.w-min-1300 { min-width: 1300rpx; }

.h-min-1300 { min-height: 1300rpx; }

.w-1200 { width: 1200rpx; }

.h-1200 { height: 1200rpx; }

.h-max-1200 { max-height: 1200rpx; }

.w-max-1200 { max-width: 1200rpx; }

.w-min-1200 { min-width: 1200rpx; }

.h-min-1200 { min-height: 1200rpx; }

.w-1100 { width: 1100rpx; }

.h-1100 { height: 1100rpx; }

.h-max-1100 { max-height: 1100rpx; }

.w-max-1100 { max-width: 1100rpx; }

.w-min-1100 { min-width: 1100rpx; }

.h-min-1100 { min-height: 1100rpx; }

.w-1000 { width: 1000rpx; }

.h-1000 { height: 1000rpx; }

.h-max-1000 { max-height: 1000rpx; }

.w-max-1000 { max-width: 1000rpx; }

.w-min-1000 { min-width: 1000rpx; }

.h-min-1000 { min-height: 1000rpx; }

.w-992 { width: 992rpx; }

.h-992 { height: 992rpx; }

.h-max-992 { max-height: 992rpx; }

.w-max-992 { max-width: 992rpx; }

.w-min-992 { min-width: 992rpx; }

.h-min-992 { min-height: 992rpx; }

.w-960 { width: 960rpx; }

.h-960 { height: 960rpx; }

.h-max-960 { max-height: 960rpx; }

.w-max-960 { max-width: 960rpx; }

.w-min-960 { min-width: 960rpx; }

.h-min-960 { min-height: 960rpx; }

.w-900 { width: 900rpx; }

.h-900 { height: 900rpx; }

.h-max-900 { max-height: 900rpx; }

.w-max-900 { max-width: 900rpx; }

.w-min-900 { min-width: 900rpx; }

.h-min-900 { min-height: 900rpx; }

.w-768 { width: 768rpx; }

.h-768 { height: 768rpx; }

.h-max-768 { max-height: 768rpx; }

.w-max-768 { max-width: 768rpx; }

.w-min-768 { min-width: 768rpx; }

.h-min-768 { min-height: 768rpx; }

.w-750 { width: 750rpx; }

.h-750 { height: 750rpx; }

.h-max-750 { max-height: 750rpx; }

.w-max-750 { max-width: 750rpx; }

.w-min-750 { min-width: 750rpx; }

.h-min-750 { min-height: 750rpx; }

.w-800 { width: 800rpx; }

.h-800 { height: 800rpx; }

.h-max-800 { max-height: 800rpx; }

.w-max-800 { max-width: 800rpx; }

.w-min-800 { min-width: 800rpx; }

.h-min-800 { min-height: 800rpx; }

.w-600 { width: 600rpx; }

.h-600 { height: 600rpx; }

.h-max-600 { max-height: 600rpx; }

.w-max-600 { max-width: 600rpx; }

.w-min-600 { min-width: 600rpx; }

.h-min-600 { min-height: 600rpx; }

.w-500 { width: 500rpx; }

.h-500 { height: 500rpx; }

.h-max-500 { max-height: 500rpx; }

.w-max-500 { max-width: 500rpx; }

.w-min-500 { min-width: 500rpx; }

.h-min-500 { min-height: 500rpx; }

.w-450 { width: 450rpx; }

.h-450 { height: 450rpx; }

.h-max-450 { max-height: 450rpx; }

.w-max-450 { max-width: 450rpx; }

.w-min-450 { min-width: 450rpx; }

.h-min-450 { min-height: 450rpx; }

.w-400 { width: 400rpx; }

.h-400 { height: 400rpx; }

.h-max-400 { max-height: 400rpx; }

.w-max-400 { max-width: 400rpx; }

.w-min-400 { min-width: 400rpx; }

.h-min-400 { min-height: 400rpx; }

.w-375 { width: 375rpx; }

.h-375 { height: 375rpx; }

.h-max-375 { max-height: 375rpx; }

.w-max-375 { max-width: 375rpx; }

.w-min-375 { min-width: 375rpx; }

.h-min-375 { min-height: 375rpx; }

.w-350 { width: 350rpx; }

.h-350 { height: 350rpx; }

.h-max-350 { max-height: 350rpx; }

.w-max-350 { max-width: 350rpx; }

.w-min-350 { min-width: 350rpx; }

.h-min-350 { min-height: 350rpx; }

.w-320 { width: 320rpx; }

.h-320 { height: 320rpx; }

.h-max-320 { max-height: 320rpx; }

.w-max-320 { max-width: 320rpx; }

.w-min-320 { min-width: 320rpx; }

.h-min-320 { min-height: 320rpx; }

.w-300 { width: 300rpx; }

.h-300 { height: 300rpx; }

.h-max-300 { max-height: 300rpx; }

.w-max-300 { max-width: 300rpx; }

.w-min-300 { min-width: 300rpx; }

.h-min-300 { min-height: 300rpx; }

.w-310 { width: 310rpx; }

.h-310 { height: 310rpx; }

.h-max-310 { max-height: 310rpx; }

.w-max-310 { max-width: 310rpx; }

.w-min-310 { min-width: 310rpx; }

.h-min-310 { min-height: 310rpx; }

.w-290 { width: 290rpx; }

.h-290 { height: 290rpx; }

.h-max-290 { max-height: 290rpx; }

.w-max-290 { max-width: 290rpx; }

.w-min-290 { min-width: 290rpx; }

.h-min-290 { min-height: 290rpx; }

.w-260 { width: 260rpx; }

.h-260 { height: 260rpx; }

.h-max-260 { max-height: 260rpx; }

.w-max-260 { max-width: 260rpx; }

.w-min-260 { min-width: 260rpx; }

.h-min-260 { min-height: 260rpx; }

.w-250 { width: 250rpx; }

.h-250 { height: 250rpx; }

.h-max-250 { max-height: 250rpx; }

.w-max-250 { max-width: 250rpx; }

.w-min-250 { min-width: 250rpx; }

.h-min-250 { min-height: 250rpx; }

.w-200 { width: 200rpx; }

.h-200 { height: 200rpx; }

.h-max-200 { max-height: 200rpx; }

.w-max-200 { max-width: 200rpx; }

.w-min-200 { min-width: 200rpx; }

.h-min-200 { min-height: 200rpx; }

.w-190 { width: 190rpx; }

.h-190 { height: 190rpx; }

.h-max-190 { max-height: 190rpx; }

.w-max-190 { max-width: 190rpx; }

.w-min-190 { min-width: 190rpx; }

.h-min-190 { min-height: 190rpx; }

.w-180 { width: 180rpx; }

.h-180 { height: 180rpx; }

.h-max-180 { max-height: 180rpx; }

.w-max-180 { max-width: 180rpx; }

.w-min-180 { min-width: 180rpx; }

.h-min-180 { min-height: 180rpx; }

.w-160 { width: 160rpx; }

.h-160 { height: 160rpx; }

.h-max-160 { max-height: 160rpx; }

.w-max-160 { max-width: 160rpx; }

.w-min-160 { min-width: 160rpx; }

.h-min-160 { min-height: 160rpx; }

.w-30 { width: 30rpx; }

.h-30 { height: 30rpx; }

.w-max-30 { max-width: 30rpx; }

.h-max-30 { max-height: 30rpx; }

.w-min-30 { min-width: 30rpx; }

.h-min-30 { min-height: 30rpx; }

.w-31 { width: 31rpx; }

.h-31 { height: 31rpx; }

.w-max-31 { max-width: 31rpx; }

.h-max-31 { max-height: 31rpx; }

.w-min-31 { min-width: 31rpx; }

.h-min-31 { min-height: 31rpx; }

.w-32 { width: 32rpx; }

.h-32 { height: 32rpx; }

.w-max-32 { max-width: 32rpx; }

.h-max-32 { max-height: 32rpx; }

.w-min-32 { min-width: 32rpx; }

.h-min-32 { min-height: 32rpx; }

.w-33 { width: 33rpx; }

.h-33 { height: 33rpx; }

.w-max-33 { max-width: 33rpx; }

.h-max-33 { max-height: 33rpx; }

.w-min-33 { min-width: 33rpx; }

.h-min-33 { min-height: 33rpx; }

.w-34 { width: 34rpx; }

.h-34 { height: 34rpx; }

.w-max-34 { max-width: 34rpx; }

.h-max-34 { max-height: 34rpx; }

.w-min-34 { min-width: 34rpx; }

.h-min-34 { min-height: 34rpx; }

.w-35 { width: 35rpx; }

.h-35 { height: 35rpx; }

.w-max-35 { max-width: 35rpx; }

.h-max-35 { max-height: 35rpx; }

.w-min-35 { min-width: 35rpx; }

.h-min-35 { min-height: 35rpx; }

.w-36 { width: 36rpx; }

.h-36 { height: 36rpx; }

.w-max-36 { max-width: 36rpx; }

.h-max-36 { max-height: 36rpx; }

.w-min-36 { min-width: 36rpx; }

.h-min-36 { min-height: 36rpx; }

.w-37 { width: 37rpx; }

.h-37 { height: 37rpx; }

.w-max-37 { max-width: 37rpx; }

.h-max-37 { max-height: 37rpx; }

.w-min-37 { min-width: 37rpx; }

.h-min-37 { min-height: 37rpx; }

.w-38 { width: 38rpx; }

.h-38 { height: 38rpx; }

.w-max-38 { max-width: 38rpx; }

.h-max-38 { max-height: 38rpx; }

.w-min-38 { min-width: 38rpx; }

.h-min-38 { min-height: 38rpx; }

.w-39 { width: 39rpx; }

.h-39 { height: 39rpx; }

.w-max-39 { max-width: 39rpx; }

.h-max-39 { max-height: 39rpx; }

.w-min-39 { min-width: 39rpx; }

.h-min-39 { min-height: 39rpx; }

.w-40 { width: 40rpx; }

.h-40 { height: 40rpx; }

.w-max-40 { max-width: 40rpx; }

.h-max-40 { max-height: 40rpx; }

.w-min-40 { min-width: 40rpx; }

.h-min-40 { min-height: 40rpx; }

.w-41 { width: 41rpx; }

.h-41 { height: 41rpx; }

.w-max-41 { max-width: 41rpx; }

.h-max-41 { max-height: 41rpx; }

.w-min-41 { min-width: 41rpx; }

.h-min-41 { min-height: 41rpx; }

.w-42 { width: 42rpx; }

.h-42 { height: 42rpx; }

.w-max-42 { max-width: 42rpx; }

.h-max-42 { max-height: 42rpx; }

.w-min-42 { min-width: 42rpx; }

.h-min-42 { min-height: 42rpx; }

.w-43 { width: 43rpx; }

.h-43 { height: 43rpx; }

.w-max-43 { max-width: 43rpx; }

.h-max-43 { max-height: 43rpx; }

.w-min-43 { min-width: 43rpx; }

.h-min-43 { min-height: 43rpx; }

.w-44 { width: 44rpx; }

.h-44 { height: 44rpx; }

.w-max-44 { max-width: 44rpx; }

.h-max-44 { max-height: 44rpx; }

.w-min-44 { min-width: 44rpx; }

.h-min-44 { min-height: 44rpx; }

.w-45 { width: 45rpx; }

.h-45 { height: 45rpx; }

.w-max-45 { max-width: 45rpx; }

.h-max-45 { max-height: 45rpx; }

.w-min-45 { min-width: 45rpx; }

.h-min-45 { min-height: 45rpx; }

.w-46 { width: 46rpx; }

.h-46 { height: 46rpx; }

.w-max-46 { max-width: 46rpx; }

.h-max-46 { max-height: 46rpx; }

.w-min-46 { min-width: 46rpx; }

.h-min-46 { min-height: 46rpx; }

.w-47 { width: 47rpx; }

.h-47 { height: 47rpx; }

.w-max-47 { max-width: 47rpx; }

.h-max-47 { max-height: 47rpx; }

.w-min-47 { min-width: 47rpx; }

.h-min-47 { min-height: 47rpx; }

.w-48 { width: 48rpx; }

.h-48 { height: 48rpx; }

.w-max-48 { max-width: 48rpx; }

.h-max-48 { max-height: 48rpx; }

.w-min-48 { min-width: 48rpx; }

.h-min-48 { min-height: 48rpx; }

.w-49 { width: 49rpx; }

.h-49 { height: 49rpx; }

.w-max-49 { max-width: 49rpx; }

.h-max-49 { max-height: 49rpx; }

.w-min-49 { min-width: 49rpx; }

.h-min-49 { min-height: 49rpx; }

.w-50 { width: 50rpx; }

.h-50 { height: 50rpx; }

.w-max-50 { max-width: 50rpx; }

.h-max-50 { max-height: 50rpx; }

.w-min-50 { min-width: 50rpx; }

.h-min-50 { min-height: 50rpx; }

.w-51 { width: 51rpx; }

.h-51 { height: 51rpx; }

.w-max-51 { max-width: 51rpx; }

.h-max-51 { max-height: 51rpx; }

.w-min-51 { min-width: 51rpx; }

.h-min-51 { min-height: 51rpx; }

.w-52 { width: 52rpx; }

.h-52 { height: 52rpx; }

.w-max-52 { max-width: 52rpx; }

.h-max-52 { max-height: 52rpx; }

.w-min-52 { min-width: 52rpx; }

.h-min-52 { min-height: 52rpx; }

.w-53 { width: 53rpx; }

.h-53 { height: 53rpx; }

.w-max-53 { max-width: 53rpx; }

.h-max-53 { max-height: 53rpx; }

.w-min-53 { min-width: 53rpx; }

.h-min-53 { min-height: 53rpx; }

.w-54 { width: 54rpx; }

.h-54 { height: 54rpx; }

.w-max-54 { max-width: 54rpx; }

.h-max-54 { max-height: 54rpx; }

.w-min-54 { min-width: 54rpx; }

.h-min-54 { min-height: 54rpx; }

.w-55 { width: 55rpx; }

.h-55 { height: 55rpx; }

.w-max-55 { max-width: 55rpx; }

.h-max-55 { max-height: 55rpx; }

.w-min-55 { min-width: 55rpx; }

.h-min-55 { min-height: 55rpx; }

.w-56 { width: 56rpx; }

.h-56 { height: 56rpx; }

.w-max-56 { max-width: 56rpx; }

.h-max-56 { max-height: 56rpx; }

.w-min-56 { min-width: 56rpx; }

.h-min-56 { min-height: 56rpx; }

.w-57 { width: 57rpx; }

.h-57 { height: 57rpx; }

.w-max-57 { max-width: 57rpx; }

.h-max-57 { max-height: 57rpx; }

.w-min-57 { min-width: 57rpx; }

.h-min-57 { min-height: 57rpx; }

.w-58 { width: 58rpx; }

.h-58 { height: 58rpx; }

.w-max-58 { max-width: 58rpx; }

.h-max-58 { max-height: 58rpx; }

.w-min-58 { min-width: 58rpx; }

.h-min-58 { min-height: 58rpx; }

.w-59 { width: 59rpx; }

.h-59 { height: 59rpx; }

.w-max-59 { max-width: 59rpx; }

.h-max-59 { max-height: 59rpx; }

.w-min-59 { min-width: 59rpx; }

.h-min-59 { min-height: 59rpx; }

.w-60 { width: 60rpx; }

.h-60 { height: 60rpx; }

.w-max-60 { max-width: 60rpx; }

.h-max-60 { max-height: 60rpx; }

.w-min-60 { min-width: 60rpx; }

.h-min-60 { min-height: 60rpx; }

.w-61 { width: 61rpx; }

.h-61 { height: 61rpx; }

.w-max-61 { max-width: 61rpx; }

.h-max-61 { max-height: 61rpx; }

.w-min-61 { min-width: 61rpx; }

.h-min-61 { min-height: 61rpx; }

.w-62 { width: 62rpx; }

.h-62 { height: 62rpx; }

.w-max-62 { max-width: 62rpx; }

.h-max-62 { max-height: 62rpx; }

.w-min-62 { min-width: 62rpx; }

.h-min-62 { min-height: 62rpx; }

.w-63 { width: 63rpx; }

.h-63 { height: 63rpx; }

.w-max-63 { max-width: 63rpx; }

.h-max-63 { max-height: 63rpx; }

.w-min-63 { min-width: 63rpx; }

.h-min-63 { min-height: 63rpx; }

.w-64 { width: 64rpx; }

.h-64 { height: 64rpx; }

.w-max-64 { max-width: 64rpx; }

.h-max-64 { max-height: 64rpx; }

.w-min-64 { min-width: 64rpx; }

.h-min-64 { min-height: 64rpx; }

.w-65 { width: 65rpx; }

.h-65 { height: 65rpx; }

.w-max-65 { max-width: 65rpx; }

.h-max-65 { max-height: 65rpx; }

.w-min-65 { min-width: 65rpx; }

.h-min-65 { min-height: 65rpx; }

.w-66 { width: 66rpx; }

.h-66 { height: 66rpx; }

.w-max-66 { max-width: 66rpx; }

.h-max-66 { max-height: 66rpx; }

.w-min-66 { min-width: 66rpx; }

.h-min-66 { min-height: 66rpx; }

.w-67 { width: 67rpx; }

.h-67 { height: 67rpx; }

.w-max-67 { max-width: 67rpx; }

.h-max-67 { max-height: 67rpx; }

.w-min-67 { min-width: 67rpx; }

.h-min-67 { min-height: 67rpx; }

.w-68 { width: 68rpx; }

.h-68 { height: 68rpx; }

.w-max-68 { max-width: 68rpx; }

.h-max-68 { max-height: 68rpx; }

.w-min-68 { min-width: 68rpx; }

.h-min-68 { min-height: 68rpx; }

.w-69 { width: 69rpx; }

.h-69 { height: 69rpx; }

.w-max-69 { max-width: 69rpx; }

.h-max-69 { max-height: 69rpx; }

.w-min-69 { min-width: 69rpx; }

.h-min-69 { min-height: 69rpx; }

.w-70 { width: 70rpx; }

.h-70 { height: 70rpx; }

.w-max-70 { max-width: 70rpx; }

.h-max-70 { max-height: 70rpx; }

.w-min-70 { min-width: 70rpx; }

.h-min-70 { min-height: 70rpx; }

.w-71 { width: 71rpx; }

.h-71 { height: 71rpx; }

.w-max-71 { max-width: 71rpx; }

.h-max-71 { max-height: 71rpx; }

.w-min-71 { min-width: 71rpx; }

.h-min-71 { min-height: 71rpx; }

.w-72 { width: 72rpx; }

.h-72 { height: 72rpx; }

.w-max-72 { max-width: 72rpx; }

.h-max-72 { max-height: 72rpx; }

.w-min-72 { min-width: 72rpx; }

.h-min-72 { min-height: 72rpx; }

.w-73 { width: 73rpx; }

.h-73 { height: 73rpx; }

.w-max-73 { max-width: 73rpx; }

.h-max-73 { max-height: 73rpx; }

.w-min-73 { min-width: 73rpx; }

.h-min-73 { min-height: 73rpx; }

.w-74 { width: 74rpx; }

.h-74 { height: 74rpx; }

.w-max-74 { max-width: 74rpx; }

.h-max-74 { max-height: 74rpx; }

.w-min-74 { min-width: 74rpx; }

.h-min-74 { min-height: 74rpx; }

.w-75 { width: 75rpx; }

.h-75 { height: 75rpx; }

.w-max-75 { max-width: 75rpx; }

.h-max-75 { max-height: 75rpx; }

.w-min-75 { min-width: 75rpx; }

.h-min-75 { min-height: 75rpx; }

.w-76 { width: 76rpx; }

.h-76 { height: 76rpx; }

.w-max-76 { max-width: 76rpx; }

.h-max-76 { max-height: 76rpx; }

.w-min-76 { min-width: 76rpx; }

.h-min-76 { min-height: 76rpx; }

.w-77 { width: 77rpx; }

.h-77 { height: 77rpx; }

.w-max-77 { max-width: 77rpx; }

.h-max-77 { max-height: 77rpx; }

.w-min-77 { min-width: 77rpx; }

.h-min-77 { min-height: 77rpx; }

.w-78 { width: 78rpx; }

.h-78 { height: 78rpx; }

.w-max-78 { max-width: 78rpx; }

.h-max-78 { max-height: 78rpx; }

.w-min-78 { min-width: 78rpx; }

.h-min-78 { min-height: 78rpx; }

.w-79 { width: 79rpx; }

.h-79 { height: 79rpx; }

.w-max-79 { max-width: 79rpx; }

.h-max-79 { max-height: 79rpx; }

.w-min-79 { min-width: 79rpx; }

.h-min-79 { min-height: 79rpx; }

.w-80 { width: 80rpx; }

.h-80 { height: 80rpx; }

.w-max-80 { max-width: 80rpx; }

.h-max-80 { max-height: 80rpx; }

.w-min-80 { min-width: 80rpx; }

.h-min-80 { min-height: 80rpx; }

.w-81 { width: 81rpx; }

.h-81 { height: 81rpx; }

.w-max-81 { max-width: 81rpx; }

.h-max-81 { max-height: 81rpx; }

.w-min-81 { min-width: 81rpx; }

.h-min-81 { min-height: 81rpx; }

.w-82 { width: 82rpx; }

.h-82 { height: 82rpx; }

.w-max-82 { max-width: 82rpx; }

.h-max-82 { max-height: 82rpx; }

.w-min-82 { min-width: 82rpx; }

.h-min-82 { min-height: 82rpx; }

.w-83 { width: 83rpx; }

.h-83 { height: 83rpx; }

.w-max-83 { max-width: 83rpx; }

.h-max-83 { max-height: 83rpx; }

.w-min-83 { min-width: 83rpx; }

.h-min-83 { min-height: 83rpx; }

.w-84 { width: 84rpx; }

.h-84 { height: 84rpx; }

.w-max-84 { max-width: 84rpx; }

.h-max-84 { max-height: 84rpx; }

.w-min-84 { min-width: 84rpx; }

.h-min-84 { min-height: 84rpx; }

.w-85 { width: 85rpx; }

.h-85 { height: 85rpx; }

.w-max-85 { max-width: 85rpx; }

.h-max-85 { max-height: 85rpx; }

.w-min-85 { min-width: 85rpx; }

.h-min-85 { min-height: 85rpx; }

.w-86 { width: 86rpx; }

.h-86 { height: 86rpx; }

.w-max-86 { max-width: 86rpx; }

.h-max-86 { max-height: 86rpx; }

.w-min-86 { min-width: 86rpx; }

.h-min-86 { min-height: 86rpx; }

.w-87 { width: 87rpx; }

.h-87 { height: 87rpx; }

.w-max-87 { max-width: 87rpx; }

.h-max-87 { max-height: 87rpx; }

.w-min-87 { min-width: 87rpx; }

.h-min-87 { min-height: 87rpx; }

.w-88 { width: 88rpx; }

.h-88 { height: 88rpx; }

.w-max-88 { max-width: 88rpx; }

.h-max-88 { max-height: 88rpx; }

.w-min-88 { min-width: 88rpx; }

.h-min-88 { min-height: 88rpx; }

.w-89 { width: 89rpx; }

.h-89 { height: 89rpx; }

.w-max-89 { max-width: 89rpx; }

.h-max-89 { max-height: 89rpx; }

.w-min-89 { min-width: 89rpx; }

.h-min-89 { min-height: 89rpx; }

.w-90 { width: 90rpx; }

.h-90 { height: 90rpx; }

.w-max-90 { max-width: 90rpx; }

.h-max-90 { max-height: 90rpx; }

.w-min-90 { min-width: 90rpx; }

.h-min-90 { min-height: 90rpx; }

.w-91 { width: 91rpx; }

.h-91 { height: 91rpx; }

.w-max-91 { max-width: 91rpx; }

.h-max-91 { max-height: 91rpx; }

.w-min-91 { min-width: 91rpx; }

.h-min-91 { min-height: 91rpx; }

.w-92 { width: 92rpx; }

.h-92 { height: 92rpx; }

.w-max-92 { max-width: 92rpx; }

.h-max-92 { max-height: 92rpx; }

.w-min-92 { min-width: 92rpx; }

.h-min-92 { min-height: 92rpx; }

.w-93 { width: 93rpx; }

.h-93 { height: 93rpx; }

.w-max-93 { max-width: 93rpx; }

.h-max-93 { max-height: 93rpx; }

.w-min-93 { min-width: 93rpx; }

.h-min-93 { min-height: 93rpx; }

.w-94 { width: 94rpx; }

.h-94 { height: 94rpx; }

.w-max-94 { max-width: 94rpx; }

.h-max-94 { max-height: 94rpx; }

.w-min-94 { min-width: 94rpx; }

.h-min-94 { min-height: 94rpx; }

.w-95 { width: 95rpx; }

.h-95 { height: 95rpx; }

.w-max-95 { max-width: 95rpx; }

.h-max-95 { max-height: 95rpx; }

.w-min-95 { min-width: 95rpx; }

.h-min-95 { min-height: 95rpx; }

.w-96 { width: 96rpx; }

.h-96 { height: 96rpx; }

.w-max-96 { max-width: 96rpx; }

.h-max-96 { max-height: 96rpx; }

.w-min-96 { min-width: 96rpx; }

.h-min-96 { min-height: 96rpx; }

.w-97 { width: 97rpx; }

.h-97 { height: 97rpx; }

.w-max-97 { max-width: 97rpx; }

.h-max-97 { max-height: 97rpx; }

.w-min-97 { min-width: 97rpx; }

.h-min-97 { min-height: 97rpx; }

.w-98 { width: 98rpx; }

.h-98 { height: 98rpx; }

.w-max-98 { max-width: 98rpx; }

.h-max-98 { max-height: 98rpx; }

.w-min-98 { min-width: 98rpx; }

.h-min-98 { min-height: 98rpx; }

.w-99 { width: 99rpx; }

.h-99 { height: 99rpx; }

.w-max-99 { max-width: 99rpx; }

.h-max-99 { max-height: 99rpx; }

.w-min-99 { min-width: 99rpx; }

.h-min-99 { min-height: 99rpx; }

.w-100 { width: 100rpx; }

.h-100 { height: 100rpx; }

.w-max-100 { max-width: 100rpx; }

.h-max-100 { max-height: 100rpx; }

.w-min-100 { min-width: 100rpx; }

.h-min-100 { min-height: 100rpx; }

.w-101 { width: 101rpx; }

.h-101 { height: 101rpx; }

.w-max-101 { max-width: 101rpx; }

.h-max-101 { max-height: 101rpx; }

.w-min-101 { min-width: 101rpx; }

.h-min-101 { min-height: 101rpx; }

.w-102 { width: 102rpx; }

.h-102 { height: 102rpx; }

.w-max-102 { max-width: 102rpx; }

.h-max-102 { max-height: 102rpx; }

.w-min-102 { min-width: 102rpx; }

.h-min-102 { min-height: 102rpx; }

.w-103 { width: 103rpx; }

.h-103 { height: 103rpx; }

.w-max-103 { max-width: 103rpx; }

.h-max-103 { max-height: 103rpx; }

.w-min-103 { min-width: 103rpx; }

.h-min-103 { min-height: 103rpx; }

.w-104 { width: 104rpx; }

.h-104 { height: 104rpx; }

.w-max-104 { max-width: 104rpx; }

.h-max-104 { max-height: 104rpx; }

.w-min-104 { min-width: 104rpx; }

.h-min-104 { min-height: 104rpx; }

.w-105 { width: 105rpx; }

.h-105 { height: 105rpx; }

.w-max-105 { max-width: 105rpx; }

.h-max-105 { max-height: 105rpx; }

.w-min-105 { min-width: 105rpx; }

.h-min-105 { min-height: 105rpx; }

.w-106 { width: 106rpx; }

.h-106 { height: 106rpx; }

.w-max-106 { max-width: 106rpx; }

.h-max-106 { max-height: 106rpx; }

.w-min-106 { min-width: 106rpx; }

.h-min-106 { min-height: 106rpx; }

.w-107 { width: 107rpx; }

.h-107 { height: 107rpx; }

.w-max-107 { max-width: 107rpx; }

.h-max-107 { max-height: 107rpx; }

.w-min-107 { min-width: 107rpx; }

.h-min-107 { min-height: 107rpx; }

.w-108 { width: 108rpx; }

.h-108 { height: 108rpx; }

.w-max-108 { max-width: 108rpx; }

.h-max-108 { max-height: 108rpx; }

.w-min-108 { min-width: 108rpx; }

.h-min-108 { min-height: 108rpx; }

.w-109 { width: 109rpx; }

.h-109 { height: 109rpx; }

.w-max-109 { max-width: 109rpx; }

.h-max-109 { max-height: 109rpx; }

.w-min-109 { min-width: 109rpx; }

.h-min-109 { min-height: 109rpx; }

.w-110 { width: 110rpx; }

.h-110 { height: 110rpx; }

.w-max-110 { max-width: 110rpx; }

.h-max-110 { max-height: 110rpx; }

.w-min-110 { min-width: 110rpx; }

.h-min-110 { min-height: 110rpx; }

.w-111 { width: 111rpx; }

.h-111 { height: 111rpx; }

.w-max-111 { max-width: 111rpx; }

.h-max-111 { max-height: 111rpx; }

.w-min-111 { min-width: 111rpx; }

.h-min-111 { min-height: 111rpx; }

.w-112 { width: 112rpx; }

.h-112 { height: 112rpx; }

.w-max-112 { max-width: 112rpx; }

.h-max-112 { max-height: 112rpx; }

.w-min-112 { min-width: 112rpx; }

.h-min-112 { min-height: 112rpx; }

.w-113 { width: 113rpx; }

.h-113 { height: 113rpx; }

.w-max-113 { max-width: 113rpx; }

.h-max-113 { max-height: 113rpx; }

.w-min-113 { min-width: 113rpx; }

.h-min-113 { min-height: 113rpx; }

.w-114 { width: 114rpx; }

.h-114 { height: 114rpx; }

.w-max-114 { max-width: 114rpx; }

.h-max-114 { max-height: 114rpx; }

.w-min-114 { min-width: 114rpx; }

.h-min-114 { min-height: 114rpx; }

.w-115 { width: 115rpx; }

.h-115 { height: 115rpx; }

.w-max-115 { max-width: 115rpx; }

.h-max-115 { max-height: 115rpx; }

.w-min-115 { min-width: 115rpx; }

.h-min-115 { min-height: 115rpx; }

.w-116 { width: 116rpx; }

.h-116 { height: 116rpx; }

.w-max-116 { max-width: 116rpx; }

.h-max-116 { max-height: 116rpx; }

.w-min-116 { min-width: 116rpx; }

.h-min-116 { min-height: 116rpx; }

.w-117 { width: 117rpx; }

.h-117 { height: 117rpx; }

.w-max-117 { max-width: 117rpx; }

.h-max-117 { max-height: 117rpx; }

.w-min-117 { min-width: 117rpx; }

.h-min-117 { min-height: 117rpx; }

.w-118 { width: 118rpx; }

.h-118 { height: 118rpx; }

.w-max-118 { max-width: 118rpx; }

.h-max-118 { max-height: 118rpx; }

.w-min-118 { min-width: 118rpx; }

.h-min-118 { min-height: 118rpx; }

.w-119 { width: 119rpx; }

.h-119 { height: 119rpx; }

.w-max-119 { max-width: 119rpx; }

.h-max-119 { max-height: 119rpx; }

.w-min-119 { min-width: 119rpx; }

.h-min-119 { min-height: 119rpx; }

.w-120 { width: 120rpx; }

.h-120 { height: 120rpx; }

.w-max-120 { max-width: 120rpx; }

.h-max-120 { max-height: 120rpx; }

.w-min-120 { min-width: 120rpx; }

.h-min-120 { min-height: 120rpx; }

.w-121 { width: 121rpx; }

.h-121 { height: 121rpx; }

.w-max-121 { max-width: 121rpx; }

.h-max-121 { max-height: 121rpx; }

.w-min-121 { min-width: 121rpx; }

.h-min-121 { min-height: 121rpx; }

.w-122 { width: 122rpx; }

.h-122 { height: 122rpx; }

.w-max-122 { max-width: 122rpx; }

.h-max-122 { max-height: 122rpx; }

.w-min-122 { min-width: 122rpx; }

.h-min-122 { min-height: 122rpx; }

.w-123 { width: 123rpx; }

.h-123 { height: 123rpx; }

.w-max-123 { max-width: 123rpx; }

.h-max-123 { max-height: 123rpx; }

.w-min-123 { min-width: 123rpx; }

.h-min-123 { min-height: 123rpx; }

.w-124 { width: 124rpx; }

.h-124 { height: 124rpx; }

.w-max-124 { max-width: 124rpx; }

.h-max-124 { max-height: 124rpx; }

.w-min-124 { min-width: 124rpx; }

.h-min-124 { min-height: 124rpx; }

.w-125 { width: 125rpx; }

.h-125 { height: 125rpx; }

.w-max-125 { max-width: 125rpx; }

.h-max-125 { max-height: 125rpx; }

.w-min-125 { min-width: 125rpx; }

.h-min-125 { min-height: 125rpx; }

.w-126 { width: 126rpx; }

.h-126 { height: 126rpx; }

.w-max-126 { max-width: 126rpx; }

.h-max-126 { max-height: 126rpx; }

.w-min-126 { min-width: 126rpx; }

.h-min-126 { min-height: 126rpx; }

.w-127 { width: 127rpx; }

.h-127 { height: 127rpx; }

.w-max-127 { max-width: 127rpx; }

.h-max-127 { max-height: 127rpx; }

.w-min-127 { min-width: 127rpx; }

.h-min-127 { min-height: 127rpx; }

.w-128 { width: 128rpx; }

.h-128 { height: 128rpx; }

.w-max-128 { max-width: 128rpx; }

.h-max-128 { max-height: 128rpx; }

.w-min-128 { min-width: 128rpx; }

.h-min-128 { min-height: 128rpx; }

.w-129 { width: 129rpx; }

.h-129 { height: 129rpx; }

.w-max-129 { max-width: 129rpx; }

.h-max-129 { max-height: 129rpx; }

.w-min-129 { min-width: 129rpx; }

.h-min-129 { min-height: 129rpx; }

.w-130 { width: 130rpx; }

.h-130 { height: 130rpx; }

.w-max-130 { max-width: 130rpx; }

.h-max-130 { max-height: 130rpx; }

.w-min-130 { min-width: 130rpx; }

.h-min-130 { min-height: 130rpx; }

.w-131 { width: 131rpx; }

.h-131 { height: 131rpx; }

.w-max-131 { max-width: 131rpx; }

.h-max-131 { max-height: 131rpx; }

.w-min-131 { min-width: 131rpx; }

.h-min-131 { min-height: 131rpx; }

.w-132 { width: 132rpx; }

.h-132 { height: 132rpx; }

.w-max-132 { max-width: 132rpx; }

.h-max-132 { max-height: 132rpx; }

.w-min-132 { min-width: 132rpx; }

.h-min-132 { min-height: 132rpx; }

.w-133 { width: 133rpx; }

.h-133 { height: 133rpx; }

.w-max-133 { max-width: 133rpx; }

.h-max-133 { max-height: 133rpx; }

.w-min-133 { min-width: 133rpx; }

.h-min-133 { min-height: 133rpx; }

.w-134 { width: 134rpx; }

.h-134 { height: 134rpx; }

.w-max-134 { max-width: 134rpx; }

.h-max-134 { max-height: 134rpx; }

.w-min-134 { min-width: 134rpx; }

.h-min-134 { min-height: 134rpx; }

.w-135 { width: 135rpx; }

.h-135 { height: 135rpx; }

.w-max-135 { max-width: 135rpx; }

.h-max-135 { max-height: 135rpx; }

.w-min-135 { min-width: 135rpx; }

.h-min-135 { min-height: 135rpx; }

.w-136 { width: 136rpx; }

.h-136 { height: 136rpx; }

.w-max-136 { max-width: 136rpx; }

.h-max-136 { max-height: 136rpx; }

.w-min-136 { min-width: 136rpx; }

.h-min-136 { min-height: 136rpx; }

.w-137 { width: 137rpx; }

.h-137 { height: 137rpx; }

.w-max-137 { max-width: 137rpx; }

.h-max-137 { max-height: 137rpx; }

.w-min-137 { min-width: 137rpx; }

.h-min-137 { min-height: 137rpx; }

.w-138 { width: 138rpx; }

.h-138 { height: 138rpx; }

.w-max-138 { max-width: 138rpx; }

.h-max-138 { max-height: 138rpx; }

.w-min-138 { min-width: 138rpx; }

.h-min-138 { min-height: 138rpx; }

.w-139 { width: 139rpx; }

.h-139 { height: 139rpx; }

.w-max-139 { max-width: 139rpx; }

.h-max-139 { max-height: 139rpx; }

.w-min-139 { min-width: 139rpx; }

.h-min-139 { min-height: 139rpx; }

.w-140 { width: 140rpx; }

.h-140 { height: 140rpx; }

.w-max-140 { max-width: 140rpx; }

.h-max-140 { max-height: 140rpx; }

.w-min-140 { min-width: 140rpx; }

.h-min-140 { min-height: 140rpx; }

.w-141 { width: 141rpx; }

.h-141 { height: 141rpx; }

.w-max-141 { max-width: 141rpx; }

.h-max-141 { max-height: 141rpx; }

.w-min-141 { min-width: 141rpx; }

.h-min-141 { min-height: 141rpx; }

.w-142 { width: 142rpx; }

.h-142 { height: 142rpx; }

.w-max-142 { max-width: 142rpx; }

.h-max-142 { max-height: 142rpx; }

.w-min-142 { min-width: 142rpx; }

.h-min-142 { min-height: 142rpx; }

.w-143 { width: 143rpx; }

.h-143 { height: 143rpx; }

.w-max-143 { max-width: 143rpx; }

.h-max-143 { max-height: 143rpx; }

.w-min-143 { min-width: 143rpx; }

.h-min-143 { min-height: 143rpx; }

.w-144 { width: 144rpx; }

.h-144 { height: 144rpx; }

.w-max-144 { max-width: 144rpx; }

.h-max-144 { max-height: 144rpx; }

.w-min-144 { min-width: 144rpx; }

.h-min-144 { min-height: 144rpx; }

.w-145 { width: 145rpx; }

.h-145 { height: 145rpx; }

.w-max-145 { max-width: 145rpx; }

.h-max-145 { max-height: 145rpx; }

.w-min-145 { min-width: 145rpx; }

.h-min-145 { min-height: 145rpx; }

.w-146 { width: 146rpx; }

.h-146 { height: 146rpx; }

.w-max-146 { max-width: 146rpx; }

.h-max-146 { max-height: 146rpx; }

.w-min-146 { min-width: 146rpx; }

.h-min-146 { min-height: 146rpx; }

.w-147 { width: 147rpx; }

.h-147 { height: 147rpx; }

.w-max-147 { max-width: 147rpx; }

.h-max-147 { max-height: 147rpx; }

.w-min-147 { min-width: 147rpx; }

.h-min-147 { min-height: 147rpx; }

.w-148 { width: 148rpx; }

.h-148 { height: 148rpx; }

.w-max-148 { max-width: 148rpx; }

.h-max-148 { max-height: 148rpx; }

.w-min-148 { min-width: 148rpx; }

.h-min-148 { min-height: 148rpx; }

.w-149 { width: 149rpx; }

.h-149 { height: 149rpx; }

.w-max-149 { max-width: 149rpx; }

.h-max-149 { max-height: 149rpx; }

.w-min-149 { min-width: 149rpx; }

.h-min-149 { min-height: 149rpx; }

.w-150 { width: 150rpx; }

.h-150 { height: 150rpx; }

.w-max-150 { max-width: 150rpx; }

.h-max-150 { max-height: 150rpx; }

.w-min-150 { min-width: 150rpx; }

.h-min-150 { min-height: 150rpx; }

.w-151 { width: 151rpx; }

.h-151 { height: 151rpx; }

.w-max-151 { max-width: 151rpx; }

.h-max-151 { max-height: 151rpx; }

.w-min-151 { min-width: 151rpx; }

.h-min-151 { min-height: 151rpx; }

.w-152 { width: 152rpx; }

.h-152 { height: 152rpx; }

.w-max-152 { max-width: 152rpx; }

.h-max-152 { max-height: 152rpx; }

.w-min-152 { min-width: 152rpx; }

.h-min-152 { min-height: 152rpx; }

.w-153 { width: 153rpx; }

.h-153 { height: 153rpx; }

.w-max-153 { max-width: 153rpx; }

.h-max-153 { max-height: 153rpx; }

.w-min-153 { min-width: 153rpx; }

.h-min-153 { min-height: 153rpx; }

.w-154 { width: 154rpx; }

.h-154 { height: 154rpx; }

.w-max-154 { max-width: 154rpx; }

.h-max-154 { max-height: 154rpx; }

.w-min-154 { min-width: 154rpx; }

.h-min-154 { min-height: 154rpx; }

.w-155 { width: 155rpx; }

.h-155 { height: 155rpx; }

.w-max-155 { max-width: 155rpx; }

.h-max-155 { max-height: 155rpx; }

.w-min-155 { min-width: 155rpx; }

.h-min-155 { min-height: 155rpx; }

.w-156 { width: 156rpx; }

.h-156 { height: 156rpx; }

.w-max-156 { max-width: 156rpx; }

.h-max-156 { max-height: 156rpx; }

.w-min-156 { min-width: 156rpx; }

.h-min-156 { min-height: 156rpx; }

.w-157 { width: 157rpx; }

.h-157 { height: 157rpx; }

.w-max-157 { max-width: 157rpx; }

.h-max-157 { max-height: 157rpx; }

.w-min-157 { min-width: 157rpx; }

.h-min-157 { min-height: 157rpx; }

.w-158 { width: 158rpx; }

.h-158 { height: 158rpx; }

.w-max-158 { max-width: 158rpx; }

.h-max-158 { max-height: 158rpx; }

.w-min-158 { min-width: 158rpx; }

.h-min-158 { min-height: 158rpx; }

.w-159 { width: 159rpx; }

.h-159 { height: 159rpx; }

.w-max-159 { max-width: 159rpx; }

.h-max-159 { max-height: 159rpx; }

.w-min-159 { min-width: 159rpx; }

.h-min-159 { min-height: 159rpx; }

.w-160 { width: 160rpx; }

.h-160 { height: 160rpx; }

.w-max-160 { max-width: 160rpx; }

.h-max-160 { max-height: 160rpx; }

.w-min-160 { min-width: 160rpx; }

.h-min-160 { min-height: 160rpx; }

.w-161 { width: 161rpx; }

.h-161 { height: 161rpx; }

.w-max-161 { max-width: 161rpx; }

.h-max-161 { max-height: 161rpx; }

.w-min-161 { min-width: 161rpx; }

.h-min-161 { min-height: 161rpx; }

.w-162 { width: 162rpx; }

.h-162 { height: 162rpx; }

.w-max-162 { max-width: 162rpx; }

.h-max-162 { max-height: 162rpx; }

.w-min-162 { min-width: 162rpx; }

.h-min-162 { min-height: 162rpx; }

.w-163 { width: 163rpx; }

.h-163 { height: 163rpx; }

.w-max-163 { max-width: 163rpx; }

.h-max-163 { max-height: 163rpx; }

.w-min-163 { min-width: 163rpx; }

.h-min-163 { min-height: 163rpx; }

.w-164 { width: 164rpx; }

.h-164 { height: 164rpx; }

.w-max-164 { max-width: 164rpx; }

.h-max-164 { max-height: 164rpx; }

.w-min-164 { min-width: 164rpx; }

.h-min-164 { min-height: 164rpx; }

.w-165 { width: 165rpx; }

.h-165 { height: 165rpx; }

.w-max-165 { max-width: 165rpx; }

.h-max-165 { max-height: 165rpx; }

.w-min-165 { min-width: 165rpx; }

.h-min-165 { min-height: 165rpx; }

.w-166 { width: 166rpx; }

.h-166 { height: 166rpx; }

.w-max-166 { max-width: 166rpx; }

.h-max-166 { max-height: 166rpx; }

.w-min-166 { min-width: 166rpx; }

.h-min-166 { min-height: 166rpx; }

.w-167 { width: 167rpx; }

.h-167 { height: 167rpx; }

.w-max-167 { max-width: 167rpx; }

.h-max-167 { max-height: 167rpx; }

.w-min-167 { min-width: 167rpx; }

.h-min-167 { min-height: 167rpx; }

.w-168 { width: 168rpx; }

.h-168 { height: 168rpx; }

.w-max-168 { max-width: 168rpx; }

.h-max-168 { max-height: 168rpx; }

.w-min-168 { min-width: 168rpx; }

.h-min-168 { min-height: 168rpx; }

.w-169 { width: 169rpx; }

.h-169 { height: 169rpx; }

.w-max-169 { max-width: 169rpx; }

.h-max-169 { max-height: 169rpx; }

.w-min-169 { min-width: 169rpx; }

.h-min-169 { min-height: 169rpx; }

.w-170 { width: 170rpx; }

.h-170 { height: 170rpx; }

.w-max-170 { max-width: 170rpx; }

.h-max-170 { max-height: 170rpx; }

.w-min-170 { min-width: 170rpx; }

.h-min-170 { min-height: 170rpx; }

.w-171 { width: 171rpx; }

.h-171 { height: 171rpx; }

.w-max-171 { max-width: 171rpx; }

.h-max-171 { max-height: 171rpx; }

.w-min-171 { min-width: 171rpx; }

.h-min-171 { min-height: 171rpx; }

.w-172 { width: 172rpx; }

.h-172 { height: 172rpx; }

.w-max-172 { max-width: 172rpx; }

.h-max-172 { max-height: 172rpx; }

.w-min-172 { min-width: 172rpx; }

.h-min-172 { min-height: 172rpx; }

.w-173 { width: 173rpx; }

.h-173 { height: 173rpx; }

.w-max-173 { max-width: 173rpx; }

.h-max-173 { max-height: 173rpx; }

.w-min-173 { min-width: 173rpx; }

.h-min-173 { min-height: 173rpx; }

.w-174 { width: 174rpx; }

.h-174 { height: 174rpx; }

.w-max-174 { max-width: 174rpx; }

.h-max-174 { max-height: 174rpx; }

.w-min-174 { min-width: 174rpx; }

.h-min-174 { min-height: 174rpx; }

.w-175 { width: 175rpx; }

.h-175 { height: 175rpx; }

.w-max-175 { max-width: 175rpx; }

.h-max-175 { max-height: 175rpx; }

.w-min-175 { min-width: 175rpx; }

.h-min-175 { min-height: 175rpx; }

.w-176 { width: 176rpx; }

.h-176 { height: 176rpx; }

.w-max-176 { max-width: 176rpx; }

.h-max-176 { max-height: 176rpx; }

.w-min-176 { min-width: 176rpx; }

.h-min-176 { min-height: 176rpx; }

.w-177 { width: 177rpx; }

.h-177 { height: 177rpx; }

.w-max-177 { max-width: 177rpx; }

.h-max-177 { max-height: 177rpx; }

.w-min-177 { min-width: 177rpx; }

.h-min-177 { min-height: 177rpx; }

.w-178 { width: 178rpx; }

.h-178 { height: 178rpx; }

.w-max-178 { max-width: 178rpx; }

.h-max-178 { max-height: 178rpx; }

.w-min-178 { min-width: 178rpx; }

.h-min-178 { min-height: 178rpx; }

.w-179 { width: 179rpx; }

.h-179 { height: 179rpx; }

.w-max-179 { max-width: 179rpx; }

.h-max-179 { max-height: 179rpx; }

.w-min-179 { min-width: 179rpx; }

.h-min-179 { min-height: 179rpx; }

.w-180 { width: 180rpx; }

.h-180 { height: 180rpx; }

.w-max-180 { max-width: 180rpx; }

.h-max-180 { max-height: 180rpx; }

.w-min-180 { min-width: 180rpx; }

.h-min-180 { min-height: 180rpx; }

.w-181 { width: 181rpx; }

.h-181 { height: 181rpx; }

.w-max-181 { max-width: 181rpx; }

.h-max-181 { max-height: 181rpx; }

.w-min-181 { min-width: 181rpx; }

.h-min-181 { min-height: 181rpx; }

.w-182 { width: 182rpx; }

.h-182 { height: 182rpx; }

.w-max-182 { max-width: 182rpx; }

.h-max-182 { max-height: 182rpx; }

.w-min-182 { min-width: 182rpx; }

.h-min-182 { min-height: 182rpx; }

.w-183 { width: 183rpx; }

.h-183 { height: 183rpx; }

.w-max-183 { max-width: 183rpx; }

.h-max-183 { max-height: 183rpx; }

.w-min-183 { min-width: 183rpx; }

.h-min-183 { min-height: 183rpx; }

.w-184 { width: 184rpx; }

.h-184 { height: 184rpx; }

.w-max-184 { max-width: 184rpx; }

.h-max-184 { max-height: 184rpx; }

.w-min-184 { min-width: 184rpx; }

.h-min-184 { min-height: 184rpx; }

.w-185 { width: 185rpx; }

.h-185 { height: 185rpx; }

.w-max-185 { max-width: 185rpx; }

.h-max-185 { max-height: 185rpx; }

.w-min-185 { min-width: 185rpx; }

.h-min-185 { min-height: 185rpx; }

.w-186 { width: 186rpx; }

.h-186 { height: 186rpx; }

.w-max-186 { max-width: 186rpx; }

.h-max-186 { max-height: 186rpx; }

.w-min-186 { min-width: 186rpx; }

.h-min-186 { min-height: 186rpx; }

.w-187 { width: 187rpx; }

.h-187 { height: 187rpx; }

.w-max-187 { max-width: 187rpx; }

.h-max-187 { max-height: 187rpx; }

.w-min-187 { min-width: 187rpx; }

.h-min-187 { min-height: 187rpx; }

.w-188 { width: 188rpx; }

.h-188 { height: 188rpx; }

.w-max-188 { max-width: 188rpx; }

.h-max-188 { max-height: 188rpx; }

.w-min-188 { min-width: 188rpx; }

.h-min-188 { min-height: 188rpx; }

.w-189 { width: 189rpx; }

.h-189 { height: 189rpx; }

.w-max-189 { max-width: 189rpx; }

.h-max-189 { max-height: 189rpx; }

.w-min-189 { min-width: 189rpx; }

.h-min-189 { min-height: 189rpx; }

.w-190 { width: 190rpx; }

.h-190 { height: 190rpx; }

.w-max-190 { max-width: 190rpx; }

.h-max-190 { max-height: 190rpx; }

.w-min-190 { min-width: 190rpx; }

.h-min-190 { min-height: 190rpx; }

.f-20 { font-size: 20rpx; }

.l-20 { line-height: 20rpx; }

.f-21 { font-size: 21rpx; }

.l-21 { line-height: 21rpx; }

.f-22 { font-size: 22rpx; }

.l-22 { line-height: 22rpx; }

.f-23 { font-size: 23rpx; }

.l-23 { line-height: 23rpx; }

.f-24 { font-size: 24rpx; }

.l-24 { line-height: 24rpx; }

.f-25 { font-size: 25rpx; }

.l-25 { line-height: 25rpx; }

.f-26 { font-size: 26rpx; }

.l-26 { line-height: 26rpx; }

.f-27 { font-size: 27rpx; }

.l-27 { line-height: 27rpx; }

.f-28 { font-size: 28rpx; }

.l-28 { line-height: 28rpx; }

.f-29 { font-size: 29rpx; }

.l-29 { line-height: 29rpx; }

.f-30 { font-size: 30rpx; }

.l-30 { line-height: 30rpx; }

.f-31 { font-size: 31rpx; }

.l-31 { line-height: 31rpx; }

.f-32 { font-size: 32rpx; }

.l-32 { line-height: 32rpx; }

.f-33 { font-size: 33rpx; }

.l-33 { line-height: 33rpx; }

.f-34 { font-size: 34rpx; }

.l-34 { line-height: 34rpx; }

.f-35 { font-size: 35rpx; }

.l-35 { line-height: 35rpx; }

.f-36 { font-size: 36rpx; }

.l-36 { line-height: 36rpx; }

.f-37 { font-size: 37rpx; }

.l-37 { line-height: 37rpx; }

.f-38 { font-size: 38rpx; }

.l-38 { line-height: 38rpx; }

.f-39 { font-size: 39rpx; }

.l-39 { line-height: 39rpx; }

.f-40 { font-size: 40rpx; }

.l-40 { line-height: 40rpx; }

.f-41 { font-size: 41rpx; }

.l-41 { line-height: 41rpx; }

.f-42 { font-size: 42rpx; }

.l-42 { line-height: 42rpx; }

.f-43 { font-size: 43rpx; }

.l-43 { line-height: 43rpx; }

.f-44 { font-size: 44rpx; }

.l-44 { line-height: 44rpx; }

.f-45 { font-size: 45rpx; }

.l-45 { line-height: 45rpx; }

.f-46 { font-size: 46rpx; }

.l-46 { line-height: 46rpx; }

.f-47 { font-size: 47rpx; }

.l-47 { line-height: 47rpx; }

.f-48 { font-size: 48rpx; }

.l-48 { line-height: 48rpx; }

.f-49 { font-size: 49rpx; }

.l-49 { line-height: 49rpx; }

.f-50 { font-size: 50rpx; }

.l-50 { line-height: 50rpx; }

.f-51 { font-size: 51rpx; }

.l-51 { line-height: 51rpx; }

.f-52 { font-size: 52rpx; }

.l-52 { line-height: 52rpx; }

.f-53 { font-size: 53rpx; }

.l-53 { line-height: 53rpx; }

.f-54 { font-size: 54rpx; }

.l-54 { line-height: 54rpx; }

.f-55 { font-size: 55rpx; }

.l-55 { line-height: 55rpx; }

.f-56 { font-size: 56rpx; }

.l-56 { line-height: 56rpx; }

.f-57 { font-size: 57rpx; }

.l-57 { line-height: 57rpx; }

.f-58 { font-size: 58rpx; }

.l-58 { line-height: 58rpx; }

.f-59 { font-size: 59rpx; }

.l-59 { line-height: 59rpx; }

.f-60 { font-size: 60rpx; }

.l-60 { line-height: 60rpx; }

.f-61 { font-size: 61rpx; }

.l-61 { line-height: 61rpx; }

.f-62 { font-size: 62rpx; }

.l-62 { line-height: 62rpx; }

.f-63 { font-size: 63rpx; }

.l-63 { line-height: 63rpx; }

.f-64 { font-size: 64rpx; }

.l-64 { line-height: 64rpx; }

.mt-0 { margin-top: 0rpx !important; }

.mh-0 { margin-left: 0rpx !important; margin-right: 0rpx !important; }

.mv-0 { margin-top: 0rpx !important; margin-bottom: 0rpx !important; }

.mb-0 { margin-bottom: 0rpx !important; }

.mr-0 { margin-right: 0rpx !important; }

.ml-0 { margin-left: 0rpx !important; }

.pt-0 { padding-top: 0rpx !important; }

.pb-0 { padding-bottom: 0rpx !important; }

.pr-0 { padding-right: 0rpx !important; }

.pl-0 { padding-left: 0rpx !important; }

.ph-0 { padding-left: 0rpx !important; padding-right: 0rpx !important; }

.pv-0 { padding-top: 0rpx !important; padding-bottom: 0rpx !important; }

.p-0 { padding: 0rpx; }

.m-0 { margin: 0rpx; }

.mt-1 { margin-top: 1rpx !important; }

.mh-1 { margin-left: 1rpx !important; margin-right: 1rpx !important; }

.mv-1 { margin-top: 1rpx !important; margin-bottom: 1rpx !important; }

.mb-1 { margin-bottom: 1rpx !important; }

.mr-1 { margin-right: 1rpx !important; }

.ml-1 { margin-left: 1rpx !important; }

.pt-1 { padding-top: 1rpx !important; }

.pb-1 { padding-bottom: 1rpx !important; }

.pr-1 { padding-right: 1rpx !important; }

.pl-1 { padding-left: 1rpx !important; }

.ph-1 { padding-left: 1rpx !important; padding-right: 1rpx !important; }

.pv-1 { padding-top: 1rpx !important; padding-bottom: 1rpx !important; }

.p-1 { padding: 1rpx; }

.m-1 { margin: 1rpx; }

.mt-2 { margin-top: 2rpx !important; }

.mh-2 { margin-left: 2rpx !important; margin-right: 2rpx !important; }

.mv-2 { margin-top: 2rpx !important; margin-bottom: 2rpx !important; }

.mb-2 { margin-bottom: 2rpx !important; }

.mr-2 { margin-right: 2rpx !important; }

.ml-2 { margin-left: 2rpx !important; }

.pt-2 { padding-top: 2rpx !important; }

.pb-2 { padding-bottom: 2rpx !important; }

.pr-2 { padding-right: 2rpx !important; }

.pl-2 { padding-left: 2rpx !important; }

.ph-2 { padding-left: 2rpx !important; padding-right: 2rpx !important; }

.pv-2 { padding-top: 2rpx !important; padding-bottom: 2rpx !important; }

.p-2 { padding: 2rpx; }

.m-2 { margin: 2rpx; }

.mt-3 { margin-top: 3rpx !important; }

.mh-3 { margin-left: 3rpx !important; margin-right: 3rpx !important; }

.mv-3 { margin-top: 3rpx !important; margin-bottom: 3rpx !important; }

.mb-3 { margin-bottom: 3rpx !important; }

.mr-3 { margin-right: 3rpx !important; }

.ml-3 { margin-left: 3rpx !important; }

.pt-3 { padding-top: 3rpx !important; }

.pb-3 { padding-bottom: 3rpx !important; }

.pr-3 { padding-right: 3rpx !important; }

.pl-3 { padding-left: 3rpx !important; }

.ph-3 { padding-left: 3rpx !important; padding-right: 3rpx !important; }

.pv-3 { padding-top: 3rpx !important; padding-bottom: 3rpx !important; }

.p-3 { padding: 3rpx; }

.m-3 { margin: 3rpx; }

.mt-4 { margin-top: 4rpx !important; }

.mh-4 { margin-left: 4rpx !important; margin-right: 4rpx !important; }

.mv-4 { margin-top: 4rpx !important; margin-bottom: 4rpx !important; }

.mb-4 { margin-bottom: 4rpx !important; }

.mr-4 { margin-right: 4rpx !important; }

.ml-4 { margin-left: 4rpx !important; }

.pt-4 { padding-top: 4rpx !important; }

.pb-4 { padding-bottom: 4rpx !important; }

.pr-4 { padding-right: 4rpx !important; }

.pl-4 { padding-left: 4rpx !important; }

.ph-4 { padding-left: 4rpx !important; padding-right: 4rpx !important; }

.pv-4 { padding-top: 4rpx !important; padding-bottom: 4rpx !important; }

.p-4 { padding: 4rpx; }

.m-4 { margin: 4rpx; }

.mt-5 { margin-top: 5rpx !important; }

.mh-5 { margin-left: 5rpx !important; margin-right: 5rpx !important; }

.mv-5 { margin-top: 5rpx !important; margin-bottom: 5rpx !important; }

.mb-5 { margin-bottom: 5rpx !important; }

.mr-5 { margin-right: 5rpx !important; }

.ml-5 { margin-left: 5rpx !important; }

.pt-5 { padding-top: 5rpx !important; }

.pb-5 { padding-bottom: 5rpx !important; }

.pr-5 { padding-right: 5rpx !important; }

.pl-5 { padding-left: 5rpx !important; }

.ph-5 { padding-left: 5rpx !important; padding-right: 5rpx !important; }

.pv-5 { padding-top: 5rpx !important; padding-bottom: 5rpx !important; }

.p-5 { padding: 5rpx; }

.m-5 { margin: 5rpx; }

.mt-6 { margin-top: 6rpx !important; }

.mh-6 { margin-left: 6rpx !important; margin-right: 6rpx !important; }

.mv-6 { margin-top: 6rpx !important; margin-bottom: 6rpx !important; }

.mb-6 { margin-bottom: 6rpx !important; }

.mr-6 { margin-right: 6rpx !important; }

.ml-6 { margin-left: 6rpx !important; }

.pt-6 { padding-top: 6rpx !important; }

.pb-6 { padding-bottom: 6rpx !important; }

.pr-6 { padding-right: 6rpx !important; }

.pl-6 { padding-left: 6rpx !important; }

.ph-6 { padding-left: 6rpx !important; padding-right: 6rpx !important; }

.pv-6 { padding-top: 6rpx !important; padding-bottom: 6rpx !important; }

.p-6 { padding: 6rpx; }

.m-6 { margin: 6rpx; }

.mt-7 { margin-top: 7rpx !important; }

.mh-7 { margin-left: 7rpx !important; margin-right: 7rpx !important; }

.mv-7 { margin-top: 7rpx !important; margin-bottom: 7rpx !important; }

.mb-7 { margin-bottom: 7rpx !important; }

.mr-7 { margin-right: 7rpx !important; }

.ml-7 { margin-left: 7rpx !important; }

.pt-7 { padding-top: 7rpx !important; }

.pb-7 { padding-bottom: 7rpx !important; }

.pr-7 { padding-right: 7rpx !important; }

.pl-7 { padding-left: 7rpx !important; }

.ph-7 { padding-left: 7rpx !important; padding-right: 7rpx !important; }

.pv-7 { padding-top: 7rpx !important; padding-bottom: 7rpx !important; }

.p-7 { padding: 7rpx; }

.m-7 { margin: 7rpx; }

.mt-8 { margin-top: 8rpx !important; }

.mh-8 { margin-left: 8rpx !important; margin-right: 8rpx !important; }

.mv-8 { margin-top: 8rpx !important; margin-bottom: 8rpx !important; }

.mb-8 { margin-bottom: 8rpx !important; }

.mr-8 { margin-right: 8rpx !important; }

.ml-8 { margin-left: 8rpx !important; }

.pt-8 { padding-top: 8rpx !important; }

.pb-8 { padding-bottom: 8rpx !important; }

.pr-8 { padding-right: 8rpx !important; }

.pl-8 { padding-left: 8rpx !important; }

.ph-8 { padding-left: 8rpx !important; padding-right: 8rpx !important; }

.pv-8 { padding-top: 8rpx !important; padding-bottom: 8rpx !important; }

.p-8 { padding: 8rpx; }

.m-8 { margin: 8rpx; }

.mt-9 { margin-top: 9rpx !important; }

.mh-9 { margin-left: 9rpx !important; margin-right: 9rpx !important; }

.mv-9 { margin-top: 9rpx !important; margin-bottom: 9rpx !important; }

.mb-9 { margin-bottom: 9rpx !important; }

.mr-9 { margin-right: 9rpx !important; }

.ml-9 { margin-left: 9rpx !important; }

.pt-9 { padding-top: 9rpx !important; }

.pb-9 { padding-bottom: 9rpx !important; }

.pr-9 { padding-right: 9rpx !important; }

.pl-9 { padding-left: 9rpx !important; }

.ph-9 { padding-left: 9rpx !important; padding-right: 9rpx !important; }

.pv-9 { padding-top: 9rpx !important; padding-bottom: 9rpx !important; }

.p-9 { padding: 9rpx; }

.m-9 { margin: 9rpx; }

.mt-10 { margin-top: 10rpx !important; }

.mh-10 { margin-left: 10rpx !important; margin-right: 10rpx !important; }

.mv-10 { margin-top: 10rpx !important; margin-bottom: 10rpx !important; }

.mb-10 { margin-bottom: 10rpx !important; }

.mr-10 { margin-right: 10rpx !important; }

.ml-10 { margin-left: 10rpx !important; }

.pt-10 { padding-top: 10rpx !important; }

.pb-10 { padding-bottom: 10rpx !important; }

.pr-10 { padding-right: 10rpx !important; }

.pl-10 { padding-left: 10rpx !important; }

.ph-10 { padding-left: 10rpx !important; padding-right: 10rpx !important; }

.pv-10 { padding-top: 10rpx !important; padding-bottom: 10rpx !important; }

.p-10 { padding: 10rpx; }

.m-10 { margin: 10rpx; }

.mt-11 { margin-top: 11rpx !important; }

.mh-11 { margin-left: 11rpx !important; margin-right: 11rpx !important; }

.mv-11 { margin-top: 11rpx !important; margin-bottom: 11rpx !important; }

.mb-11 { margin-bottom: 11rpx !important; }

.mr-11 { margin-right: 11rpx !important; }

.ml-11 { margin-left: 11rpx !important; }

.pt-11 { padding-top: 11rpx !important; }

.pb-11 { padding-bottom: 11rpx !important; }

.pr-11 { padding-right: 11rpx !important; }

.pl-11 { padding-left: 11rpx !important; }

.ph-11 { padding-left: 11rpx !important; padding-right: 11rpx !important; }

.pv-11 { padding-top: 11rpx !important; padding-bottom: 11rpx !important; }

.p-11 { padding: 11rpx; }

.m-11 { margin: 11rpx; }

.mt-12 { margin-top: 12rpx !important; }

.mh-12 { margin-left: 12rpx !important; margin-right: 12rpx !important; }

.mv-12 { margin-top: 12rpx !important; margin-bottom: 12rpx !important; }

.mb-12 { margin-bottom: 12rpx !important; }

.mr-12 { margin-right: 12rpx !important; }

.ml-12 { margin-left: 12rpx !important; }

.pt-12 { padding-top: 12rpx !important; }

.pb-12 { padding-bottom: 12rpx !important; }

.pr-12 { padding-right: 12rpx !important; }

.pl-12 { padding-left: 12rpx !important; }

.ph-12 { padding-left: 12rpx !important; padding-right: 12rpx !important; }

.pv-12 { padding-top: 12rpx !important; padding-bottom: 12rpx !important; }

.p-12 { padding: 12rpx; }

.m-12 { margin: 12rpx; }

.mt-13 { margin-top: 13rpx !important; }

.mh-13 { margin-left: 13rpx !important; margin-right: 13rpx !important; }

.mv-13 { margin-top: 13rpx !important; margin-bottom: 13rpx !important; }

.mb-13 { margin-bottom: 13rpx !important; }

.mr-13 { margin-right: 13rpx !important; }

.ml-13 { margin-left: 13rpx !important; }

.pt-13 { padding-top: 13rpx !important; }

.pb-13 { padding-bottom: 13rpx !important; }

.pr-13 { padding-right: 13rpx !important; }

.pl-13 { padding-left: 13rpx !important; }

.ph-13 { padding-left: 13rpx !important; padding-right: 13rpx !important; }

.pv-13 { padding-top: 13rpx !important; padding-bottom: 13rpx !important; }

.p-13 { padding: 13rpx; }

.m-13 { margin: 13rpx; }

.mt-14 { margin-top: 14rpx !important; }

.mh-14 { margin-left: 14rpx !important; margin-right: 14rpx !important; }

.mv-14 { margin-top: 14rpx !important; margin-bottom: 14rpx !important; }

.mb-14 { margin-bottom: 14rpx !important; }

.mr-14 { margin-right: 14rpx !important; }

.ml-14 { margin-left: 14rpx !important; }

.pt-14 { padding-top: 14rpx !important; }

.pb-14 { padding-bottom: 14rpx !important; }

.pr-14 { padding-right: 14rpx !important; }

.pl-14 { padding-left: 14rpx !important; }

.ph-14 { padding-left: 14rpx !important; padding-right: 14rpx !important; }

.pv-14 { padding-top: 14rpx !important; padding-bottom: 14rpx !important; }

.p-14 { padding: 14rpx; }

.m-14 { margin: 14rpx; }

.mt-15 { margin-top: 15rpx !important; }

.mh-15 { margin-left: 15rpx !important; margin-right: 15rpx !important; }

.mv-15 { margin-top: 15rpx !important; margin-bottom: 15rpx !important; }

.mb-15 { margin-bottom: 15rpx !important; }

.mr-15 { margin-right: 15rpx !important; }

.ml-15 { margin-left: 15rpx !important; }

.pt-15 { padding-top: 15rpx !important; }

.pb-15 { padding-bottom: 15rpx !important; }

.pr-15 { padding-right: 15rpx !important; }

.pl-15 { padding-left: 15rpx !important; }

.ph-15 { padding-left: 15rpx !important; padding-right: 15rpx !important; }

.pv-15 { padding-top: 15rpx !important; padding-bottom: 15rpx !important; }

.p-15 { padding: 15rpx; }

.m-15 { margin: 15rpx; }

.mt-16 { margin-top: 16rpx !important; }

.mh-16 { margin-left: 16rpx !important; margin-right: 16rpx !important; }

.mv-16 { margin-top: 16rpx !important; margin-bottom: 16rpx !important; }

.mb-16 { margin-bottom: 16rpx !important; }

.mr-16 { margin-right: 16rpx !important; }

.ml-16 { margin-left: 16rpx !important; }

.pt-16 { padding-top: 16rpx !important; }

.pb-16 { padding-bottom: 16rpx !important; }

.pr-16 { padding-right: 16rpx !important; }

.pl-16 { padding-left: 16rpx !important; }

.ph-16 { padding-left: 16rpx !important; padding-right: 16rpx !important; }

.pv-16 { padding-top: 16rpx !important; padding-bottom: 16rpx !important; }

.p-16 { padding: 16rpx; }

.m-16 { margin: 16rpx; }

.mt-17 { margin-top: 17rpx !important; }

.mh-17 { margin-left: 17rpx !important; margin-right: 17rpx !important; }

.mv-17 { margin-top: 17rpx !important; margin-bottom: 17rpx !important; }

.mb-17 { margin-bottom: 17rpx !important; }

.mr-17 { margin-right: 17rpx !important; }

.ml-17 { margin-left: 17rpx !important; }

.pt-17 { padding-top: 17rpx !important; }

.pb-17 { padding-bottom: 17rpx !important; }

.pr-17 { padding-right: 17rpx !important; }

.pl-17 { padding-left: 17rpx !important; }

.ph-17 { padding-left: 17rpx !important; padding-right: 17rpx !important; }

.pv-17 { padding-top: 17rpx !important; padding-bottom: 17rpx !important; }

.p-17 { padding: 17rpx; }

.m-17 { margin: 17rpx; }

.mt-18 { margin-top: 18rpx !important; }

.mh-18 { margin-left: 18rpx !important; margin-right: 18rpx !important; }

.mv-18 { margin-top: 18rpx !important; margin-bottom: 18rpx !important; }

.mb-18 { margin-bottom: 18rpx !important; }

.mr-18 { margin-right: 18rpx !important; }

.ml-18 { margin-left: 18rpx !important; }

.pt-18 { padding-top: 18rpx !important; }

.pb-18 { padding-bottom: 18rpx !important; }

.pr-18 { padding-right: 18rpx !important; }

.pl-18 { padding-left: 18rpx !important; }

.ph-18 { padding-left: 18rpx !important; padding-right: 18rpx !important; }

.pv-18 { padding-top: 18rpx !important; padding-bottom: 18rpx !important; }

.p-18 { padding: 18rpx; }

.m-18 { margin: 18rpx; }

.mt-19 { margin-top: 19rpx !important; }

.mh-19 { margin-left: 19rpx !important; margin-right: 19rpx !important; }

.mv-19 { margin-top: 19rpx !important; margin-bottom: 19rpx !important; }

.mb-19 { margin-bottom: 19rpx !important; }

.mr-19 { margin-right: 19rpx !important; }

.ml-19 { margin-left: 19rpx !important; }

.pt-19 { padding-top: 19rpx !important; }

.pb-19 { padding-bottom: 19rpx !important; }

.pr-19 { padding-right: 19rpx !important; }

.pl-19 { padding-left: 19rpx !important; }

.ph-19 { padding-left: 19rpx !important; padding-right: 19rpx !important; }

.pv-19 { padding-top: 19rpx !important; padding-bottom: 19rpx !important; }

.p-19 { padding: 19rpx; }

.m-19 { margin: 19rpx; }

.mt-20 { margin-top: 20rpx !important; }

.mh-20 { margin-left: 20rpx !important; margin-right: 20rpx !important; }

.mv-20 { margin-top: 20rpx !important; margin-bottom: 20rpx !important; }

.mb-20 { margin-bottom: 20rpx !important; }

.mr-20 { margin-right: 20rpx !important; }

.ml-20 { margin-left: 20rpx !important; }

.pt-20 { padding-top: 20rpx !important; }

.pb-20 { padding-bottom: 20rpx !important; }

.pr-20 { padding-right: 20rpx !important; }

.pl-20 { padding-left: 20rpx !important; }

.ph-20 { padding-left: 20rpx !important; padding-right: 20rpx !important; }

.pv-20 { padding-top: 20rpx !important; padding-bottom: 20rpx !important; }

.p-20 { padding: 20rpx; }

.m-20 { margin: 20rpx; }

.mt-21 { margin-top: 21rpx !important; }

.mh-21 { margin-left: 21rpx !important; margin-right: 21rpx !important; }

.mv-21 { margin-top: 21rpx !important; margin-bottom: 21rpx !important; }

.mb-21 { margin-bottom: 21rpx !important; }

.mr-21 { margin-right: 21rpx !important; }

.ml-21 { margin-left: 21rpx !important; }

.pt-21 { padding-top: 21rpx !important; }

.pb-21 { padding-bottom: 21rpx !important; }

.pr-21 { padding-right: 21rpx !important; }

.pl-21 { padding-left: 21rpx !important; }

.ph-21 { padding-left: 21rpx !important; padding-right: 21rpx !important; }

.pv-21 { padding-top: 21rpx !important; padding-bottom: 21rpx !important; }

.p-21 { padding: 21rpx; }

.m-21 { margin: 21rpx; }

.mt-22 { margin-top: 22rpx !important; }

.mh-22 { margin-left: 22rpx !important; margin-right: 22rpx !important; }

.mv-22 { margin-top: 22rpx !important; margin-bottom: 22rpx !important; }

.mb-22 { margin-bottom: 22rpx !important; }

.mr-22 { margin-right: 22rpx !important; }

.ml-22 { margin-left: 22rpx !important; }

.pt-22 { padding-top: 22rpx !important; }

.pb-22 { padding-bottom: 22rpx !important; }

.pr-22 { padding-right: 22rpx !important; }

.pl-22 { padding-left: 22rpx !important; }

.ph-22 { padding-left: 22rpx !important; padding-right: 22rpx !important; }

.pv-22 { padding-top: 22rpx !important; padding-bottom: 22rpx !important; }

.p-22 { padding: 22rpx; }

.m-22 { margin: 22rpx; }

.mt-23 { margin-top: 23rpx !important; }

.mh-23 { margin-left: 23rpx !important; margin-right: 23rpx !important; }

.mv-23 { margin-top: 23rpx !important; margin-bottom: 23rpx !important; }

.mb-23 { margin-bottom: 23rpx !important; }

.mr-23 { margin-right: 23rpx !important; }

.ml-23 { margin-left: 23rpx !important; }

.pt-23 { padding-top: 23rpx !important; }

.pb-23 { padding-bottom: 23rpx !important; }

.pr-23 { padding-right: 23rpx !important; }

.pl-23 { padding-left: 23rpx !important; }

.ph-23 { padding-left: 23rpx !important; padding-right: 23rpx !important; }

.pv-23 { padding-top: 23rpx !important; padding-bottom: 23rpx !important; }

.p-23 { padding: 23rpx; }

.m-23 { margin: 23rpx; }

.mt-24 { margin-top: 24rpx !important; }

.mh-24 { margin-left: 24rpx !important; margin-right: 24rpx !important; }

.mv-24 { margin-top: 24rpx !important; margin-bottom: 24rpx !important; }

.mb-24 { margin-bottom: 24rpx !important; }

.mr-24 { margin-right: 24rpx !important; }

.ml-24 { margin-left: 24rpx !important; }

.pt-24 { padding-top: 24rpx !important; }

.pb-24 { padding-bottom: 24rpx !important; }

.pr-24 { padding-right: 24rpx !important; }

.pl-24 { padding-left: 24rpx !important; }

.ph-24 { padding-left: 24rpx !important; padding-right: 24rpx !important; }

.pv-24 { padding-top: 24rpx !important; padding-bottom: 24rpx !important; }

.p-24 { padding: 24rpx; }

.m-24 { margin: 24rpx; }

.mt-25 { margin-top: 25rpx !important; }

.mh-25 { margin-left: 25rpx !important; margin-right: 25rpx !important; }

.mv-25 { margin-top: 25rpx !important; margin-bottom: 25rpx !important; }

.mb-25 { margin-bottom: 25rpx !important; }

.mr-25 { margin-right: 25rpx !important; }

.ml-25 { margin-left: 25rpx !important; }

.pt-25 { padding-top: 25rpx !important; }

.pb-25 { padding-bottom: 25rpx !important; }

.pr-25 { padding-right: 25rpx !important; }

.pl-25 { padding-left: 25rpx !important; }

.ph-25 { padding-left: 25rpx !important; padding-right: 25rpx !important; }

.pv-25 { padding-top: 25rpx !important; padding-bottom: 25rpx !important; }

.p-25 { padding: 25rpx; }

.m-25 { margin: 25rpx; }

.mt-26 { margin-top: 26rpx !important; }

.mh-26 { margin-left: 26rpx !important; margin-right: 26rpx !important; }

.mv-26 { margin-top: 26rpx !important; margin-bottom: 26rpx !important; }

.mb-26 { margin-bottom: 26rpx !important; }

.mr-26 { margin-right: 26rpx !important; }

.ml-26 { margin-left: 26rpx !important; }

.pt-26 { padding-top: 26rpx !important; }

.pb-26 { padding-bottom: 26rpx !important; }

.pr-26 { padding-right: 26rpx !important; }

.pl-26 { padding-left: 26rpx !important; }

.ph-26 { padding-left: 26rpx !important; padding-right: 26rpx !important; }

.pv-26 { padding-top: 26rpx !important; padding-bottom: 26rpx !important; }

.p-26 { padding: 26rpx; }

.m-26 { margin: 26rpx; }

.mt-27 { margin-top: 27rpx !important; }

.mh-27 { margin-left: 27rpx !important; margin-right: 27rpx !important; }

.mv-27 { margin-top: 27rpx !important; margin-bottom: 27rpx !important; }

.mb-27 { margin-bottom: 27rpx !important; }

.mr-27 { margin-right: 27rpx !important; }

.ml-27 { margin-left: 27rpx !important; }

.pt-27 { padding-top: 27rpx !important; }

.pb-27 { padding-bottom: 27rpx !important; }

.pr-27 { padding-right: 27rpx !important; }

.pl-27 { padding-left: 27rpx !important; }

.ph-27 { padding-left: 27rpx !important; padding-right: 27rpx !important; }

.pv-27 { padding-top: 27rpx !important; padding-bottom: 27rpx !important; }

.p-27 { padding: 27rpx; }

.m-27 { margin: 27rpx; }

.mt-28 { margin-top: 28rpx !important; }

.mh-28 { margin-left: 28rpx !important; margin-right: 28rpx !important; }

.mv-28 { margin-top: 28rpx !important; margin-bottom: 28rpx !important; }

.mb-28 { margin-bottom: 28rpx !important; }

.mr-28 { margin-right: 28rpx !important; }

.ml-28 { margin-left: 28rpx !important; }

.pt-28 { padding-top: 28rpx !important; }

.pb-28 { padding-bottom: 28rpx !important; }

.pr-28 { padding-right: 28rpx !important; }

.pl-28 { padding-left: 28rpx !important; }

.ph-28 { padding-left: 28rpx !important; padding-right: 28rpx !important; }

.pv-28 { padding-top: 28rpx !important; padding-bottom: 28rpx !important; }

.p-28 { padding: 28rpx; }

.m-28 { margin: 28rpx; }

.mt-29 { margin-top: 29rpx !important; }

.mh-29 { margin-left: 29rpx !important; margin-right: 29rpx !important; }

.mv-29 { margin-top: 29rpx !important; margin-bottom: 29rpx !important; }

.mb-29 { margin-bottom: 29rpx !important; }

.mr-29 { margin-right: 29rpx !important; }

.ml-29 { margin-left: 29rpx !important; }

.pt-29 { padding-top: 29rpx !important; }

.pb-29 { padding-bottom: 29rpx !important; }

.pr-29 { padding-right: 29rpx !important; }

.pl-29 { padding-left: 29rpx !important; }

.ph-29 { padding-left: 29rpx !important; padding-right: 29rpx !important; }

.pv-29 { padding-top: 29rpx !important; padding-bottom: 29rpx !important; }

.p-29 { padding: 29rpx; }

.m-29 { margin: 29rpx; }

.mt-30 { margin-top: 30rpx !important; }

.mh-30 { margin-left: 30rpx !important; margin-right: 30rpx !important; }

.mv-30 { margin-top: 30rpx !important; margin-bottom: 30rpx !important; }

.mb-30 { margin-bottom: 30rpx !important; }

.mr-30 { margin-right: 30rpx !important; }

.ml-30 { margin-left: 30rpx !important; }

.pt-30 { padding-top: 30rpx !important; }

.pb-30 { padding-bottom: 30rpx !important; }

.pr-30 { padding-right: 30rpx !important; }

.pl-30 { padding-left: 30rpx !important; }

.ph-30 { padding-left: 30rpx !important; padding-right: 30rpx !important; }

.pv-30 { padding-top: 30rpx !important; padding-bottom: 30rpx !important; }

.p-30 { padding: 30rpx; }

.m-30 { margin: 30rpx; }

.mt-31 { margin-top: 31rpx !important; }

.mh-31 { margin-left: 31rpx !important; margin-right: 31rpx !important; }

.mv-31 { margin-top: 31rpx !important; margin-bottom: 31rpx !important; }

.mb-31 { margin-bottom: 31rpx !important; }

.mr-31 { margin-right: 31rpx !important; }

.ml-31 { margin-left: 31rpx !important; }

.pt-31 { padding-top: 31rpx !important; }

.pb-31 { padding-bottom: 31rpx !important; }

.pr-31 { padding-right: 31rpx !important; }

.pl-31 { padding-left: 31rpx !important; }

.ph-31 { padding-left: 31rpx !important; padding-right: 31rpx !important; }

.pv-31 { padding-top: 31rpx !important; padding-bottom: 31rpx !important; }

.p-31 { padding: 31rpx; }

.m-31 { margin: 31rpx; }

.mt-32 { margin-top: 32rpx !important; }

.mh-32 { margin-left: 32rpx !important; margin-right: 32rpx !important; }

.mv-32 { margin-top: 32rpx !important; margin-bottom: 32rpx !important; }

.mb-32 { margin-bottom: 32rpx !important; }

.mr-32 { margin-right: 32rpx !important; }

.ml-32 { margin-left: 32rpx !important; }

.pt-32 { padding-top: 32rpx !important; }

.pb-32 { padding-bottom: 32rpx !important; }

.pr-32 { padding-right: 32rpx !important; }

.pl-32 { padding-left: 32rpx !important; }

.ph-32 { padding-left: 32rpx !important; padding-right: 32rpx !important; }

.pv-32 { padding-top: 32rpx !important; padding-bottom: 32rpx !important; }

.p-32 { padding: 32rpx; }

.m-32 { margin: 32rpx; }

.mt-33 { margin-top: 33rpx !important; }

.mh-33 { margin-left: 33rpx !important; margin-right: 33rpx !important; }

.mv-33 { margin-top: 33rpx !important; margin-bottom: 33rpx !important; }

.mb-33 { margin-bottom: 33rpx !important; }

.mr-33 { margin-right: 33rpx !important; }

.ml-33 { margin-left: 33rpx !important; }

.pt-33 { padding-top: 33rpx !important; }

.pb-33 { padding-bottom: 33rpx !important; }

.pr-33 { padding-right: 33rpx !important; }

.pl-33 { padding-left: 33rpx !important; }

.ph-33 { padding-left: 33rpx !important; padding-right: 33rpx !important; }

.pv-33 { padding-top: 33rpx !important; padding-bottom: 33rpx !important; }

.p-33 { padding: 33rpx; }

.m-33 { margin: 33rpx; }

.mt-34 { margin-top: 34rpx !important; }

.mh-34 { margin-left: 34rpx !important; margin-right: 34rpx !important; }

.mv-34 { margin-top: 34rpx !important; margin-bottom: 34rpx !important; }

.mb-34 { margin-bottom: 34rpx !important; }

.mr-34 { margin-right: 34rpx !important; }

.ml-34 { margin-left: 34rpx !important; }

.pt-34 { padding-top: 34rpx !important; }

.pb-34 { padding-bottom: 34rpx !important; }

.pr-34 { padding-right: 34rpx !important; }

.pl-34 { padding-left: 34rpx !important; }

.ph-34 { padding-left: 34rpx !important; padding-right: 34rpx !important; }

.pv-34 { padding-top: 34rpx !important; padding-bottom: 34rpx !important; }

.p-34 { padding: 34rpx; }

.m-34 { margin: 34rpx; }

.mt-35 { margin-top: 35rpx !important; }

.mh-35 { margin-left: 35rpx !important; margin-right: 35rpx !important; }

.mv-35 { margin-top: 35rpx !important; margin-bottom: 35rpx !important; }

.mb-35 { margin-bottom: 35rpx !important; }

.mr-35 { margin-right: 35rpx !important; }

.ml-35 { margin-left: 35rpx !important; }

.pt-35 { padding-top: 35rpx !important; }

.pb-35 { padding-bottom: 35rpx !important; }

.pr-35 { padding-right: 35rpx !important; }

.pl-35 { padding-left: 35rpx !important; }

.ph-35 { padding-left: 35rpx !important; padding-right: 35rpx !important; }

.pv-35 { padding-top: 35rpx !important; padding-bottom: 35rpx !important; }

.p-35 { padding: 35rpx; }

.m-35 { margin: 35rpx; }

.mt-36 { margin-top: 36rpx !important; }

.mh-36 { margin-left: 36rpx !important; margin-right: 36rpx !important; }

.mv-36 { margin-top: 36rpx !important; margin-bottom: 36rpx !important; }

.mb-36 { margin-bottom: 36rpx !important; }

.mr-36 { margin-right: 36rpx !important; }

.ml-36 { margin-left: 36rpx !important; }

.pt-36 { padding-top: 36rpx !important; }

.pb-36 { padding-bottom: 36rpx !important; }

.pr-36 { padding-right: 36rpx !important; }

.pl-36 { padding-left: 36rpx !important; }

.ph-36 { padding-left: 36rpx !important; padding-right: 36rpx !important; }

.pv-36 { padding-top: 36rpx !important; padding-bottom: 36rpx !important; }

.p-36 { padding: 36rpx; }

.m-36 { margin: 36rpx; }

.mt-37 { margin-top: 37rpx !important; }

.mh-37 { margin-left: 37rpx !important; margin-right: 37rpx !important; }

.mv-37 { margin-top: 37rpx !important; margin-bottom: 37rpx !important; }

.mb-37 { margin-bottom: 37rpx !important; }

.mr-37 { margin-right: 37rpx !important; }

.ml-37 { margin-left: 37rpx !important; }

.pt-37 { padding-top: 37rpx !important; }

.pb-37 { padding-bottom: 37rpx !important; }

.pr-37 { padding-right: 37rpx !important; }

.pl-37 { padding-left: 37rpx !important; }

.ph-37 { padding-left: 37rpx !important; padding-right: 37rpx !important; }

.pv-37 { padding-top: 37rpx !important; padding-bottom: 37rpx !important; }

.p-37 { padding: 37rpx; }

.m-37 { margin: 37rpx; }

.mt-38 { margin-top: 38rpx !important; }

.mh-38 { margin-left: 38rpx !important; margin-right: 38rpx !important; }

.mv-38 { margin-top: 38rpx !important; margin-bottom: 38rpx !important; }

.mb-38 { margin-bottom: 38rpx !important; }

.mr-38 { margin-right: 38rpx !important; }

.ml-38 { margin-left: 38rpx !important; }

.pt-38 { padding-top: 38rpx !important; }

.pb-38 { padding-bottom: 38rpx !important; }

.pr-38 { padding-right: 38rpx !important; }

.pl-38 { padding-left: 38rpx !important; }

.ph-38 { padding-left: 38rpx !important; padding-right: 38rpx !important; }

.pv-38 { padding-top: 38rpx !important; padding-bottom: 38rpx !important; }

.p-38 { padding: 38rpx; }

.m-38 { margin: 38rpx; }

.mt-39 { margin-top: 39rpx !important; }

.mh-39 { margin-left: 39rpx !important; margin-right: 39rpx !important; }

.mv-39 { margin-top: 39rpx !important; margin-bottom: 39rpx !important; }

.mb-39 { margin-bottom: 39rpx !important; }

.mr-39 { margin-right: 39rpx !important; }

.ml-39 { margin-left: 39rpx !important; }

.pt-39 { padding-top: 39rpx !important; }

.pb-39 { padding-bottom: 39rpx !important; }

.pr-39 { padding-right: 39rpx !important; }

.pl-39 { padding-left: 39rpx !important; }

.ph-39 { padding-left: 39rpx !important; padding-right: 39rpx !important; }

.pv-39 { padding-top: 39rpx !important; padding-bottom: 39rpx !important; }

.p-39 { padding: 39rpx; }

.m-39 { margin: 39rpx; }

.mt-40 { margin-top: 40rpx !important; }

.mh-40 { margin-left: 40rpx !important; margin-right: 40rpx !important; }

.mv-40 { margin-top: 40rpx !important; margin-bottom: 40rpx !important; }

.mb-40 { margin-bottom: 40rpx !important; }

.mr-40 { margin-right: 40rpx !important; }

.ml-40 { margin-left: 40rpx !important; }

.pt-40 { padding-top: 40rpx !important; }

.pb-40 { padding-bottom: 40rpx !important; }

.pr-40 { padding-right: 40rpx !important; }

.pl-40 { padding-left: 40rpx !important; }

.ph-40 { padding-left: 40rpx !important; padding-right: 40rpx !important; }

.pv-40 { padding-top: 40rpx !important; padding-bottom: 40rpx !important; }

.p-40 { padding: 40rpx; }

.m-40 { margin: 40rpx; }

.mt-41 { margin-top: 41rpx !important; }

.mh-41 { margin-left: 41rpx !important; margin-right: 41rpx !important; }

.mv-41 { margin-top: 41rpx !important; margin-bottom: 41rpx !important; }

.mb-41 { margin-bottom: 41rpx !important; }

.mr-41 { margin-right: 41rpx !important; }

.ml-41 { margin-left: 41rpx !important; }

.pt-41 { padding-top: 41rpx !important; }

.pb-41 { padding-bottom: 41rpx !important; }

.pr-41 { padding-right: 41rpx !important; }

.pl-41 { padding-left: 41rpx !important; }

.ph-41 { padding-left: 41rpx !important; padding-right: 41rpx !important; }

.pv-41 { padding-top: 41rpx !important; padding-bottom: 41rpx !important; }

.p-41 { padding: 41rpx; }

.m-41 { margin: 41rpx; }

.mt-42 { margin-top: 42rpx !important; }

.mh-42 { margin-left: 42rpx !important; margin-right: 42rpx !important; }

.mv-42 { margin-top: 42rpx !important; margin-bottom: 42rpx !important; }

.mb-42 { margin-bottom: 42rpx !important; }

.mr-42 { margin-right: 42rpx !important; }

.ml-42 { margin-left: 42rpx !important; }

.pt-42 { padding-top: 42rpx !important; }

.pb-42 { padding-bottom: 42rpx !important; }

.pr-42 { padding-right: 42rpx !important; }

.pl-42 { padding-left: 42rpx !important; }

.ph-42 { padding-left: 42rpx !important; padding-right: 42rpx !important; }

.pv-42 { padding-top: 42rpx !important; padding-bottom: 42rpx !important; }

.p-42 { padding: 42rpx; }

.m-42 { margin: 42rpx; }

.mt-43 { margin-top: 43rpx !important; }

.mh-43 { margin-left: 43rpx !important; margin-right: 43rpx !important; }

.mv-43 { margin-top: 43rpx !important; margin-bottom: 43rpx !important; }

.mb-43 { margin-bottom: 43rpx !important; }

.mr-43 { margin-right: 43rpx !important; }

.ml-43 { margin-left: 43rpx !important; }

.pt-43 { padding-top: 43rpx !important; }

.pb-43 { padding-bottom: 43rpx !important; }

.pr-43 { padding-right: 43rpx !important; }

.pl-43 { padding-left: 43rpx !important; }

.ph-43 { padding-left: 43rpx !important; padding-right: 43rpx !important; }

.pv-43 { padding-top: 43rpx !important; padding-bottom: 43rpx !important; }

.p-43 { padding: 43rpx; }

.m-43 { margin: 43rpx; }

.mt-44 { margin-top: 44rpx !important; }

.mh-44 { margin-left: 44rpx !important; margin-right: 44rpx !important; }

.mv-44 { margin-top: 44rpx !important; margin-bottom: 44rpx !important; }

.mb-44 { margin-bottom: 44rpx !important; }

.mr-44 { margin-right: 44rpx !important; }

.ml-44 { margin-left: 44rpx !important; }

.pt-44 { padding-top: 44rpx !important; }

.pb-44 { padding-bottom: 44rpx !important; }

.pr-44 { padding-right: 44rpx !important; }

.pl-44 { padding-left: 44rpx !important; }

.ph-44 { padding-left: 44rpx !important; padding-right: 44rpx !important; }

.pv-44 { padding-top: 44rpx !important; padding-bottom: 44rpx !important; }

.p-44 { padding: 44rpx; }

.m-44 { margin: 44rpx; }

.mt-45 { margin-top: 45rpx !important; }

.mh-45 { margin-left: 45rpx !important; margin-right: 45rpx !important; }

.mv-45 { margin-top: 45rpx !important; margin-bottom: 45rpx !important; }

.mb-45 { margin-bottom: 45rpx !important; }

.mr-45 { margin-right: 45rpx !important; }

.ml-45 { margin-left: 45rpx !important; }

.pt-45 { padding-top: 45rpx !important; }

.pb-45 { padding-bottom: 45rpx !important; }

.pr-45 { padding-right: 45rpx !important; }

.pl-45 { padding-left: 45rpx !important; }

.ph-45 { padding-left: 45rpx !important; padding-right: 45rpx !important; }

.pv-45 { padding-top: 45rpx !important; padding-bottom: 45rpx !important; }

.p-45 { padding: 45rpx; }

.m-45 { margin: 45rpx; }

.mt-46 { margin-top: 46rpx !important; }

.mh-46 { margin-left: 46rpx !important; margin-right: 46rpx !important; }

.mv-46 { margin-top: 46rpx !important; margin-bottom: 46rpx !important; }

.mb-46 { margin-bottom: 46rpx !important; }

.mr-46 { margin-right: 46rpx !important; }

.ml-46 { margin-left: 46rpx !important; }

.pt-46 { padding-top: 46rpx !important; }

.pb-46 { padding-bottom: 46rpx !important; }

.pr-46 { padding-right: 46rpx !important; }

.pl-46 { padding-left: 46rpx !important; }

.ph-46 { padding-left: 46rpx !important; padding-right: 46rpx !important; }

.pv-46 { padding-top: 46rpx !important; padding-bottom: 46rpx !important; }

.p-46 { padding: 46rpx; }

.m-46 { margin: 46rpx; }

.mt-47 { margin-top: 47rpx !important; }

.mh-47 { margin-left: 47rpx !important; margin-right: 47rpx !important; }

.mv-47 { margin-top: 47rpx !important; margin-bottom: 47rpx !important; }

.mb-47 { margin-bottom: 47rpx !important; }

.mr-47 { margin-right: 47rpx !important; }

.ml-47 { margin-left: 47rpx !important; }

.pt-47 { padding-top: 47rpx !important; }

.pb-47 { padding-bottom: 47rpx !important; }

.pr-47 { padding-right: 47rpx !important; }

.pl-47 { padding-left: 47rpx !important; }

.ph-47 { padding-left: 47rpx !important; padding-right: 47rpx !important; }

.pv-47 { padding-top: 47rpx !important; padding-bottom: 47rpx !important; }

.p-47 { padding: 47rpx; }

.m-47 { margin: 47rpx; }

.mt-48 { margin-top: 48rpx !important; }

.mh-48 { margin-left: 48rpx !important; margin-right: 48rpx !important; }

.mv-48 { margin-top: 48rpx !important; margin-bottom: 48rpx !important; }

.mb-48 { margin-bottom: 48rpx !important; }

.mr-48 { margin-right: 48rpx !important; }

.ml-48 { margin-left: 48rpx !important; }

.pt-48 { padding-top: 48rpx !important; }

.pb-48 { padding-bottom: 48rpx !important; }

.pr-48 { padding-right: 48rpx !important; }

.pl-48 { padding-left: 48rpx !important; }

.ph-48 { padding-left: 48rpx !important; padding-right: 48rpx !important; }

.pv-48 { padding-top: 48rpx !important; padding-bottom: 48rpx !important; }

.p-48 { padding: 48rpx; }

.m-48 { margin: 48rpx; }

.mt-49 { margin-top: 49rpx !important; }

.mh-49 { margin-left: 49rpx !important; margin-right: 49rpx !important; }

.mv-49 { margin-top: 49rpx !important; margin-bottom: 49rpx !important; }

.mb-49 { margin-bottom: 49rpx !important; }

.mr-49 { margin-right: 49rpx !important; }

.ml-49 { margin-left: 49rpx !important; }

.pt-49 { padding-top: 49rpx !important; }

.pb-49 { padding-bottom: 49rpx !important; }

.pr-49 { padding-right: 49rpx !important; }

.pl-49 { padding-left: 49rpx !important; }

.ph-49 { padding-left: 49rpx !important; padding-right: 49rpx !important; }

.pv-49 { padding-top: 49rpx !important; padding-bottom: 49rpx !important; }

.p-49 { padding: 49rpx; }

.m-49 { margin: 49rpx; }

.mt-50 { margin-top: 50rpx !important; }

.mh-50 { margin-left: 50rpx !important; margin-right: 50rpx !important; }

.mv-50 { margin-top: 50rpx !important; margin-bottom: 50rpx !important; }

.mb-50 { margin-bottom: 50rpx !important; }

.mr-50 { margin-right: 50rpx !important; }

.ml-50 { margin-left: 50rpx !important; }

.pt-50 { padding-top: 50rpx !important; }

.pb-50 { padding-bottom: 50rpx !important; }

.pr-50 { padding-right: 50rpx !important; }

.pl-50 { padding-left: 50rpx !important; }

.ph-50 { padding-left: 50rpx !important; padding-right: 50rpx !important; }

.pv-50 { padding-top: 50rpx !important; padding-bottom: 50rpx !important; }

.p-50 { padding: 50rpx; }

.m-50 { margin: 50rpx; }

.mt-51 { margin-top: 51rpx !important; }

.mh-51 { margin-left: 51rpx !important; margin-right: 51rpx !important; }

.mv-51 { margin-top: 51rpx !important; margin-bottom: 51rpx !important; }

.mb-51 { margin-bottom: 51rpx !important; }

.mr-51 { margin-right: 51rpx !important; }

.ml-51 { margin-left: 51rpx !important; }

.pt-51 { padding-top: 51rpx !important; }

.pb-51 { padding-bottom: 51rpx !important; }

.pr-51 { padding-right: 51rpx !important; }

.pl-51 { padding-left: 51rpx !important; }

.ph-51 { padding-left: 51rpx !important; padding-right: 51rpx !important; }

.pv-51 { padding-top: 51rpx !important; padding-bottom: 51rpx !important; }

.p-51 { padding: 51rpx; }

.m-51 { margin: 51rpx; }

.mt-52 { margin-top: 52rpx !important; }

.mh-52 { margin-left: 52rpx !important; margin-right: 52rpx !important; }

.mv-52 { margin-top: 52rpx !important; margin-bottom: 52rpx !important; }

.mb-52 { margin-bottom: 52rpx !important; }

.mr-52 { margin-right: 52rpx !important; }

.ml-52 { margin-left: 52rpx !important; }

.pt-52 { padding-top: 52rpx !important; }

.pb-52 { padding-bottom: 52rpx !important; }

.pr-52 { padding-right: 52rpx !important; }

.pl-52 { padding-left: 52rpx !important; }

.ph-52 { padding-left: 52rpx !important; padding-right: 52rpx !important; }

.pv-52 { padding-top: 52rpx !important; padding-bottom: 52rpx !important; }

.p-52 { padding: 52rpx; }

.m-52 { margin: 52rpx; }

.mt-53 { margin-top: 53rpx !important; }

.mh-53 { margin-left: 53rpx !important; margin-right: 53rpx !important; }

.mv-53 { margin-top: 53rpx !important; margin-bottom: 53rpx !important; }

.mb-53 { margin-bottom: 53rpx !important; }

.mr-53 { margin-right: 53rpx !important; }

.ml-53 { margin-left: 53rpx !important; }

.pt-53 { padding-top: 53rpx !important; }

.pb-53 { padding-bottom: 53rpx !important; }

.pr-53 { padding-right: 53rpx !important; }

.pl-53 { padding-left: 53rpx !important; }

.ph-53 { padding-left: 53rpx !important; padding-right: 53rpx !important; }

.pv-53 { padding-top: 53rpx !important; padding-bottom: 53rpx !important; }

.p-53 { padding: 53rpx; }

.m-53 { margin: 53rpx; }

.mt-54 { margin-top: 54rpx !important; }

.mh-54 { margin-left: 54rpx !important; margin-right: 54rpx !important; }

.mv-54 { margin-top: 54rpx !important; margin-bottom: 54rpx !important; }

.mb-54 { margin-bottom: 54rpx !important; }

.mr-54 { margin-right: 54rpx !important; }

.ml-54 { margin-left: 54rpx !important; }

.pt-54 { padding-top: 54rpx !important; }

.pb-54 { padding-bottom: 54rpx !important; }

.pr-54 { padding-right: 54rpx !important; }

.pl-54 { padding-left: 54rpx !important; }

.ph-54 { padding-left: 54rpx !important; padding-right: 54rpx !important; }

.pv-54 { padding-top: 54rpx !important; padding-bottom: 54rpx !important; }

.p-54 { padding: 54rpx; }

.m-54 { margin: 54rpx; }

.mt-55 { margin-top: 55rpx !important; }

.mh-55 { margin-left: 55rpx !important; margin-right: 55rpx !important; }

.mv-55 { margin-top: 55rpx !important; margin-bottom: 55rpx !important; }

.mb-55 { margin-bottom: 55rpx !important; }

.mr-55 { margin-right: 55rpx !important; }

.ml-55 { margin-left: 55rpx !important; }

.pt-55 { padding-top: 55rpx !important; }

.pb-55 { padding-bottom: 55rpx !important; }

.pr-55 { padding-right: 55rpx !important; }

.pl-55 { padding-left: 55rpx !important; }

.ph-55 { padding-left: 55rpx !important; padding-right: 55rpx !important; }

.pv-55 { padding-top: 55rpx !important; padding-bottom: 55rpx !important; }

.p-55 { padding: 55rpx; }

.m-55 { margin: 55rpx; }

.mt-56 { margin-top: 56rpx !important; }

.mh-56 { margin-left: 56rpx !important; margin-right: 56rpx !important; }

.mv-56 { margin-top: 56rpx !important; margin-bottom: 56rpx !important; }

.mb-56 { margin-bottom: 56rpx !important; }

.mr-56 { margin-right: 56rpx !important; }

.ml-56 { margin-left: 56rpx !important; }

.pt-56 { padding-top: 56rpx !important; }

.pb-56 { padding-bottom: 56rpx !important; }

.pr-56 { padding-right: 56rpx !important; }

.pl-56 { padding-left: 56rpx !important; }

.ph-56 { padding-left: 56rpx !important; padding-right: 56rpx !important; }

.pv-56 { padding-top: 56rpx !important; padding-bottom: 56rpx !important; }

.p-56 { padding: 56rpx; }

.m-56 { margin: 56rpx; }

.mt-57 { margin-top: 57rpx !important; }

.mh-57 { margin-left: 57rpx !important; margin-right: 57rpx !important; }

.mv-57 { margin-top: 57rpx !important; margin-bottom: 57rpx !important; }

.mb-57 { margin-bottom: 57rpx !important; }

.mr-57 { margin-right: 57rpx !important; }

.ml-57 { margin-left: 57rpx !important; }

.pt-57 { padding-top: 57rpx !important; }

.pb-57 { padding-bottom: 57rpx !important; }

.pr-57 { padding-right: 57rpx !important; }

.pl-57 { padding-left: 57rpx !important; }

.ph-57 { padding-left: 57rpx !important; padding-right: 57rpx !important; }

.pv-57 { padding-top: 57rpx !important; padding-bottom: 57rpx !important; }

.p-57 { padding: 57rpx; }

.m-57 { margin: 57rpx; }

.mt-58 { margin-top: 58rpx !important; }

.mh-58 { margin-left: 58rpx !important; margin-right: 58rpx !important; }

.mv-58 { margin-top: 58rpx !important; margin-bottom: 58rpx !important; }

.mb-58 { margin-bottom: 58rpx !important; }

.mr-58 { margin-right: 58rpx !important; }

.ml-58 { margin-left: 58rpx !important; }

.pt-58 { padding-top: 58rpx !important; }

.pb-58 { padding-bottom: 58rpx !important; }

.pr-58 { padding-right: 58rpx !important; }

.pl-58 { padding-left: 58rpx !important; }

.ph-58 { padding-left: 58rpx !important; padding-right: 58rpx !important; }

.pv-58 { padding-top: 58rpx !important; padding-bottom: 58rpx !important; }

.p-58 { padding: 58rpx; }

.m-58 { margin: 58rpx; }

.mt-59 { margin-top: 59rpx !important; }

.mh-59 { margin-left: 59rpx !important; margin-right: 59rpx !important; }

.mv-59 { margin-top: 59rpx !important; margin-bottom: 59rpx !important; }

.mb-59 { margin-bottom: 59rpx !important; }

.mr-59 { margin-right: 59rpx !important; }

.ml-59 { margin-left: 59rpx !important; }

.pt-59 { padding-top: 59rpx !important; }

.pb-59 { padding-bottom: 59rpx !important; }

.pr-59 { padding-right: 59rpx !important; }

.pl-59 { padding-left: 59rpx !important; }

.ph-59 { padding-left: 59rpx !important; padding-right: 59rpx !important; }

.pv-59 { padding-top: 59rpx !important; padding-bottom: 59rpx !important; }

.p-59 { padding: 59rpx; }

.m-59 { margin: 59rpx; }

.mt-60 { margin-top: 60rpx !important; }

.mh-60 { margin-left: 60rpx !important; margin-right: 60rpx !important; }

.mv-60 { margin-top: 60rpx !important; margin-bottom: 60rpx !important; }

.mb-60 { margin-bottom: 60rpx !important; }

.mr-60 { margin-right: 60rpx !important; }

.ml-60 { margin-left: 60rpx !important; }

.pt-60 { padding-top: 60rpx !important; }

.pb-60 { padding-bottom: 60rpx !important; }

.pr-60 { padding-right: 60rpx !important; }

.pl-60 { padding-left: 60rpx !important; }

.ph-60 { padding-left: 60rpx !important; padding-right: 60rpx !important; }

.pv-60 { padding-top: 60rpx !important; padding-bottom: 60rpx !important; }

.p-60 { padding: 60rpx; }

.m-60 { margin: 60rpx; }

.w98 { margin: 0 auto; }

.w96 { margin: 0 auto; }

.w95 { margin: 0 auto; }

.w90 { margin: 0 auto; }

.w85 { margin: 0 auto; }

.w-auto { width: auto; }

.h-auto { height: auto; }

.w1 { width: 1%; }

.h1 { height: 1%; }

.vw-1 { width: 1vw; }

.vh-1 { height: 1vh; }

.w2 { width: 2%; }

.h2 { height: 2%; }

.vw-2 { width: 2vw; }

.vh-2 { height: 2vh; }

.w3 { width: 3%; }

.h3 { height: 3%; }

.vw-3 { width: 3vw; }

.vh-3 { height: 3vh; }

.w4 { width: 4%; }

.h4 { height: 4%; }

.vw-4 { width: 4vw; }

.vh-4 { height: 4vh; }

.w5 { width: 5%; }

.h5 { height: 5%; }

.vw-5 { width: 5vw; }

.vh-5 { height: 5vh; }

.w6 { width: 6%; }

.h6 { height: 6%; }

.vw-6 { width: 6vw; }

.vh-6 { height: 6vh; }

.w7 { width: 7%; }

.h7 { height: 7%; }

.vw-7 { width: 7vw; }

.vh-7 { height: 7vh; }

.w8 { width: 8%; }

.h8 { height: 8%; }

.vw-8 { width: 8vw; }

.vh-8 { height: 8vh; }

.w9 { width: 9%; }

.h9 { height: 9%; }

.vw-9 { width: 9vw; }

.vh-9 { height: 9vh; }

.w10 { width: 10%; }

.h10 { height: 10%; }

.vw-10 { width: 10vw; }

.vh-10 { height: 10vh; }

.w11 { width: 11%; }

.h11 { height: 11%; }

.vw-11 { width: 11vw; }

.vh-11 { height: 11vh; }

.w12 { width: 12%; }

.h12 { height: 12%; }

.vw-12 { width: 12vw; }

.vh-12 { height: 12vh; }

.w13 { width: 13%; }

.h13 { height: 13%; }

.vw-13 { width: 13vw; }

.vh-13 { height: 13vh; }

.w14 { width: 14%; }

.h14 { height: 14%; }

.vw-14 { width: 14vw; }

.vh-14 { height: 14vh; }

.w15 { width: 15%; }

.h15 { height: 15%; }

.vw-15 { width: 15vw; }

.vh-15 { height: 15vh; }

.w16 { width: 16%; }

.h16 { height: 16%; }

.vw-16 { width: 16vw; }

.vh-16 { height: 16vh; }

.w17 { width: 17%; }

.h17 { height: 17%; }

.vw-17 { width: 17vw; }

.vh-17 { height: 17vh; }

.w18 { width: 18%; }

.h18 { height: 18%; }

.vw-18 { width: 18vw; }

.vh-18 { height: 18vh; }

.w19 { width: 19%; }

.h19 { height: 19%; }

.vw-19 { width: 19vw; }

.vh-19 { height: 19vh; }

.w20 { width: 20%; }

.h20 { height: 20%; }

.vw-20 { width: 20vw; }

.vh-20 { height: 20vh; }

.w21 { width: 21%; }

.h21 { height: 21%; }

.vw-21 { width: 21vw; }

.vh-21 { height: 21vh; }

.w22 { width: 22%; }

.h22 { height: 22%; }

.vw-22 { width: 22vw; }

.vh-22 { height: 22vh; }

.w23 { width: 23%; }

.h23 { height: 23%; }

.vw-23 { width: 23vw; }

.vh-23 { height: 23vh; }

.w24 { width: 24%; }

.h24 { height: 24%; }

.vw-24 { width: 24vw; }

.vh-24 { height: 24vh; }

.w25 { width: 25%; }

.h25 { height: 25%; }

.vw-25 { width: 25vw; }

.vh-25 { height: 25vh; }

.w26 { width: 26%; }

.h26 { height: 26%; }

.vw-26 { width: 26vw; }

.vh-26 { height: 26vh; }

.w27 { width: 27%; }

.h27 { height: 27%; }

.vw-27 { width: 27vw; }

.vh-27 { height: 27vh; }

.w28 { width: 28%; }

.h28 { height: 28%; }

.vw-28 { width: 28vw; }

.vh-28 { height: 28vh; }

.w29 { width: 29%; }

.h29 { height: 29%; }

.vw-29 { width: 29vw; }

.vh-29 { height: 29vh; }

.w30 { width: 30%; }

.h30 { height: 30%; }

.vw-30 { width: 30vw; }

.vh-30 { height: 30vh; }

.w31 { width: 31%; }

.h31 { height: 31%; }

.vw-31 { width: 31vw; }

.vh-31 { height: 31vh; }

.w32 { width: 32%; }

.h32 { height: 32%; }

.vw-32 { width: 32vw; }

.vh-32 { height: 32vh; }

.w33 { width: 33%; }

.h33 { height: 33%; }

.vw-33 { width: 33vw; }

.vh-33 { height: 33vh; }

.w34 { width: 34%; }

.h34 { height: 34%; }

.vw-34 { width: 34vw; }

.vh-34 { height: 34vh; }

.w35 { width: 35%; }

.h35 { height: 35%; }

.vw-35 { width: 35vw; }

.vh-35 { height: 35vh; }

.w36 { width: 36%; }

.h36 { height: 36%; }

.vw-36 { width: 36vw; }

.vh-36 { height: 36vh; }

.w37 { width: 37%; }

.h37 { height: 37%; }

.vw-37 { width: 37vw; }

.vh-37 { height: 37vh; }

.w38 { width: 38%; }

.h38 { height: 38%; }

.vw-38 { width: 38vw; }

.vh-38 { height: 38vh; }

.w39 { width: 39%; }

.h39 { height: 39%; }

.vw-39 { width: 39vw; }

.vh-39 { height: 39vh; }

.w40 { width: 40%; }

.h40 { height: 40%; }

.vw-40 { width: 40vw; }

.vh-40 { height: 40vh; }

.w41 { width: 41%; }

.h41 { height: 41%; }

.vw-41 { width: 41vw; }

.vh-41 { height: 41vh; }

.w42 { width: 42%; }

.h42 { height: 42%; }

.vw-42 { width: 42vw; }

.vh-42 { height: 42vh; }

.w43 { width: 43%; }

.h43 { height: 43%; }

.vw-43 { width: 43vw; }

.vh-43 { height: 43vh; }

.w44 { width: 44%; }

.h44 { height: 44%; }

.vw-44 { width: 44vw; }

.vh-44 { height: 44vh; }

.w45 { width: 45%; }

.h45 { height: 45%; }

.vw-45 { width: 45vw; }

.vh-45 { height: 45vh; }

.w46 { width: 46%; }

.h46 { height: 46%; }

.vw-46 { width: 46vw; }

.vh-46 { height: 46vh; }

.w47 { width: 47%; }

.h47 { height: 47%; }

.vw-47 { width: 47vw; }

.vh-47 { height: 47vh; }

.w48 { width: 48%; }

.h48 { height: 48%; }

.vw-48 { width: 48vw; }

.vh-48 { height: 48vh; }

.w49 { width: 49%; }

.h49 { height: 49%; }

.vw-49 { width: 49vw; }

.vh-49 { height: 49vh; }

.w50 { width: 50%; }

.h50 { height: 50%; }

.vw-50 { width: 50vw; }

.vh-50 { height: 50vh; }

.w51 { width: 51%; }

.h51 { height: 51%; }

.vw-51 { width: 51vw; }

.vh-51 { height: 51vh; }

.w52 { width: 52%; }

.h52 { height: 52%; }

.vw-52 { width: 52vw; }

.vh-52 { height: 52vh; }

.w53 { width: 53%; }

.h53 { height: 53%; }

.vw-53 { width: 53vw; }

.vh-53 { height: 53vh; }

.w54 { width: 54%; }

.h54 { height: 54%; }

.vw-54 { width: 54vw; }

.vh-54 { height: 54vh; }

.w55 { width: 55%; }

.h55 { height: 55%; }

.vw-55 { width: 55vw; }

.vh-55 { height: 55vh; }

.w56 { width: 56%; }

.h56 { height: 56%; }

.vw-56 { width: 56vw; }

.vh-56 { height: 56vh; }

.w57 { width: 57%; }

.h57 { height: 57%; }

.vw-57 { width: 57vw; }

.vh-57 { height: 57vh; }

.w58 { width: 58%; }

.h58 { height: 58%; }

.vw-58 { width: 58vw; }

.vh-58 { height: 58vh; }

.w59 { width: 59%; }

.h59 { height: 59%; }

.vw-59 { width: 59vw; }

.vh-59 { height: 59vh; }

.w60 { width: 60%; }

.h60 { height: 60%; }

.vw-60 { width: 60vw; }

.vh-60 { height: 60vh; }

.w61 { width: 61%; }

.h61 { height: 61%; }

.vw-61 { width: 61vw; }

.vh-61 { height: 61vh; }

.w62 { width: 62%; }

.h62 { height: 62%; }

.vw-62 { width: 62vw; }

.vh-62 { height: 62vh; }

.w63 { width: 63%; }

.h63 { height: 63%; }

.vw-63 { width: 63vw; }

.vh-63 { height: 63vh; }

.w64 { width: 64%; }

.h64 { height: 64%; }

.vw-64 { width: 64vw; }

.vh-64 { height: 64vh; }

.w65 { width: 65%; }

.h65 { height: 65%; }

.vw-65 { width: 65vw; }

.vh-65 { height: 65vh; }

.w66 { width: 66%; }

.h66 { height: 66%; }

.vw-66 { width: 66vw; }

.vh-66 { height: 66vh; }

.w67 { width: 67%; }

.h67 { height: 67%; }

.vw-67 { width: 67vw; }

.vh-67 { height: 67vh; }

.w68 { width: 68%; }

.h68 { height: 68%; }

.vw-68 { width: 68vw; }

.vh-68 { height: 68vh; }

.w69 { width: 69%; }

.h69 { height: 69%; }

.vw-69 { width: 69vw; }

.vh-69 { height: 69vh; }

.w70 { width: 70%; }

.h70 { height: 70%; }

.vw-70 { width: 70vw; }

.vh-70 { height: 70vh; }

.w71 { width: 71%; }

.h71 { height: 71%; }

.vw-71 { width: 71vw; }

.vh-71 { height: 71vh; }

.w72 { width: 72%; }

.h72 { height: 72%; }

.vw-72 { width: 72vw; }

.vh-72 { height: 72vh; }

.w73 { width: 73%; }

.h73 { height: 73%; }

.vw-73 { width: 73vw; }

.vh-73 { height: 73vh; }

.w74 { width: 74%; }

.h74 { height: 74%; }

.vw-74 { width: 74vw; }

.vh-74 { height: 74vh; }

.w75 { width: 75%; }

.h75 { height: 75%; }

.vw-75 { width: 75vw; }

.vh-75 { height: 75vh; }

.w76 { width: 76%; }

.h76 { height: 76%; }

.vw-76 { width: 76vw; }

.vh-76 { height: 76vh; }

.w77 { width: 77%; }

.h77 { height: 77%; }

.vw-77 { width: 77vw; }

.vh-77 { height: 77vh; }

.w78 { width: 78%; }

.h78 { height: 78%; }

.vw-78 { width: 78vw; }

.vh-78 { height: 78vh; }

.w79 { width: 79%; }

.h79 { height: 79%; }

.vw-79 { width: 79vw; }

.vh-79 { height: 79vh; }

.w80 { width: 80%; }

.h80 { height: 80%; }

.vw-80 { width: 80vw; }

.vh-80 { height: 80vh; }

.w81 { width: 81%; }

.h81 { height: 81%; }

.vw-81 { width: 81vw; }

.vh-81 { height: 81vh; }

.w82 { width: 82%; }

.h82 { height: 82%; }

.vw-82 { width: 82vw; }

.vh-82 { height: 82vh; }

.w83 { width: 83%; }

.h83 { height: 83%; }

.vw-83 { width: 83vw; }

.vh-83 { height: 83vh; }

.w84 { width: 84%; }

.h84 { height: 84%; }

.vw-84 { width: 84vw; }

.vh-84 { height: 84vh; }

.w85 { width: 85%; }

.h85 { height: 85%; }

.vw-85 { width: 85vw; }

.vh-85 { height: 85vh; }

.w86 { width: 86%; }

.h86 { height: 86%; }

.vw-86 { width: 86vw; }

.vh-86 { height: 86vh; }

.w87 { width: 87%; }

.h87 { height: 87%; }

.vw-87 { width: 87vw; }

.vh-87 { height: 87vh; }

.w88 { width: 88%; }

.h88 { height: 88%; }

.vw-88 { width: 88vw; }

.vh-88 { height: 88vh; }

.w89 { width: 89%; }

.h89 { height: 89%; }

.vw-89 { width: 89vw; }

.vh-89 { height: 89vh; }

.w90 { width: 90%; }

.h90 { height: 90%; }

.vw-90 { width: 90vw; }

.vh-90 { height: 90vh; }

.w91 { width: 91%; }

.h91 { height: 91%; }

.vw-91 { width: 91vw; }

.vh-91 { height: 91vh; }

.w92 { width: 92%; }

.h92 { height: 92%; }

.vw-92 { width: 92vw; }

.vh-92 { height: 92vh; }

.w93 { width: 93%; }

.h93 { height: 93%; }

.vw-93 { width: 93vw; }

.vh-93 { height: 93vh; }

.w94 { width: 94%; }

.h94 { height: 94%; }

.vw-94 { width: 94vw; }

.vh-94 { height: 94vh; }

.w95 { width: 95%; }

.h95 { height: 95%; }

.vw-95 { width: 95vw; }

.vh-95 { height: 95vh; }

.w96 { width: 96%; }

.h96 { height: 96%; }

.vw-96 { width: 96vw; }

.vh-96 { height: 96vh; }

.w97 { width: 97%; }

.h97 { height: 97%; }

.vw-97 { width: 97vw; }

.vh-97 { height: 97vh; }

.w98 { width: 98%; }

.h98 { height: 98%; }

.vw-98 { width: 98vw; }

.vh-98 { height: 98vh; }

.w99 { width: 99%; }

.h99 { height: 99%; }

.vw-99 { width: 99vw; }

.vh-99 { height: 99vh; }

.w100 { width: 100%; }

.h100 { height: 100%; }

.vw-100 { width: 100vw; }

.vh-100 { height: 100vh; }

.br-1 { border-radius: 1rpx; }

.br-left-top-1 { border-top-left-radius: 1rpx; }

.br-right-top-1 { border-top-right-radius: 1rpx; }

.br-2 { border-radius: 2rpx; }

.br-left-top-2 { border-top-left-radius: 2rpx; }

.br-right-top-2 { border-top-right-radius: 2rpx; }

.br-3 { border-radius: 3rpx; }

.br-left-top-3 { border-top-left-radius: 3rpx; }

.br-right-top-3 { border-top-right-radius: 3rpx; }

.br-4 { border-radius: 4rpx; }

.br-left-top-4 { border-top-left-radius: 4rpx; }

.br-right-top-4 { border-top-right-radius: 4rpx; }

.br-5 { border-radius: 5rpx; }

.br-left-top-5 { border-top-left-radius: 5rpx; }

.br-right-top-5 { border-top-right-radius: 5rpx; }

.br-6 { border-radius: 6rpx; }

.br-left-top-6 { border-top-left-radius: 6rpx; }

.br-right-top-6 { border-top-right-radius: 6rpx; }

.br-7 { border-radius: 7rpx; }

.br-left-top-7 { border-top-left-radius: 7rpx; }

.br-right-top-7 { border-top-right-radius: 7rpx; }

.br-8 { border-radius: 8rpx; }

.br-left-top-8 { border-top-left-radius: 8rpx; }

.br-right-top-8 { border-top-right-radius: 8rpx; }

.br-9 { border-radius: 9rpx; }

.br-left-top-9 { border-top-left-radius: 9rpx; }

.br-right-top-9 { border-top-right-radius: 9rpx; }

.br-10 { border-radius: 10rpx; }

.br-left-top-10 { border-top-left-radius: 10rpx; }

.br-right-top-10 { border-top-right-radius: 10rpx; }

.br-11 { border-radius: 11rpx; }

.br-left-top-11 { border-top-left-radius: 11rpx; }

.br-right-top-11 { border-top-right-radius: 11rpx; }

.br-12 { border-radius: 12rpx; }

.br-left-top-12 { border-top-left-radius: 12rpx; }

.br-right-top-12 { border-top-right-radius: 12rpx; }

.br-13 { border-radius: 13rpx; }

.br-left-top-13 { border-top-left-radius: 13rpx; }

.br-right-top-13 { border-top-right-radius: 13rpx; }

.br-14 { border-radius: 14rpx; }

.br-left-top-14 { border-top-left-radius: 14rpx; }

.br-right-top-14 { border-top-right-radius: 14rpx; }

.br-15 { border-radius: 15rpx; }

.br-left-top-15 { border-top-left-radius: 15rpx; }

.br-right-top-15 { border-top-right-radius: 15rpx; }

.br-16 { border-radius: 16rpx; }

.br-left-top-16 { border-top-left-radius: 16rpx; }

.br-right-top-16 { border-top-right-radius: 16rpx; }

.br-17 { border-radius: 17rpx; }

.br-left-top-17 { border-top-left-radius: 17rpx; }

.br-right-top-17 { border-top-right-radius: 17rpx; }

.br-18 { border-radius: 18rpx; }

.br-left-top-18 { border-top-left-radius: 18rpx; }

.br-right-top-18 { border-top-right-radius: 18rpx; }

.br-19 { border-radius: 19rpx; }

.br-left-top-19 { border-top-left-radius: 19rpx; }

.br-right-top-19 { border-top-right-radius: 19rpx; }

.br-20 { border-radius: 20rpx; }

.br-left-top-20 { border-top-left-radius: 20rpx; }

.br-right-top-20 { border-top-right-radius: 20rpx; }

.br-21 { border-radius: 21rpx; }

.br-left-top-21 { border-top-left-radius: 21rpx; }

.br-right-top-21 { border-top-right-radius: 21rpx; }

.br-22 { border-radius: 22rpx; }

.br-left-top-22 { border-top-left-radius: 22rpx; }

.br-right-top-22 { border-top-right-radius: 22rpx; }

.br-23 { border-radius: 23rpx; }

.br-left-top-23 { border-top-left-radius: 23rpx; }

.br-right-top-23 { border-top-right-radius: 23rpx; }

.br-24 { border-radius: 24rpx; }

.br-left-top-24 { border-top-left-radius: 24rpx; }

.br-right-top-24 { border-top-right-radius: 24rpx; }

.br-25 { border-radius: 25rpx; }

.br-left-top-25 { border-top-left-radius: 25rpx; }

.br-right-top-25 { border-top-right-radius: 25rpx; }

.br-26 { border-radius: 26rpx; }

.br-left-top-26 { border-top-left-radius: 26rpx; }

.br-right-top-26 { border-top-right-radius: 26rpx; }

.br-27 { border-radius: 27rpx; }

.br-left-top-27 { border-top-left-radius: 27rpx; }

.br-right-top-27 { border-top-right-radius: 27rpx; }

.br-28 { border-radius: 28rpx; }

.br-left-top-28 { border-top-left-radius: 28rpx; }

.br-right-top-28 { border-top-right-radius: 28rpx; }

.br-29 { border-radius: 29rpx; }

.br-left-top-29 { border-top-left-radius: 29rpx; }

.br-right-top-29 { border-top-right-radius: 29rpx; }

.br-30 { border-radius: 30rpx; }

.br-left-top-30 { border-top-left-radius: 30rpx; }

.br-right-top-30 { border-top-right-radius: 30rpx; }

.br-31 { border-radius: 31rpx; }

.br-left-top-31 { border-top-left-radius: 31rpx; }

.br-right-top-31 { border-top-right-radius: 31rpx; }

.br-32 { border-radius: 32rpx; }

.br-left-top-32 { border-top-left-radius: 32rpx; }

.br-right-top-32 { border-top-right-radius: 32rpx; }

.br-33 { border-radius: 33rpx; }

.br-left-top-33 { border-top-left-radius: 33rpx; }

.br-right-top-33 { border-top-right-radius: 33rpx; }

.br-34 { border-radius: 34rpx; }

.br-left-top-34 { border-top-left-radius: 34rpx; }

.br-right-top-34 { border-top-right-radius: 34rpx; }

.br-35 { border-radius: 35rpx; }

.br-left-top-35 { border-top-left-radius: 35rpx; }

.br-right-top-35 { border-top-right-radius: 35rpx; }

.br-36 { border-radius: 36rpx; }

.br-left-top-36 { border-top-left-radius: 36rpx; }

.br-right-top-36 { border-top-right-radius: 36rpx; }

.br-37 { border-radius: 37rpx; }

.br-left-top-37 { border-top-left-radius: 37rpx; }

.br-right-top-37 { border-top-right-radius: 37rpx; }

.br-38 { border-radius: 38rpx; }

.br-left-top-38 { border-top-left-radius: 38rpx; }

.br-right-top-38 { border-top-right-radius: 38rpx; }

.br-39 { border-radius: 39rpx; }

.br-left-top-39 { border-top-left-radius: 39rpx; }

.br-right-top-39 { border-top-right-radius: 39rpx; }

.br-40 { border-radius: 40rpx; }

.br-left-top-40 { border-top-left-radius: 40rpx; }

.br-right-top-40 { border-top-right-radius: 40rpx; }

.top-0 { top: 0rpx; }

.left-0 { left: 0rpx; }

.right-0 { right: 0rpx; }

.bottom-0 { bottom: 0rpx; }

.top-1 { top: 1rpx; }

.left-1 { left: 1rpx; }

.right-1 { right: 1rpx; }

.bottom-1 { bottom: 1rpx; }

.top-2 { top: 2rpx; }

.left-2 { left: 2rpx; }

.right-2 { right: 2rpx; }

.bottom-2 { bottom: 2rpx; }

.top-3 { top: 3rpx; }

.left-3 { left: 3rpx; }

.right-3 { right: 3rpx; }

.bottom-3 { bottom: 3rpx; }

.top-4 { top: 4rpx; }

.left-4 { left: 4rpx; }

.right-4 { right: 4rpx; }

.bottom-4 { bottom: 4rpx; }

.top-5 { top: 5rpx; }

.left-5 { left: 5rpx; }

.right-5 { right: 5rpx; }

.bottom-5 { bottom: 5rpx; }

.top-6 { top: 6rpx; }

.left-6 { left: 6rpx; }

.right-6 { right: 6rpx; }

.bottom-6 { bottom: 6rpx; }

.top-7 { top: 7rpx; }

.left-7 { left: 7rpx; }

.right-7 { right: 7rpx; }

.bottom-7 { bottom: 7rpx; }

.top-8 { top: 8rpx; }

.left-8 { left: 8rpx; }

.right-8 { right: 8rpx; }

.bottom-8 { bottom: 8rpx; }

.top-9 { top: 9rpx; }

.left-9 { left: 9rpx; }

.right-9 { right: 9rpx; }

.bottom-9 { bottom: 9rpx; }

.top-10 { top: 10rpx; }

.left-10 { left: 10rpx; }

.right-10 { right: 10rpx; }

.bottom-10 { bottom: 10rpx; }

.top-11 { top: 11rpx; }

.left-11 { left: 11rpx; }

.right-11 { right: 11rpx; }

.bottom-11 { bottom: 11rpx; }

.top-12 { top: 12rpx; }

.left-12 { left: 12rpx; }

.right-12 { right: 12rpx; }

.bottom-12 { bottom: 12rpx; }

.top-13 { top: 13rpx; }

.left-13 { left: 13rpx; }

.right-13 { right: 13rpx; }

.bottom-13 { bottom: 13rpx; }

.top-14 { top: 14rpx; }

.left-14 { left: 14rpx; }

.right-14 { right: 14rpx; }

.bottom-14 { bottom: 14rpx; }

.top-15 { top: 15rpx; }

.left-15 { left: 15rpx; }

.right-15 { right: 15rpx; }

.bottom-15 { bottom: 15rpx; }

.top-16 { top: 16rpx; }

.left-16 { left: 16rpx; }

.right-16 { right: 16rpx; }

.bottom-16 { bottom: 16rpx; }

.top-17 { top: 17rpx; }

.left-17 { left: 17rpx; }

.right-17 { right: 17rpx; }

.bottom-17 { bottom: 17rpx; }

.top-18 { top: 18rpx; }

.left-18 { left: 18rpx; }

.right-18 { right: 18rpx; }

.bottom-18 { bottom: 18rpx; }

.top-19 { top: 19rpx; }

.left-19 { left: 19rpx; }

.right-19 { right: 19rpx; }

.bottom-19 { bottom: 19rpx; }

.top-20 { top: 20rpx; }

.left-20 { left: 20rpx; }

.right-20 { right: 20rpx; }

.bottom-20 { bottom: 20rpx; }

.mg, .mg0 { margin: 0 auto; }

/*栅格*/
/* #ifndef MP */
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 { float: left; }

.col-12 { width: 100%; }

.col-11 { width: 91.66666667%; }

.col-10 { width: 83.33333333%; }

.col-9 { width: 75%; }

.col-8 { width: 66.66666667%; }

.col-7 { width: 58.33333333%; }

.col-6 { width: 50%; }

.col-5 { width: 41.66666667%; }

.col-4 { width: 33.33333333%; }

.col-3 { width: 25%; }

.col-2 { width: 16.66666667%; }

.col-1 { width: 8.33333333%; }

.col-pull-12 { right: 100%; }

.col-pull-11 { right: 91.66666667%; }

.col-pull-10 { right: 83.33333333%; }

.col-pull-9 { right: 75%; }

.col-pull-8 { right: 66.66666667%; }

.col-pull-7 { right: 58.33333333%; }

.col-pull-6 { right: 50%; }

.col-pull-5 { right: 41.66666667%; }

.col-pull-4 { right: 33.33333333%; }

.col-pull-3 { right: 25%; }

.col-pull-2 { right: 16.66666667%; }

.col-pull-1 { right: 8.33333333%; }

.col-pull-0 { right: auto; }

.col-push-12 { left: 100%; }

.col-push-11 { left: 91.66666667%; }

.col-push-10 { left: 83.33333333%; }

.col-push-9 { left: 75%; }

.col-push-8 { left: 66.66666667%; }

.col-push-7 { left: 58.33333333%; }

.col-push-6 { left: 50%; }

.col-push-5 { left: 41.66666667%; }

.col-push-4 { left: 33.33333333%; }

.col-push-3 { left: 25%; }

.col-push-2 { left: 16.66666667%; }

.col-push-1 { left: 8.33333333%; }

.col-push-0 { left: auto; }

.col-offset-12 { margin-left: 100%; }

.col-offset-11 { margin-left: 91.66666667%; }

.col-offset-10 { margin-left: 83.33333333%; }

.col-offset-9 { margin-left: 75%; }

.col-offset-8 { margin-left: 66.66666667%; }

.col-offset-7 { margin-left: 58.33333333%; }

.col-offset-6 { margin-left: 50%; }

.col-offset-5 { margin-left: 41.66666667%; }

.col-offset-4 { margin-left: 33.33333333%; }

.col-offset-3 { margin-left: 25%; }

.col-offset-2 { margin-left: 16.66666667%; }

.col-offset-1 { margin-left: 8.33333333%; }

.col-offset-0 { margin-left: 0; }

/* #endif */
.fr { float: right !important; }

.fl { float: left; }

.clearfix, .clear { clear: both; }

.top0 { top: 0; }

.bottom0 { bottom: 0; }

.left0 { left: 0; }

.right0 { right: 0; }

.fixed { position: fixed; }

.stack-box, .relative { position: relative; }

.stack, .absolute { position: absolute; }

.static { position: static; }

.p-footer { position: fixed; bottom: 0; width: 100%; z-index: 1; }

.p-header { position: fixed; top: 0; width: 100%; z-index: 1; }

.item-center { vertical-align: middle; }

.item-top { vertical-align: top; }

.item-bottom { vertical-align: bottom; }

.item-baseline { vertical-align: baseline; }

.hd-required .el-form-item__label::before { content: "*"; color: red; padding-right: 3px; display: inline-block; }

.print-box { display: block; padding: 9.5px; margin: 24px 0 0 0; font-size: 13px; line-height: 1.42857; color: #333; word-break: break-all; word-wrap: break-word; background-color: #F5F5F5; border: 1px solid #CCC; border-radius: 4px; }

.lbtn, .btn { font-size: 12px; padding: 0 8px; line-height: 20px; position: relative; transition: all 0.3s ease-in-out; }

.lbtn:hover, .btn:hover { cursor: pointer; opacity: .9; transition: all 0.3s ease-in-out; }

.pointer:hover { cursor: pointer; opacity: .9; transition: all 0.3s ease-in-out; }

.pointer * { cursor: pointer !important; }

.input-line input { border: 0; border-bottom: 1px solid #bbb; outline: none; transition: all 0.3s ease-in-out; border-radius: 0; padding: 0 !important; }

.input-line input:focus { border-bottom: 1px solid #1890ff; transition: all 0.3s ease-in-out; }

.input-line-none input { border: 0 !important; border-radius: 0; padding: 0 !important; }

.o-h:hover { opacity: .9; cursor: pointer; transition: all 0.3s ease-in-out; }

.link { cursor: pointer; }


.box-shadow { box-shadow: inset 0px 0px 16px 0px #ccc; }

.details-box { position: relative; overflow: hidden; border-radius: 12px !important; min-height: 200px; background: #4f517f; background: -moz-linear-gradient(45deg, #419fff 0, #5c86ff 44%, #697afe 100%); background: -webkit-linear-gradient(45deg, #419fff 0, #5c86ff 44%, #697afe 100%); background: linear-gradient(45deg, #419fff 0, #5c86ff 44%, #697afe 100%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#419fff', endColorstr='#697afe', GradientType=1); }

.details-box:before { content: ""; position: absolute; right: -40px; top: 80px; width: 130px; height: 130px; border-radius: 100px; background-color: rgba(245, 245, 245, 0.3); }

.details-box:after { content: ""; position: absolute; right: -20px; bottom: -40px; width: 160px; height: 160px; border-radius: 100px; background-color: rgba(245, 245, 245, 0.3); }

.bg-yuan { position: relative; cursor: pointer; }

.bg-yuan:before, .bg-yuan:after { content: ""; position: absolute; border-radius: 100px; background-color: rgba(245, 245, 245, 0.3); }

.bg-yuan:before { right: -40px; top: 80px; width: 130px; height: 130px; }

.bg-yuan:after { right: -20px; bottom: -40px; width: 160px; height: 160px; }

.bg-flash { position: relative; cursor: pointer; }

.bg-flash:after { position: absolute; left: -120%; top: 0; content: ""; width: 100%; height: 100%; transform: skew(-30deg); background-image: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent); }

.bg-flash:hover::after { left: 120%; transition: all 1s; -webkit-transition: all 1s; -moz-transition: all 1s; -ms-transition: all 1s; -o-transition: all 1s; }

.bg-water, .el-button { overflow: hidden; position: relative; transition: all 0.3s linear, border 0.3s linear; cursor: pointer; }

.bg-water:after, .el-button:after { content: ""; display: block; position: absolute; width: 100%; height: 100%; top: 0; left: 0; pointer-events: none; background-image: radial-gradient(circle, #000 10%, transparent 10.01%); background-repeat: no-repeat; background-position: 50%; transform: scale(10); opacity: 0; transition: transform 0.3s, opacity .6s; }

.bg-water:active:after, .el-button:active:after { transform: scale(0); opacity: 0.2; transition: 0s; }

.bg-water:hover, .el-button:hover { transition: all .3s; opacity: 0.9; }

.point-1 { width: 1rpx; height: 1rpx; border-radius: 1rpx; display: inline-block; }

.bg-line-10 { position: relative; cursor: pointer; }

.bg-line-10:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 10rpx solid rgba(255, 255, 255, 0.2); border-left: 10rpx solid transparent; }

.point-2 { width: 2rpx; height: 2rpx; border-radius: 2rpx; display: inline-block; }

.bg-line-20 { position: relative; cursor: pointer; }

.bg-line-20:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 20rpx solid rgba(255, 255, 255, 0.2); border-left: 20rpx solid transparent; }

.point-3 { width: 3rpx; height: 3rpx; border-radius: 3rpx; display: inline-block; }

.bg-line-30 { position: relative; cursor: pointer; }

.bg-line-30:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 30rpx solid rgba(255, 255, 255, 0.2); border-left: 30rpx solid transparent; }

.point-4 { width: 4rpx; height: 4rpx; border-radius: 4rpx; display: inline-block; }

.bg-line-40 { position: relative; cursor: pointer; }

.bg-line-40:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 40rpx solid rgba(255, 255, 255, 0.2); border-left: 40rpx solid transparent; }

.point-5 { width: 5rpx; height: 5rpx; border-radius: 5rpx; display: inline-block; }

.bg-line-50 { position: relative; cursor: pointer; }

.bg-line-50:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 50rpx solid rgba(255, 255, 255, 0.2); border-left: 50rpx solid transparent; }

.point-6 { width: 6rpx; height: 6rpx; border-radius: 6rpx; display: inline-block; }

.bg-line-60 { position: relative; cursor: pointer; }

.bg-line-60:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 60rpx solid rgba(255, 255, 255, 0.2); border-left: 60rpx solid transparent; }

.point-7 { width: 7rpx; height: 7rpx; border-radius: 7rpx; display: inline-block; }

.bg-line-70 { position: relative; cursor: pointer; }

.bg-line-70:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 70rpx solid rgba(255, 255, 255, 0.2); border-left: 70rpx solid transparent; }

.point-8 { width: 8rpx; height: 8rpx; border-radius: 8rpx; display: inline-block; }

.bg-line-80 { position: relative; cursor: pointer; }

.bg-line-80:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 80rpx solid rgba(255, 255, 255, 0.2); border-left: 80rpx solid transparent; }

.point-9 { width: 9rpx; height: 9rpx; border-radius: 9rpx; display: inline-block; }

.bg-line-90 { position: relative; cursor: pointer; }

.bg-line-90:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 90rpx solid rgba(255, 255, 255, 0.2); border-left: 90rpx solid transparent; }

.point-10 { width: 10rpx; height: 10rpx; border-radius: 10rpx; display: inline-block; }

.bg-line-100 { position: relative; cursor: pointer; }

.bg-line-100:after { content: ""; position: absolute; right: 0; top: 0; width: 0; height: 0; border-top: 100rpx solid rgba(255, 255, 255, 0.2); border-left: 100rpx solid transparent; }

.bg-avatar { border-radius: 50%; transition: all 0.3s linear, border 0.3s linear; cursor: pointer; }

.bg-avatar:hover { transform: scale(1.35); }

/* 边框投影 */
.boxshow1 { box-shadow: 3px 3px 3px #666; }

.boxshow2 { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); }

.boxshow2:hover { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2); transition: opacity 0.3s ease-in-out; }

.boxshow3 { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); }

.position-footer { width: 100%; position: absolute; bottom: 0; }

.position-header { width: 100%; position: absolute; top: 0; }

/* 圆形 */
.yuan { border-radius: 50%; }

.img-center { vertical-align: middle; }

.img-top { vertical-align: top; }

.img-bottom { vertical-align: bottom; }

.img-baseline { vertical-align: baseline; }

/* 图片圆形灯操作 */
.img-rounded { border-radius: 6px; }

.img-thumbnail { padding: 4px; background-color: #fff; border: 1px solid #ddd; border-radius: 4px; }

.img-circle { border-radius: 50%; }

.media-object.img-thumbnail { max-width: none; }

/* #ifndef MP */
/* blockquote */
blockquote { border: 1px solid #eee; page-break-inside: avoid; }

blockquote { padding: 10px 20px; margin: 0 0 20px; font-size: 17.5px; border-left: 5px solid #eeeeee; }

blockquote p:last-child, blockquote ul:last-child, blockquote ol:last-child { margin-bottom: 0; }

blockquote footer, blockquote small, blockquote .small { display: block; font-size: 80%; line-height: 1.42857143; color: #777777; }

.blockquote-reverse, blockquote.pull-right { padding-right: 15px; padding-left: 0; text-align: right; border-right: 5px solid #eeeeee; border-left: 0; }

.blockquote-reverse footer:before, blockquote.pull-right footer:before, .blockquote-reverse small:before, blockquote.pull-right small:before, .blockquote-reverse .small:before, blockquote.pull-right .small:before { content: ""; }

/* #endif */
.boxshow-1 { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); }

.boxshow-1:hover { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2); transition: opacity 0.3s ease-in-out; -webkit-transition: opacity 0.3s ease-in-out; -moz-transition: opacity 0.3s ease-in-out; -ms-transition: opacity 0.3s ease-in-out; -o-transition: opacity 0.3s ease-in-out; }


.br-50{
	border-radius: 50rpx;
}
.br-40{
	border-radius: 20rpx;
}
.pt-80{
	padding-top: 80rpx;
}
.br-100{
	border-radius: 100rpx;
}
