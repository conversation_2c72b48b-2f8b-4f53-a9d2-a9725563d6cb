// import _util from "framework/script/ecs.js";
/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-02-16 10:48:19
 * @LastEditTime: 2025-03-20 16:43:55
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
import "./welcome"
import md5 from 'js-md5';
import _date from './date'

const _util = {
    // uuid
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => (Math.random() * 16) | 0).toString(16)
    },
    // 判断是否是开发模式
    isDebug() {
        return process.env.NODE_ENV === 'development';
    },
    // 不包含http 直接获取域名中间
    getUrl() {
        let url = window.location.href;
        let ipRegex = /\/\/([^\/]+)/;
        let matches = url.match(ipRegex);
        if (matches && matches.length >= 2) {
            let ip = matches[1];
            return ip;
        }
        return null;
    },
    // 带前缀取出 包含http
    getBaseUrl(path) {
        let url = path || window.location.href || "";
        const regex = /^(https?:\/\/[^\/]+)/;
        const matches = url.match(regex);
        if (matches && matches.length >= 2) {
            return matches[1];
        }
        return null;
    },
    // 清空对象
    clearObject(obj) {
        Object.keys(obj).forEach(key => {
            delete obj[key]
        });
    },
    //
    clearArr(arr) {
        arr.length = 0
        return arr;
    },
    //
    updateArr(arr, obj) {
        arr.length = 0;
        return Object.assign(arr, obj)
    },
    // 获取时间戳
    getTime() {
        return parseInt(`${new Date().getTime() / 1000}`)  // 获取当前10位时间戳
    },
    // 简单的加密
    getCodeFromEncryption(timer) {
        return md5(timer + 'rhecs')
    },
    // md5加密
    md5(str) {
        return md5(str)
    },
    // 转为字符串
    string(msg, def) {
        def = def || ""
        let _msg = String(msg);
        return _msg === 'undefined' || _msg === "null" || _msg === '' ? def : _msg;
    },
    // 强转整数
    int(obj) {
        return parseInt(_util.string(obj, '0'));
    },
    // 强转float
    float(obj, length, type) {
        var _len = length || -1;
        let _num = parseFloat(_util.string(obj, '0'))
        if (_len == -1) {
            return _num;
        } else {
            var _type = type || 'fixed';
            switch (_type) {
                case 'ceil':
                    _num = Math.ceil(obj * Math.pow(10, length)) / Math.pow(10, length);
                    break;
                case 'floor':
                    _num = Math.floor(obj * Math.pow(10, length)) / Math.pow(10, length);
                    break;
                default:
                    _num = parseFloat(obj.toFixed(length));
            }
        }
    },
    // 检查id
    checkId(a) {
        return parseInt(_util.string(a)) > 0;
    },
    // 检查是否为数组
    isArray(arr) {
        return arr != null && Array.isArray(arr) && arr.length > 0
    },
    // 检查是否为Map
    isMap(obj) {
        return obj && typeof (obj) == 'object' && Object.prototype.toString.call(obj).toLowerCase() === "[object object]";
    },
    //简单性
    isNumber(obj) {
        return obj === +obj
    },
    // 是否为空
    isNull(arg1) {
        return !!(!arg1 && arg1 !== 0 && typeof arg1 !== 'boolean')
    },
    // 是否为字符串
    isString(obj) {
        return obj === obj + ''
    },
    // 是否为布尔值
    isBoolean(obj) {
        return obj === !!obj
    },
    // 是否为对象
    isObject(obj) {
        return typeof obj === 'object'
    },
    // 是否为时间格式
    isDate(obj) {
        return Object.prototype.toString.call(obj) === "[object Date]"
    },
    /**
     * 将时间戳转为时间等
     * @param {(Object|string|number)} time
     * @param {string} cFormat
     * @returns {string}
     */
    parseTime(time, cFormat) {
        return _date.parseTime(...arguments)
    },
    /**
     * @param {number} time
     * @param {string} option
     * @returns {string}
     */
    formatTime(time, option) {
        return _date.formatTime(arguments)
    },
    // 将任何能转的数据转为时间戳
    toTimeStamp: ({ data = "", length = 10 } = {}) => {
        let _d = _util.toDateFormat(data)
        let _timestamp = 0
        if (_d == null) {
            _timestamp = new Date().getTime()
        } else {
            _timestamp = _d.getTime()
        }

        if (length === 10) {
            return Math.floor(_timestamp / 1000);
        } else {
            return _timestamp;
        }
    },
    // 对日期格式的数据进行计算 返回日期格式
    //  @param {日期时间格式} data   传入的
    //  @param {变更参数} options  {day: 1, month: 1, year: 1, hours: 1, minutes: 1, seconds: 1}
    countDate: (data, options, format) => {
        let _date = _util.toDateFormat(data);
        if (_date == null) return null;
        for (const key in options) {
            if (Object.hasOwnProperty.call(options, key)) {
                const value = options[key];
                const lowercaseKey = key.toLowerCase();

                switch (lowercaseKey) {
                    case 'day':
                        _date.setDate(_date.getDate() + value);
                        break;
                    case 'month':
                        _date.setMonth(_date.getMonth() + value);
                        break;
                    case 'year':
                        _date.setFullYear(_date.getFullYear() + value);
                        break;
                    case 'hours':
                        _date.setHours(_date.getHours() + value);
                        break;
                    case 'minutes':
                        _date.setMinutes(_date.getMinutes() + value);
                        break;
                    case 'seconds':
                        _date.setSeconds(_date.getSeconds() + value);
                        break;
                    default:
                        break;
                }
            }
        }

        return _util.toDate({ data: _date, format: format });
    },
    // 转成日期格式
    toDateFormat(data) {
        if (!data) return null;

        let date;
        if (typeof data === "string") {
            data = data.trim();
            const isInteger = /^[0-9]+$/.test(data);
            if (isInteger && (data.length === 10 || data.length === 13)) {
                const timestamp = Number(data);
                const timestampLength = String(timestamp).length;
                if (!isNaN(timestamp)) {
                    return new Date(timestampLength === 10 ? timestamp * 1000 : timestamp);
                } else {
                    return null;
                }
            } else {
                if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}(Z|\+\d{2}:\d{2})$/.test(data)) {
                    const dt = new Date(data);
                    // dt.setDate(dt.getDate() + 1);
                    return dt;
                } else {
                    const match = data.match(/\d+/g);
                    if (match) {
                        let year, month, day, hours, minutes, seconds;
                        const matchLength = match.length;
                        if (matchLength >= 1) {
                            year = match[0];
                        }
                        if (matchLength >= 2) {
                            month = match[1].padStart(2, "0");
                        }
                        if (matchLength >= 3) {
                            day = match[2].padStart(2, "0");
                        }
                        if (matchLength >= 4) {
                            hours = match[3].padStart(2, "0");
                        }
                        if (matchLength >= 5) {
                            minutes = match[4].padStart(2, "0");
                        }
                        if (matchLength >= 6) {
                            seconds = match[5].padStart(2, "0");
                        }

                        return new Date(year, month - 1 || 0, day || 1, hours || 0, minutes || 0, seconds || 0);
                    } else {
                        return null;
                    }
                }
            }
        } else if (typeof data === "number") {
            const timestamp = Number(data);
            const timestampLength = String(timestamp).length;
            if (!isNaN(timestamp) && (timestampLength === 10 || timestampLength === 13)) {
                return new Date(timestampLength === 10 ? timestamp * 1000 : timestamp);
            } else {
                return null;
            }
        } else if (data instanceof Date) {
            return data;
        } else {
            return null;
        }

        if (isNaN(date.getTime())) {
            return null;
        }
    },
    // trim
    trim(value, trim) {
        switch (trim) {
            case 1:
                return value.replace(/\s+/g, "");
            case 2:
                return value.replace(/(^\s*)|(\s*$)/g, "");
            case 3:
                return value.replace(/(^\s*)/g, "");
            case 4:
                return value.replace(/(\s*$)/g, "");
            default:
                return value;
        }
    },
    // 获取年龄
    getAge(birthdate) {
        // 检查出生日期格式是否正确
        let _date = _util.toDateFormat(birthdate)
        if (_util.isEmpty(_date)) {
            return birthdate;
        }
        // 计算年龄
        const today = new Date();
        const age = today.getFullYear() - _date.getFullYear();
        // 检查年龄是否超过80
        if (age > 120) {
            return '';
        }
        return age;
    },
    // 格式化
    formatTime(data, format) {
        return _util.toDate({ data, format });
    },
    // 将任何能转的数据结构化转为日期
    toDate: ({ data = "", format = "yyyy-MM-dd hh:mm:ss" } = {}) => {

        let _d = _util.toDateFormat(data)
        if (_d == null) return data;

        const year = _d.getFullYear();
        const month = String(_d.getMonth() + 1).padStart(2, "0");
        const day = String(_d.getDate()).padStart(2, "0");
        const hours = String(_d.getHours()).padStart(2, "0");
        const minutes = String(_d.getMinutes()).padStart(2, "0");
        const seconds = String(_d.getSeconds()).padStart(2, "0");
        const weekNumber = _d.getDay();
        const weekDay = ['日', '一', '二', '三', '四', '五', '六'];

        const formattedDate = format
            .replace("yyyy", year)
            .replace("MM", month)
            .replace("dd", day)
            .replace("hh", hours)
            .replace("mm", minutes)
            .replace("ss", seconds)
            .replace("W", weekDay[weekNumber])
            .replace("w", weekNumber);

        return formattedDate;
    },
    /**
     * @param {string} url
     * @returns {Object}
     */
    param2Obj(url) {
        const search = url.split('?')[1]
        if (!search) {
            return {}
        }
        return JSON.parse(
            '{"' +
            decodeURIComponent(search)
                .replace(/"/g, '\\"')
                .replace(/&/g, '","')
                .replace(/=/g, '":"')
                .replace(/\+/g, ' ') +
            '"}'
        )
    },
    // 获取类型
    getType(obj) {
        const toString = Object.prototype.toString
        const map = {
            '[object Boolean]': 'boolean',
            '[object Number]': 'number',
            '[object String]': 'string',
            '[object Function]': 'function',
            '[object Array]': 'array',
            '[object Date]': 'date',
            '[object RegExp]': 'regExp',
            '[object Undefined]': 'undefined',
            '[object Null]': 'null',
            '[object Object]': 'object'
        }
        return map[toString.call(obj)]
    },
    // 是否为空
    isEmpty(val) {
        // null or undefined
        if (val == null) return true;
        if (typeof val === 'boolean') return false;
        if (typeof val === 'number') return false;
        if (_util.string(val) === '') return true; //
        if (val instanceof Error) return val.message === '';
        switch (Object.prototype.toString.call(val)) {
            // String or Array
            case '[object String]':
            case '[object Array]':
                return !val.length;

            // Map or Set or File
            case '[object File]':
            case '[object Map]':
            case '[object Set]': {
                return !val.size;
            }
            // Plain Object
            case '[object Object]': {
                return !Object.keys(val).length;
            }
        }
        return false;
    },
    // lodash取值
    get(obj, path, defaultValue = undefined) {
        // console.log("🚀 -> get -> obj:", obj,path)
        const travel = regexp =>
            String.prototype.split
                .call(path, regexp)
                .filter(Boolean)
                .reduce((res, key) => (res !== null && res !== undefined ? res[key] : res), obj);
        const result = travel(/[,[\]]+?/) || travel(/[,[\].]+?/);
        return result === undefined || result === obj ? defaultValue : result;
    },
    /**
     * 对象数组去重
     * @param {array} arr，需要去重的数组
     * @param {string} key，通过指定key值进行去重
     * @returns {array} 返回一个去重后的新数组
     */
    arrayClear(arr, key) {
        const res = new Map();
        return arr.filter((item) => !res.has(item[key]) && res.set(item[key], true));
    },

    // 简单的加密
    getCodeFromEncryption(timer) {
        return md5(timer + 'rhecs')
    },
    /**
     * 保留n位小数，注意此方法返回的是 string类型，小数点后面不足的位数会自动补 0
     * @param {number} num 初始数字
     * @param {number} n 保留几位小数
     * @returns string
     */
    fixedLen(num, n) {
        const _num = Number(num)
        const _n = Number(n)
        const newNum = Math.round(_num * Math.pow(10, _n)) / Math.pow(10, _n)
        let str = newNum.toString()
        if (str.indexOf('.') < 0) {
            str += '.'
        }
        const len = str.length
        const idx = str.indexOf('.')
        for (let i = len - idx; i <= _n; i++) {
            str += '0'
        }
        return str
    },
    /**
     * 将数字按千分位显示，小数点后的不做千分位显示，保留原数字小数位
     * @param {number | string} num 初始数字
     * @returns string
     */
    thousandBitSeparator(num) {
        const str = num.toString()
        const arr = str.split('.')
        const str1 = arr[0].replace(/\d{1,3}(?=(\d{3})+$)/g, function (a, b, c) {
            return a + ','
        })
        return arr[1] ? str1 + '.' + arr[1] : str1;
    },
    // 是否为微信
    isWechat() {
        let ua = window.navigator.userAgent.toLowerCase()
        if (ua.match(/MicroMessenger/i) === 'micromessenger') {
            return true
        }
        return false
    },
    // 检查设备()
    device() {
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
            return 'ios';
        } else if (/(Android)/i.test(navigator.userAgent)) {
            return 'android';
        } else {
            return 'web'
        };
    },
    // 过滤html脚本
    clearHTML(str) {
        str = str.replace(/&/ig, '&')
        str = str.replace(/</ig, '<')
        str = str.replace(/>/ig, '>')
        str = str.replace(' ', ' ')
        return str
    },
    // 只保留文本
    removeHTML(str) {
        var _str = _util.string(str);
        if (_str == '') return "";
        return _str.replace(/<[^>]+>/g, "").replaceAll('&nbsp;', ' ');
    },
    // 随机数
    random(min, max) {
        if (arguments.length === 2) {
            return Math.floor(min + Math.random() * ((max + 1) - min))
        } else {
            return null
        }
    },
    // 解析url param
    urlParam(name) { // 获取url参数
        let reg = new RegExp('(^|&?)' + name + '=([^&]*)(&|$)', 'i')
        let r = window.location.href.substr(1).match(reg)
        if (r != null) {
            return decodeURI(r[2])
        }
        return undefined
    },
    // 监听软键盘缩回 弹起
    //当软件键盘弹起会改变当前 window.innerHeight，监听这个值变化 [downCb 当软键盘弹起后，缩回的回调,upCb 当软键盘弹起的回调]
    h5Resize(downCb, upCb) {
        var clientHeight = window.innerHeight;
        downCb = typeof downCb === 'function' ? downCb : function () { }
        upCb = typeof upCb === 'function' ? upCb : function () { }
        window.addEventListener('resize', () => {
            var height = window.innerHeight;
            if (height === clientHeight) {
                downCb();
            }
            if (height < clientHeight) {
                upCb();
            }
        });
    },
    // 数据合并
    mergeRecursive(source, target) {
        for (var p in target) {
            try {
                if (target[p].constructor == Object) {
                    source[p] = mergeRecursive(source[p], target[p]);
                } else {
                    source[p] = target[p];
                }
            } catch (e) {
                source[p] = target[p];
            }
        }
        return source;
    },
    //获取cookie
    getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        // console.log("获取cookie,现在循环")
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            // console.log(c)
            while (c.charAt(0) == ' ') c = c.substring(1);
            if (c.indexOf(name) != -1) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    },
    /**
    * 构造树型结构数据
    * @param {*} data 数据源
    * @param {*} id id字段 默认 'id'
    * @param {*} parentId 父节点字段 默认 'topId'
    * @param {*} children 孩子节点字段 默认 'children'
    */
    handleTree(data, id, parentId, children) {
        let config = {
            id: id || 'id',
            parentId: parentId || 'topId',
            childrenList: children || 'children'
        };

        var childrenListMap = {};
        var nodeIds = {};
        var tree = [];

        for (let d of data) {
            let parentId = d[config.parentId];
            if (childrenListMap[parentId] == null) {
                childrenListMap[parentId] = [];
            }
            nodeIds[d[config.id]] = d;
            childrenListMap[parentId].push(d);
        }

        for (let d of data) {
            let parentId = d[config.parentId];
            if (nodeIds[parentId] == null) {
                tree.push(d);
            }
        }

        for (let t of tree) {
            adaptToChildrenList(t);
        }

        function adaptToChildrenList(o) {
            if (childrenListMap[o[config.id]] !== null) {
                o[config.childrenList] = childrenListMap[o[config.id]];
            }
            if (o[config.childrenList]) {
                for (let c of o[config.childrenList]) {
                    adaptToChildrenList(c);
                }
            }
        }
        return tree;
    },
    // 判断是否存在指定的key
    hasKey(obj, key) {
        return _util.isObject(obj) && _util.string(key) !== '' && Object.prototype.hasOwnProperty.call(obj, key);
    },
    // 强转列表
    toList(obj, length) {
        let _length = length || 0;  // 列表的显示长度
        if (typeof obj === undefined || obj.length < 1) {
            return [];
        } else {
            let _arr = Array.prototype.slice.call(obj);
            if (_length > 0 && _arr.length > _length) {
                _arr.length = _length;
            }
            return _arr;
        }
    },
    //
    // -------------- arguments设计对象 --------------------------------
    // args1() {
    //     _util.args(3000,false,{abc:1},'delete',"http://www.baidu.com",{a:1,b:2});
    // },
    // args() {
    //     let _map = _util.$ecs.args(arguments, {
    //         age: 1,
    //         loading:true,
    //         url:"",
    //         methods: {
    //             type: "string",
    //             items: ["post", "get", "delete"],
    //         },
    //         data:{
    //             type:"object",
    //         },
    //         params:{
    //             type:"object",
    //             items:"2",
    //         }
    //     });
    //     console.log("🚀 -> file: info.vue:197 -> args -> _map:", _map);
    // },
    // 将数组args转为对象
    argsMap(args, isArray = false) {
        const _map = {}
        if (args == null) return _map;
        const _arg = Array.prototype.slice.call(args);
        _arg.map(_item => {
            let type = `${_util.getType(_item)}`;
            if (isArray === true) {
                if (!_map[type]) _map[type] = [];
                _map[type].push(_item);
            } else if (Array.isArray(isArray) && isArray.includes(type)) {
                if (!_map[type]) _map[type] = [];
                _map[type].push(_item);
            } else {
                _map[type] = _item;
            }
        })
        return _map
    },
    // map的格式 {name:"1",age:2,methods:{type:"string",items:['post','get'],value:'post'}}
    args(args, map) {
        if (map == null) return undefined;  // lock
        let _map = {}, _args = _util.argsMap(args, true);
        let _argsObj = _args['object'] != null ? _args['object'][0] : {};
        const objSortForMap = (oldObject) => {
            let primitiveMap = new Map(), objectMap = new Map();
            for (let key of Object.keys(oldObject)) {
                if (_util.getType(oldObject[key]) === 'object' && oldObject[key] !== null) {
                    objectMap.set(key, oldObject[key]);
                } else {
                    primitiveMap.set(key, oldObject[key]);
                }
            }
            let newObject = Object.fromEntries(objectMap);
            for (let [key, value] of primitiveMap) newObject[key] = value;
            return newObject;
        }
        let _mapSort = objSortForMap(map);
        const _defaultValue = (key,type) => {
            return _argsObj[key] !== undefined ? _argsObj[key] : (_args[type] !== undefined && _mapSort[key]?.type === undefined ? _args[type][0] : (_mapSort[key]?.value !== undefined ? map[key]?.value  : _mapSort[key]));
        }
        for (const key in _mapSort) {
            let _mapType = _util.getType(map[key])
            let _def = _defaultValue(key,_mapType) // default
            if (_mapType == 'object') {
                let _key = `${map[key].type}`
                let _v = _args[_key];
                if (map[key]?.type != "object") {
                    if (!_util.isEmpty(_v)) {
                        if (!_util.isEmpty(map[key].items)) {
                            if (!_util.isArray(map[key].items)) {
                                let _index = parseInt(`${map[key].items}`);
                                _map[key] = _v.length >= _index ? _v[_index] : _def;
                            } else {
                                _v.map(item => {
                                    if (map[key].items.includes(item)) _map[key] = item, _args[_key] = _args[_key].filter(i => i != item);
                                })
                            }
                            _map[key] = _map[key] || _def || map[key].items[0];
                        } else {
                            _map[key] = _def;
                        }
                    } else {
                        _map[key] = _def || map[key]?.items[0];
                    }
                } else {
                    let _index = map[key]?.items || 0;
                    _map[key] = _v != undefined &&  _index < _v.length ? _v[_index] : _def;
                }

            } else {
                let _v = _args[`${_util.getType(map[key])}`];
                _map[key] = (_v === undefined || !_util.isArray(_v)) ? _def : _v[0];
            }

        }
        return _map;
    },

}

export default _util;
