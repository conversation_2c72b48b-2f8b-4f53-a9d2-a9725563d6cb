// vue常用过滤器
import ecs from './ecs'  //
import imgError from '../static/errorImage.js';

// 换行转br
export const turnToBR = (value) => {
    if (value) {
        return value.replace(/\n/g, "<br />");
    }
    return value;
}

// 万能日期格式化
export const formatTime = (value, format) => {
    if (value) {
        return ecs.toDate({ data: value, format: format })
    }
    return "";
}

// 格式化文本日期  若依的日期格式转化
export const formatTime2 = (value) => {
    if (value) {
        return value.split('.')[0].replace('T', ' ')
    }
    return value;
}

//去除空格 type 1-所有空格 2-前后空格 3-前空格 4-后空格
export const trim = (value, trim) => {
    switch (trim) {
        case 1:
            return value.replace(/\s+/g, "");
        case 2:
            return value.replace(/(^\s*)|(\s*$)/g, "");
        case 3:
            return value.replace(/(^\s*)/g, "");
        case 4:
            return value.replace(/(\s*$)/g, "");
        default:
            return value;
    }
}

// 过滤Num
export const int = (value) => {
    if (ecs.isEmpty(value)) {
        return 0;
    } else {
        return value;
    }
}

// 二维码生成
export const qrcode = (value) => {
    return 'https://api.vvhan.com/api/qr?text=' + value;
    // return 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + value;
}


// 初始化图片
export const imgcol = (value) => {
    return ecs.isEmpty(value) ? imgError : value;
}

//任意格式日期处理
//使用格式：
// {{ '2018-09-14 01:05' | formaDate('yyyy-MM-dd hh:mm:ss') }}
// {{ '2018-09-14 01:05' | formaDate('yyyy-MM-dd') }}
// {{ '2018-09-14 01:05' | formaDate('MM/dd') }} 等
export const formaDate = (value, fmt) => {
    let _dateType = ecs.getType(value)
    // console.log(_dateType)
    var date = new Date()
    if (_dateType == 'string') {
        date = new Date(value.replace(/-/g, '/'))
    } else {
        date = new Date(value * 1000);
    }
    var o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "w+": date.getDay(), //星期
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o) {
        if (k === 'w+') {
            if (o[k] === 0) {
                fmt = fmt.replace('w', '周日');
            } else if (o[k] === 1) {
                fmt = fmt.replace('w', '周一');
            } else if (o[k] === 2) {
                fmt = fmt.replace('w', '周二');
            } else if (o[k] === 3) {
                fmt = fmt.replace('w', '周三');
            } else if (o[k] === 4) {
                fmt = fmt.replace('w', '周四');
            } else if (o[k] === 5) {
                fmt = fmt.replace('w', '周五');
            } else if (o[k] === 6) {
                fmt = fmt.replace('w', '周六');
            }
        } else if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
}

//字母大小写切换
/*type
 1:首字母大写
 2：首页母小写
 3：大小写转换
 4：全部大写
 5：全部小写
 * */
export const changeCase = (str, type) => {
    function ToggleCase(str) {
        var itemText = ""
        str.split("").forEach(
            function (item) {
                if (/^([a-z]+)/.test(item)) {
                    itemText += item.toUpperCase();
                } else if (/^([A-Z]+)/.test(item)) {
                    itemText += item.toLowerCase();
                } else {
                    itemText += item;
                }
            });
        return itemText;
    }
    switch (type) {
        case 1:
            return str.replace(/\b\w+\b/g, function (word) {
                return word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase();
            });
        case 2:
            return str.replace(/\b\w+\b/g, function (word) {
                return word.substring(0, 1).toLowerCase() + word.substring(1).toUpperCase();
            });
        case 3:
            return ToggleCase(str);
        case 4:
            return str.toUpperCase();
        case 5:
            return str.toLowerCase();
        default:
            return str;
    }
}

//字符串循环复制,count->次数
export const repeatStr = (str, count) => {
    var text = '';
    for (var i = 0; i < count; i++) {
        text += str;
    }
    return text;
}

//字符串替换
export const replaceAll = (str, AFindText, ARepText) => {
    var raRegExp = new RegExp(AFindText, "g");
    return str.replace(raRegExp, ARepText);
}

//字符替换*，隐藏手机号或者身份证号等
//replaceStr(字符串,字符格式, 替换方式,替换的字符（默认*）)
//ecDo.replaceStr('18819322663',[3,5,3],0)
//result：188*****663
//ecDo.replaceStr('asdasdasdaa',[3,5,3],1)
//result：***asdas***
//ecDo.replaceStr('1asd88465asdwqe3',[5],0)
//result：*****8465asdwqe3
//ecDo.replaceStr('1asd88465asdwqe3',[5],1,'+')
//result："1asd88465as+++++"
export const replaceStr = (str, regArr, type, ARepText) => {
    var regtext = '',
        Reg = null,
        replaceText = ARepText || '*';
    //repeatStr是在上面定义过的（字符串循环复制），大家注意哦
    if (regArr.length === 3 && type === 0) {
        regtext = '(\\w{' + regArr[0] + '})\\w{' + regArr[1] + '}(\\w{' + regArr[2] + '})'
        Reg = new RegExp(regtext);
        replaceCount = this.repeatStr(replaceText, regArr[1]);
        return str.replace(Reg, '$1' + replaceCount + '$2')
    } else if (regArr.length === 3 && type === 1) {
        regtext = '\\w{' + regArr[0] + '}(\\w{' + regArr[1] + '})\\w{' + regArr[2] + '}'
        Reg = new RegExp(regtext);
        var replaceCount1 = this.repeatStr(replaceText, regArr[0]);
        var replaceCount2 = this.repeatStr(replaceText, regArr[2]);
        return str.replace(Reg, replaceCount1 + '$1' + replaceCount2)
    } else if (regArr.length === 1 && type === 0) {
        regtext = '(^\\w{' + regArr[0] + '})'
        Reg = new RegExp(regtext);
        replaceCount = this.repeatStr(replaceText, regArr[0]);
        return str.replace(Reg, replaceCount)
    } else if (regArr.length === 1 && type === 1) {
        regtext = '(\\w{' + regArr[0] + '}$)'
        Reg = new RegExp(regtext);
        var replaceCount = this.repeatStr(replaceText, regArr[0]);
        return str.replace(Reg, replaceCount)
    }
}



// 格式化处理字符串
//ecDo.formatText('1234asda567asd890')
//result："12,34a,sda,567,asd,890"
//ecDo.formatText('1234asda567asd890',4,' ')
//result："1 234a sda5 67as d890"
//ecDo.formatText('1234asda567asd890',4,'-')
//result："1-234a-sda5-67as-d890"
export const formatText = (str, size, delimiter) => {
    var _size = size || 3,
        _delimiter = delimiter || ',';
    var regText = '\\B(?=(\\w{' + _size + '})+(?!\\w))';
    var reg = new RegExp(regText, 'g');
    return str.replace(reg, _delimiter);
}

// 现金额大写转换函数
//ecDo.upDigit(168752632)
//result："人民币壹亿陆仟捌佰柒拾伍万贰仟陆佰叁拾贰元整"
//ecDo.upDigit(1682)
//result："人民币壹仟陆佰捌拾贰元整"
//ecDo.upDigit(-1693)
//result："欠人民币壹仟陆佰玖拾叁元整"
export const upDigit = (n) => {
    var fraction = ['角', '分', '厘'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
    ];
    // var head = n < 0 ? '欠人民币' : '人民币';
    if (n == undefined || n == 0) {
        return "";
    }
    var head = '';
    n = Math.abs(n);
    var s = '';
    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);
    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
        //s = p + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

// 保留2位小数
export const toDecimal2 = (x) => {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}


/*
    格式化金额，
    s : 金额
    n : 保留位数
*/
export const formatMoney = (s, n) => {
    if (s == undefined) return '0.00';
    n = n > 0 && n <= 20 ? n : 2;
    s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
    var l = s.split(".")[0].split("").reverse(),
        r = s.split(".")[1];
    var t = "";
    for (var i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    }
    return t.split("").reverse().join("") + "." + r;
}

/*
    格式化距离，
    s : 距离
*/
export const formatDistance = (s) => {
    if (!isNotANumber(s)) return '未定位';
    s = parseInt(s);
    if (s < 1000) {
        return s + 'm';
    } else if (s < 100000) {
        s = s / 1000;
        s = app.formatMoney(s);
        return s + 'km';
    } else {
        return '未定位';
    }
}

// 根据生日计算年龄
export const calculateAge = (birthdate) => {
    // 检查出生日期格式是否正确
    let _date = ecs.toDateFormat(birthdate)
    if (ecs.isEmpty(_date)) {
        return birthdate;
    }

    // 计算年龄
    const today = new Date();
    const age = today.getFullYear() - _date.getFullYear();

    // 检查年龄是否超过80
    if (age > 80) {
        return '';
    }

    return age;
}

export const age = (birthdate) => calculateAge(birthdate)

// 尺寸转换单位
export const formatFileSize = (bytes) => {
    if(!bytes) return ""
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};


/* 增加阿里云oss处理函数 */
export const ossUrl = (src, width) => {
    // 如果不存在图
    if (!src || src == '') return imgError;
    width = width || 200;
    // src.replace(/\\/g,"\/");
    // 如果没有前缀
    if (src.indexOf('ttp://') != -1 || src.indexOf('ttps://') != -1) {
        return src + `?x-oss-process=image/resize,w_${width}&imageView2/2/w/${width}`;
    } else {
        return ossUrl + src + `?x-oss-process=image/resize,w_${width}&imageView2/2/w/${width}`;
    }
}

// 格式化时间戳日期
export const formatDayTemplate = (value, template) => {
    if (value == undefined || value == '') {
        return;
    }
    // 自定义末班
    let _template = template || 'yy-MM-dd hh:mm:ss';
    var date = new Date()
    if (ecs.isString(value)) {
        date = new Date(value.replace(/-/g, '/'))
    } else {
        //时间戳为10位需*1000，时间戳为13位的话不需乘1000
        if (value.length == 10) {
            date = new Date(value * 1000);
        } else {
            date = new Date(value);
        }
    }
    let y = date.getFullYear();
    let MM = date.getMonth() + 1;
    MM = MM < 10 ? ('0' + MM) : MM; //月补0
    let d = date.getDate();
    d = d < 10 ? ('0' + d) : d; //天补0
    let h = date.getHours();
    h = h < 10 ? ('0' + h) : h; //小时补0
    let m = date.getMinutes();
    m = m < 10 ? ('0' + m) : m; //分钟补0
    let s = date.getSeconds();
    s = s < 10 ? ('0' + s) : s; //秒补0
    // return y + '-' + MM + '-' + d; //年月日

    _template = _template.replace('yy', y);
    _template = _template.replace('MM', MM);
    _template = _template.replace('dd', d);
    _template = _template.replace('hh', h);
    _template = _template.replace('mm', m);
    _template = _template.replace('ss', s);

    return _template; //年月日时分秒
}



/**
 * 全局监听图片错误，显示默认图片
 */

// document.addEventListener("error", function (e) {
//     var elem = e.target;
//     if (elem.tagName.toLowerCase() == "img") {
//         elem.src = imgError;
//     }
// }, true);



/*
// 1. 添加全局方法
Vue.myGlobalMethod = function() {
    alert('MyFilter插件: 全局方法生效')
}

// 2. 添加全局指令
Vue.directive('my-directive', {
    inserted: function(el, binding) {
        el.innerHTML = "MyFilter插件 my-directive:" + binding.value
    }
})

// 3. 添加实例方法
Vue.prototype.$myMethod = function(methodOption) {
    alert('Vue 实例方法生效：' + methodOption)
}

 // 4. 注入组件选项
Vue.mixin({
    created: function () {
    // 逻辑...
    }
    ...
})
*/
