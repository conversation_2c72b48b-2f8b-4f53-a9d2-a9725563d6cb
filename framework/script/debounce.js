/*
 * @Description: 模拟lodash的防抖 节流
 * @Author: 徐静(parkhansung)
 * @Date: 2022-11-22 08:57:48
 * @LastEditTime: 2024-10-25 17:02:04
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */

const utils = {}
/**
 * 防抖
 * @param {*} fn 需要防抖的方法
 * @param {*} delay 防抖时间
 * @param {*} atOnce 是否需要立即执行
 * @returns
 */
utils.debounce = function (fn, time, atOnce) {
    let delay = time || 400;
    let timer = null;
    let count = 0;
    return function () {
        const _this = this;
        const args = arguments;
        // 如果是立即执行
        if (atOnce) {
            // 第一次直接执行不用等
            if (count === 0) {
                fn.apply(_this, args);
                count++;
            } else {
                if (timer) {
                    clearTimeout(timer);
                }
                timer = setTimeout(function () {
                    fn.apply(_this, args);
                }, delay);
            }
            //
        } else {
            if (timer) {
                clearTimeout(timer);
            }
            timer = setTimeout(function () {
                fn.apply(_this, args);
            }, delay);
        }
    };
}

/**
* 节流
* @param {*} fn 需要节流的方法
* @param {*} interval 多久执行一次节流
* @returns
*/
utils.throttle = function (fn, time) {
    let last;
    let timer;
    let interval = time || 400;
    return function () {
        const _this = this;
        const args = arguments;
        let now = +new Date();
        if (last && now - last < interval) {
            clearTimeout(timer);
            timer = setTimeout(function () {
                last = now;
                fn.apply(_this, args);
            }, interval);
        } else {
            last = now;
            timer = setTimeout(function () {
                fn.apply(_this, args);
            }, interval);
        }
    }
}


// 立即执行 400ms内不再执行
// 节流: n 秒内只运行一次，若在 n 秒内重复触发，只有一次生效
// 防抖: n 秒后在执行该事件，若在 n 秒内被重复触发，则重新计时
utils.throttle1 = (func, delay = 400) => {
    let timerId;
    let lastExecutionTime = 0;
    return function(...args) {
        const currentTime = Date.now();
        if (!timerId && (currentTime - lastExecutionTime >= delay)) {
            func.apply(this, args);
            lastExecutionTime = currentTime;
            timerId = setTimeout(() => {
                timerId = null;
            }, delay);
        }
    };
}

// 延迟400ms后执行 并且400ms内 只执行一次
utils.debounce1 = (func, delay = 400) => {
    let timerId;
    return function(...args) {
        if (!timerId) {
            timerId = setTimeout(() => {
                func.apply(this, args);
                timerId = null;
            }, delay);
        }
    };
};

export default utils
