/*
 * @Description: 常用的时间转化
 * @Author: 徐静(parkhansung)
 * @Date: 2022-12-05 08:53:04
 * @LastEditTime: 2023-05-26 14:35:50
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */

import ecs from "./ecs"


const _date = {
    // 时间格式化 【'yyyy-MM-dd hh:mm:ss',时间】
    parseTime(time, pattern) {
        if (arguments.length === 0 || !time) {
            return null
        }
        const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
        let date
        if (typeof time === 'object') {
            date = time
        } else {
            if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
                time = parseInt(time)
            } else if (typeof time === 'string') {
                time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
                if(time === '') return '';  //
            }
            if ((typeof time === 'number') && (time.toString().length === 10)) {
                time = time * 1000
            }
            date = new Date(time)
        }
        if(pattern == 'date') return date;
        const formatObj = {
            y: date.getFullYear(),
            m: date.getMonth() + 1,
            d: date.getDate(),
            h: date.getHours(),
            i: date.getMinutes(),
            s: date.getSeconds(),
            a: date.getDay()
        }
        const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
            let value = formatObj[key]
            // Note: getDay() returns 0 on Sunday
            if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
            if (result.length > 0 && value < 10) {
                value = '0' + value
            }
            return value || 0
        })
        return time_str
    },
    // 时间，时间戳转换
    stamp: {
        // 时间转10位时间戳
        getTime: ({time, ten = true} = {}) =>  {
            let date = time ? new Date(time) : new Date()
            if(ten){
                return Math.round(date.getTime() / 1000)
            }else{
                return date.getTime()
            }
        },
        // 10位时间戳转时间
        timeToStr:  (time, fmt) =>  {
            return _date.parseTime(time || new Date(),fmt || "{y}-{m}-{d}")
        }
    },
    // 转译一下
    unixTime: () => this.stamp,
    /**
     * @param {number} time
     * @param {string} option
     * @returns {string}
     */
    formatTime(time, option) {
        if(ecs.isEmpty(time)) return ''
        const d = _date.parseTime(time,"date");
        const now = Date.now()
        const diff = (now - d) / 1000
        if (diff < 30) {
            return '刚刚'
        } else if (diff < 3600) {
            // less 1 hour
            return Math.ceil(diff / 60) + '分钟前'
        } else if (diff < 3600 * 24) {
            return Math.ceil(diff / 3600) + '小时前'
        } else if (diff < 3600 * 24 * 2) {
            return '1天前'
        }
        if (option) {
            return this.parseTime(time, option)
        } else {
            return this.parseTime(time)
        }
    },
    // 统计用到的时间
    count: ({ ar, format = 'unixTime' } = {}) => {
        if (ecs.isEmpty(ar)) return;
        let datenow = new Date()
        let nowDay = datenow.getDate() //当前日
        let nowMonth = datenow.getMonth() //当前月
        let nowYear = datenow.getFullYear() //当前年
        let nowDayOfWeek = datenow.getDay() //今天本周的第几天
        // let jd = Math.ceil((nowMonth + 1) / 3)
        let _middle = [];
        switch (ar) {
            case "today":
                _middle = [new Date(nowYear, nowMonth, nowDay), Date(nowYear, nowMonth, nowDay, 23, 59, 59)]
            case "yesterday":
                _middle = [new Date(nowYear, nowMonth, nowDay - 1), new Date(nowYear, nowMonth, nowDay - 1, 23, 59, 59)]
            case "week":
                _middle = [new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1),
                            new Date(nowYear, nowMonth, nowDay + (7 - nowDayOfWeek), 23, 59, 59)]
            case "last7day":
                _middle = [datenow.getTime() - 3600 * 1000 * 24 * 7, datenow.getTime()]
            case "month":
                _middle = [new Date(nowYear, nowMonth, 1), Date(nowYear, nowMonth + 1, 0, 23, 59, 59)]
            case "lastMonth":
                _middle = [new Date(nowYear, nowMonth - 1, 1), Date(nowYear, nowMonth, 0, 23, 59, 59)]
            case "last30day":
                _middle = [datenow.getTime() - 3600 * 1000 * 24 * 30, datenow.getTime()]
            case "firstHalfYear":
                _middle = [Date(nowYear, 0, 1), Date(nowYear, 5, 30, 23, 59, 59)]
            case "secondHalfYear":
                _middle = [Date(nowYear, 6, 1), Date(nowYear, 11, 31, 23, 59, 59)]
            case "year":
                _middle = [Date(nowYear, 0, 1), Date(nowYear, 11, 31, 23, 59, 59)]
            case "lastYear":
                _middle = [Date(nowYear - 1, 0, 1), Date(nowYear - 1, 11, 31, 23, 59, 59)]

        }
        if (_middle.length == 0) return;
        if (format == 'date') {
            return _middle;
        } else if (format.indexOf('unixTime') != -1 && ['last7day', 'last30day'].indexOf(ar) == -1) {
            if(format == 'unixTime10'){
                _middle[0] = Math.round(_middle[0].getTime() / 1000)
                _middle[1] = Math.round(_middle[1].getTime() / 1000)
            }else{
                _middle[0] = _middle[0].getTime()
                _middle[1] = _middle[1].getTime()
            }
            return _middle
        }else{
            // '{y}-{m}-{d} {h}:{i}:{s}'
            _middle[0] = _date.parseTime(_middle[0],format)
            _middle[1] = _date.parseTime(_middle[1],format)
            return _middle
        }
    },

}

export default _date