/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-02-16 10:48:12
 * @LastEditTime: 2025-04-30 17:35:18
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */


import _util from './ecs'

// 计算字符串长度
const getByteLength = (str) => {
    if (!str || str === "") return 0
    // 创建一个空数组来存储字节
    let bytes = [];

    for (let i = 0; i < str.length; i++) {
        // 获取字符的 Unicode 码点
        const codePoint = str.codePointAt(i);

        // 如果是代理对（surrogate pair），跳过下一个字符
        if (codePoint > 0xffff) {
            i++;
        }

        // 根据 UTF-8 编码规则计算字节数
        if (codePoint <= 0x7f) {
            bytes.push(1);  // 1字节
        } else if (codePoint <= 0x7ff) {
            bytes.push(2);  // 2字节
        } else if (codePoint <= 0xffff) {
            bytes.push(3);  // 3字节
        } else {
            bytes.push(4);  // 4字节
        }
    }

    // 返回总字节数
    return bytes.reduce((a, b) => a + b, 0);
}

// 辅助函数：检查日期是否有效
const isValidDate = (year, month, day) => {
    // 检查年份范围（一般身份证年份从1900年开始）
    if (year < 1900 || year > new Date().getFullYear()) {
        return false;
    }

    // 检查月份和日期
    if (month < 1 || month > 12) {
        return false;
    }

    // 获取当月最大天数
    let maxDay;
    if ([4, 6, 9, 11].includes(month)) {
        maxDay = 30;
    } else if (month === 2) {
        // 闰年2月29天，平年28天
        maxDay = ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) ? 29 : 28;
    } else {
        maxDay = 31;
    }

    return day >= 1 && day <= maxDay;
}

// 封装完善的网络请求
const MyValidate = {
    //
    toStr(msg, def) {
        def = def || ""
        let _msg = String(msg);
        return _msg === 'undefined' || _msg === '' || _msg === 'null' ? def : _msg;
    },
    //  检查是否为手机号
    isMobile(value) {
        return this.toStr(value) === '' || /^1[3-9]\d{9}$/.test(value)
    },
    // 检查是否为邮箱
    isEmail(value) {
        return this.toStr(value) === '' || /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value)
    },
    // 验证URL格式
    isUrl(value) {
        return this.toStr(value) === '' || /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(value)
    },
    // 验证身份证号码
    isIDCard(value) {
        // 空值校验
        if (this.toStr(value) === '') {
            return true;
        }

        // 基本格式校验：18位，前17位为数字，最后一位可以是数字或X
        const reg = /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
        if (!reg.test(value)) {
            return false;
        }

        // 校验日期部分
        const year = parseInt(value.substr(6, 4));
        const month = parseInt(value.substr(10, 2));
        const day = parseInt(value.substr(12, 2));

        // 检查日期是否有效
        if (!isValidDate(year, month, day)) {
            return false;
        }

        // 校验码校验
        // 加权因子
        const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        // 校验码对应值
        const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        let sum = 0;
        for (let i = 0; i < 17; i++) {
            sum += parseInt(value.charAt(i)) * factor[i];
        }

        // 计算校验位
        const checkCode = parity[sum % 11];

        // 校验位比对
        return checkCode === value.charAt(17);
    },
    // 验证日期格式
    isDate(value) {
        return this.toStr(value) === '' || !/Invalid|NaN/.test(new Date(value).toString())
    },
    //验证固定电话
    isTel(value) {
        let reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/
        return this.toStr(value) === '' || reg.test(value)
    },
    // 验证是否为数组
    isArray(value) {
        if (typeof Array.isArray === 'function') {
            return Array.isArray(value)
        } else {
            return Object.prototype.toString.call(value) === '[object Array]'
        }
    },
    // 验证是否为对象
    isObject(value) {
        return Object.prototype.toString.call(value) === '[object Object]'
    },
    // 验证为验证码
    isCode(value) {
        return value.length === 4 || value.length === 6;
    },
    // 简单的验证下
    isName(value) {
        return value !== undefined && value.length > 1 && value.length < 32;
    },
    // 标题
    isTitle(value) {
        return value !== undefined && value.length > 1 && value.length < 64;
    },
    // 验证为密码
    isPassword(value) {
        return value !== undefined && value.length > 1 && value.length < 32;
    },
    // 复杂密码
    isPassword2(value) {
        // 包含至少一个字母和一个数字的正则表达式
        let reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{9,}$/;
        // 不能包含汉字的正则表达式
        let chineseReg = /[\u4e00-\u9fa5]/;
        // 过于简单的连续数字
        let simpleNumberReg = /(123456|234567|345678|456789)/;
        return reg.test(value) && !chineseReg.test(value) && !simpleNumberReg.test(value);
    },
    // 检查是否为IP地址
    isIP(value) {
        let reg = /^(([1-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.)(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){2}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/
        return this.toStr(value) === '' || reg.test(value)
    },
    isRequired(value) {
        let _v = this.toStr(value)
        return _v !== '';
    },
    isLenth(value, min, max) {
        let _v = String(value)
        const _len = getByteLength(_v)
        // console.log("🚀 -> isLenth -> _v.length:", _len, min, max)
        return _len >= min && _len <= max;
    },
    isBetween(value, min, max) {
        let _v = parseInt(String(value))
        return _v >= min && _v <= max;
    },
    // 是否在数组内
    isIn(value, arr) {
        return arr.indexOf(value) !== -1;
    },
    isNotIn(value, arr) {
        return arr.indexOf(value) === -1;
    },
    // 必须中文
    isChinese(value) {
        return this.toStr(value) === '' || /^[\u4e00-\u9fa5]+$/.test(value)
    },
    // 必须是中文姓名
    isChineseName(value) {
        // 简化标准:
        // 1. 只允许中文和中间点(·)
        // 2. 不能以点开头或结尾
        // \u4e00-\u9fa5: 基本汉字
        // \u00B7: 中间点(·)

        // 空值校验
        if (this.toStr(value) === '') return true;

        // 验证格式: 不能以点开头或结尾，只允许中文和中间点
        return /^[\u4e00-\u9fa5]+(·[\u4e00-\u9fa5]+)*$/.test(value);
    },
    // 必须是整数
    isNum(value) {
        return this.toStr(value) === '' || /^[0-9]*$/.test(value)
    },
    // The zip code
    isZipCode(value) {
        return this.toStr(value) === '' || /^[1-9]\d{5}(?!\d)/g.test(value)
    },
    // 检测正则表达式
    isReg(value, reg) {
        let _reg = `${reg}`
        // 如果reg的开始、结尾是/ 则删除开始和结尾/
        if (_reg.startsWith('/')) _reg = _reg.substring(1)
        if (_reg.endsWith('/')) _reg = _reg.substring(0, _reg.length - 1)
        return this.toStr(value) === '' || new RegExp(_reg).test(value)
    },
    // 将自定义的validate转变为element的校验规则
    elVali(items) {
        if (_util.isEmpty(items) || !_util.isObject(items)) return {};  // 非空
        let _elMap = {}
        for (const k in items) {
            let _v = _util.string(items[k]);
            _elMap[k] = [
                {
                    validator: (rule, value, callback) => {
                        let [_map, _rule] = [{}, {}]
                        _map[rule.field] = value
                        _rule[rule.field] = _v
                        let _valiMsg = MyValidate.check(_map, _rule)
                        callback(_valiMsg !== '' ? new Error(_valiMsg) : undefined);
                    }, trigger: 'blur'
                }
            ];
            // 如果有必填项 直接设置required
            if (_v.indexOf('required') != -1) _elMap[k].push({
                required: true, trigger: 'blur'
            });
        }
        // console.log("🚀 -> file: vali.js:141 -> elVali -> _elMap:", _elMap)
        return _elMap;
    },

    // // 身份证
    // isIDCard(value) {
    //     return  /^\d{15}|\d{18}$/.test(value)
    // },
    // 校验
    check(value, rule) {
        let ruleLen = Object.getOwnPropertyNames(rule).length;
        if (ruleLen < 1) return true;
        // 获取validate规则  required|name|length:1,5#姓名
        let _getValidateRule = function (str, key) {
            let _r = { name: "", rule: [], value: [] }
            let _str = str;
            if (str.indexOf("#") !== -1) {   // getName
                let _nameAr = str.split("#")
                _str = _nameAr[0];
                _r.name = _nameAr[1];
            } else {
                _r.name = key;
            }
            if (_str.indexOf("|")) {   // getRules
                _r.rule = _str.split("|")
            } else if (_str !== '') {
                _r.rule = _str
            }
            return _r;
        }

        // 检查单条  rule = {field:'max',value:[8,10]}
        let _checkOneValidte = function (value, rule) {
            switch (rule.field) {
                case 'required': // 必填项
                    return MyValidate.isRequired(value) ? '' : '不得为空';
                case 'mobile': // 手机号格式
                    return MyValidate.isMobile(value) ? '' : '格式不正确';
                case 'tel': // 座机
                    return MyValidate.isTel(value) ? '' : '格式不正确';
                case 'code': // 验证码
                    return MyValidate.isCode(value) ? '' : '格式不正确';
                case 'name': // 登录账号 长度在4-16位之间
                    return MyValidate.isName(value) ? '' : '格式不正确';
                case 'password': //
                    return MyValidate.isPassword(value) ? '' : '格式不正确，只能是字母数字和下划线的组合';
                case 'info': //
                    return MyValidate.isLenth(value, 1, 2000) ? '' : '格式不正确，长度在1-2000之间';
                case 'id': // 必须大于0 id
                    return parseInt(String(value)) > 0 ? '' : '格式不正确';
                case 'email': //
                    return MyValidate.isEmail(value) ? '' : '格式不正确';
                case 'ip': //
                    return MyValidate.isIP(value) ? '' : '格式不正确';
                case 'chinese': //
                    return MyValidate.isChinese(value) ? '' : '格式不正确';
                case 'chineseName': //
                    return MyValidate.isChineseName(value) ? '' : '格式不正确';
                case 'idCard': //
                    return MyValidate.isIDCard(value) ? '' : '格式不正确';
                case 'between': //
                    return MyValidate.isBetween(String(value), rule.value[0], rule.value[1]) ? '' : '有误，应介于' + rule.value[0] + '和' + rule.value[1] + '之间';
                case 'len': //
                    return MyValidate.isLenth(value, rule.value[0], rule.value[1]) ? '' : '格式有误，长度应介于' + rule.value[0] + '和' + rule.value[1] + '之间';
                case 'min': //
                    return parseInt(String(value)) > rule.value[0] ? '' : '格式有误，不得小于' + rule.value[0];
                case 'max': //
                    return parseInt(String(value)) < rule.value[0] ? '' : '格式有误，不得大于' + rule.value[0];
                case 'in': //
                    return MyValidate.isIn(value, rule.value) ? '' : '参数有误';
                case 'notIn': //
                    return MyValidate.isNotIn(value, rule.value) ? '' : '参数有误';
                case 'reg': //
                    return MyValidate.isReg(value, rule.value[0]) ? '' : '格式有误';
                case 'zipCode': //
                    return MyValidate.isZipCode(value) ? '' : '参数有误';
                case 'num': //
                    return MyValidate.isNum(value) ? '' : '必须是整数';
                case 'url': //
                    return MyValidate.isUrl(value) ? '' : '格式有误';
                default:
                    return '';
            }
        }

        // 执行验证All
        let _checkValidate = function (value, rule) {
            let _ruleCount = rule.rule.length;
            if (_ruleCount.length < 1) return true  // lock
            for (let index = 0; index < _ruleCount; index++) {
                let _ruleItem = rule.rule[index];
                let _r = { field: _ruleItem, value: [] }
                // 检查是否存在:和,号
                if (_ruleItem.indexOf(":") > -1) {
                    let _ruleItemAr = _ruleItem.split(":");
                    _r.field = _ruleItemAr[0];
                    _r.value = _ruleItemAr[1].split(",");
                }


                // console.log(_r)
                let _b = _checkOneValidte(value, _r);
                if (_b !== "") {
                    return rule.name + _b;
                }
            }
            return "";
        }

        for (let _key in rule) {
            let _rule = rule[_key];  // 单个验证规则
            // console.log(_key, _rule, 'enum')
            let arr = _getValidateRule(_rule, _key);
            // console.log(arr);
            let arr2 = _checkValidate(value[_key], arr);
            // console.log(arr2);
            if (arr2 !== "") {
                return arr2;
            }
        }

        return "";
    }

}
export default MyValidate;
