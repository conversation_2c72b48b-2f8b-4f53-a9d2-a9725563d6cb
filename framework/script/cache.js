/*
 * @Description: 基于localstrage的缓存
 * @Author: 徐静(parkhansung)
 * @Date: 2022-11-22 13:52:53
 * @LastEditTime: 2023-11-19 10:00:13
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */


const _cache = {
    // 设置缓存
    set(key, value, time) {
        let data = {
            value: value,
            time: new Date().getTime(),
            timeOut: time ? new Date().getTime() + time * 1000 : 0
        }
        localStorage.setItem(key, JSON.stringify(data));
    },
    // 获取缓存
    get(key) {
        let data = localStorage.getItem(key);
        if (!data) {
            return null;
        }
        data = JSON.parse(data);
        if (data.timeOut > 0 && new Date().getTime() > data.timeOut) {
            localStorage.removeItem(key);
            return null;
        }
        return data.value;
    },
    // 删除缓存
    remove(key) {
        localStorage.removeItem(key);
    },
    // 获取缓存并检测缓存是否存在 如果不存在 则设置缓存的值为value 可以传入time设置缓存时间
    getOrSave(key, value, time) {
        let _v = _cache.get(key)
        if (_v != undefined) return _v; // 有缓存
        if (value != undefined) {
            _cache.set(key, value, time)
        }
        return value;
    },
    // 优先从value获取值 如果有值 就存储到缓存
    getAndSave(key, value, time) {
        if (value != undefined) {
            _cache.set(key, value, time)
            return value;
        } else {
            let _v = _cache.get(key)
            return _v //
        }
    },
    // 获取缓存 并删除缓存
    getAndRemove(key) {
        let _v = _cache.get(key)
        if (_v != undefined) {
            _cache.remove(key)
        }
        return _v;
    },
    // 获取 不存在则取默认值
    getOrDefault(key, defValue) {
        let _v = _cache.get(key)
        if (_v != undefined) {
            return _v;
        }
        return defValue;
    }
}


export default _cache