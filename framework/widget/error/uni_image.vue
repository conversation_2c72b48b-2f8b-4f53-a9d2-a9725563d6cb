<!--
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2023-08-31 09:53:40
 * @LastEditTime: 2025-03-20 14:52:11
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
-->
<template>
	<!-- #ifdef MP-WEIXIN -->
	<div class="">
		<div class="">askudhasd</div>
		<image :src="src"  alt style="width:50rpx;height:50rpx"></image>
	</div>
	<!--  #endif -->
	<!--  #ifndef MP-WEIXIN -->
	<u--image :src="currentSrc" :radius="radius" :width="width" :height="height">
		<template v-slot:error>
			<u--image
				:src="getDefaultSrcByType(type)"
				:radius="radius"
				:width="width"
				:height="height"
				mode="aspectFill"
			></u--image>
		</template>
	</u--image>
	<!--  #endif -->
</template>

<script>
import _util from "../../script/ecs";
export default {
	name: "my-image",
	props: {
		src: {
			type: String,
			default: "",
		},
		errorSrc: {
			type: String,
		},
		width: {
			type: [Number, String],
			default: null,
		},
		height: {
			type: [Number, String],
			default: null,
		},
		radius: {
			type: [Number, String],
			default: 0,
		},
		type: {
			type: String,
			default: "",
		},
		fit: {
			type: String,
			default: "scaleToFill",
		},
	},
	data() {
		return {
			showErrImg: false,
			avatarDefaultSrc: "avatar_default.jpg", // 默认头像的路径，你需要替换成你的实际路径
			otherTypeDefaultSrc: "other_type_default.jpg", // 其他类型的默认路径，你需要替换成你的实际路径
			imageSrc: this.src,
		};
	},
	computed: {
		// 图片样式 针对微信小程序的设计
		imgStyle2() {
			let _map = {}
			if (this.width !== null) {
				_map.width = typeof this.width === "number" ? this.width + "rpx" : this.width
			}
			if (this.height !== null) {
				_map.height = typeof this.height === "number" ? this.height + "rpx" : this.height
			}
			if (this.radius !== null) {
				_map.borderRadius = typeof this.radius === "number" ? this.radius + "rpx" : this.radius
			}
			return _map
		},
		currentSrc() {
			// console.log(this.src)
			if (!_util.isEmpty(this.src)) {
				if (this.src.startsWith("/")) {
					return this.$ecs.getBaseUrl() + this.src;
				} else {
					return this.src;
				}
			} else {
				return this.getDefaultSrcByType(this.type);
			}
		},
		imgStyle() {
			let style = {};
			if (this.width !== null) {
				style.width =
					typeof this.width === "number"
						? this.width + "px"
						: this.width;
			}
			if (this.height !== null) {
				style.height =
					typeof this.height === "number"
						? this.height + "px"
						: this.height;
			}
			if (this.radius !== null) {
				style.borderRadius =
					typeof this.radius === "number"
						? this.radius + "px"
						: this.radius;
			}
			return style;
		},
	},
	methods: {
		checkIsMPAPP() {
			const _system = uni.getSystemInfoSync().platform;
			console.log(
				"🚀 -> file: uni_image.vue:73 -> checkIsMPAPP -> uni.getSystemInfoSync().platform",
				_system
			);
			return _system === "mp-weixin";
		},
		getDefaultSrcByType() {
			return require("./default1.jpg");
			
		},
		onError1(arr, index) {
			// console.log("🚀 -> file: uni_image.vue:108 -> onError -> arr:", arr,index,"图片加载失败")
			// let img = event.srcElement;
			// console.log("图片加载失败");
			this.arr[index].src =
				this.errorSrc || this.getDefaultSrcByType(this.type);
			// img.onerror = null; //防止闪图
		},
		handleImageError(){
			// this.imageSrc = this.defaultImage; // 图片加载失败时，使用默认图片
		}
	},
};
</script>

<style scoped>
img {
	object-fit: cover;
	max-width: 100%;
}
</style>
