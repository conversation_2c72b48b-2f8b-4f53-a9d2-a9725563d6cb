/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-29 22:34:41
 * @LastEditTime: 2025-03-20 16:45:06
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
import Vue from 'vue';
import Popup from './toast.vue'
import ecs from '../../../script/ecs'


const _toastWidget = Vue.extend(Popup);

Popup.install = (...a) => {
  let data = ecs.args(a, {
    msg: '',
    duration: 1500
  })
  let instance = new _toastWidget({
    data,
  }).$mount()

  document.body.appendChild(instance.$el) // dom

  // 点击事件
  Vue.nextTick(() => {
    // console.log(data)
    instance.show({
      msg: instance.msg,
      duration: instance.duration
    })
  })
}

export default Popup