<!--
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-29 22:31:26
 * @LastEditTime: 2024-04-21 15:10:38
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
-->
<template>
<!-- <div class="npm-com-toast" v-show="isShow" @click="close"> -->
<div class="npm-com-toast" v-show="isShow">
    <div class="mask">
      <div class="box" @click.stop>
        <div class="html" v-html="msg"></div>
      </div>
    </div>
</div>
</template>
<script>
export default {
  data () {
    return {
      msg: '',
      isShow: false,
      timeout: null,
      resolve: null,
    };
  },
  methods: {
    /**
     * 显示toast
     * @desc 本方法为异步方法，将在toast关闭时执行成功回调
     * @param {Number} [options.msg=''] 显示的内容，可以是html
     * @param {Number} [options.duration=3000] 持续显示时间
     */
    async show ({msg = '', duration = 1500} = {}) {
      return new Promise(resolve => {
        // 清除之前的Loading定时器
        if (this.isShow) {
          this.close();
        }

        this.msg = msg;

        // 生成新的定时器
        this.isShow = true;
        this.resolve = resolve;
        this.timeout = setTimeout(this.close, duration);
      });
    },

    /**
     * 关闭toast
     * @param duration
     */
    close () {
      this.isShow = false;

      clearTimeout(this.timeout);
      this.timeout = null;

      // 清空显示内容
      this.msg = '';

      if (this.resolve) {
        this.resolve();
        this.resolve = null;
      }
    },

    /**
     * 显示/关闭toast提示
     * @desc 本方法为异步方法，将在toast关闭时执行成功回调
     * @param {Object} options 复合参数（值为false：隐藏|值为其他情况或不传：显示）
     * @param {Number} [options.duration=30000] 持续显示时间
     */
    async toast (options) {
      if (options === false) {
        await this.close();
      } else {
        await this.show(options);
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.npm-com-toast {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: table;
  z-index: 999999;
  line-height: 1;

  .mask {
    display: table-cell;
    vertical-align: bottom;
    text-align: center;
    animation: npm-com-toast-mask-animation 0.2s forwards;
    padding-bottom: 100px;
    // @keyframes npm-com-toast-mask-animation {
    //   0% {
    //     background: rgba(0, 0, 0, 0);
    //   }

    //   100% {
    //     background: rgba(0, 0, 0, 0.1);
    //   }
    // }

    .box {
      display: inline-block;
      box-sizing: border-box;
      padding: 10px 15px;
      min-width: 120px;
      max-width: 320px;
      min-height: 40px;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.75);
      color: #FFF;
      font-size: 14px;
      line-height: 20px;
      animation: npm-com-toast-mask-box-animation 0.2s forwards;

      @keyframes npm-com-toast-mask-box-animation {
        0% {
          transform: scale(0);
        }

        100% {
          transform: scale(1);
        }
      }

      .html {
        display: inline-block;
        text-align: center;
      }
    }
  }
}
</style>