<template>
	<div class="confirm" v-if="isShow">
		<div class="con_box" :class="object.temp != 1 ? `confirm-height2` : `confirm-height1`">
			<div class="header">
				<div class="left oneline">{{ object.type }}</div>
				<div class="right icon-for-close fs" @click="close()">×</div>
			</div>
			<div class="context">
				<div class="content15 w100 h100" :class="object.temp != 1 ? 'tip' : 'tl  c333'">
					<div class="fc">
						<div class="fs" v-if="object.icon != ''">
							<img :src="icon[object.icon]" alt class="icon-image"/>
						</div>
						<p class="max-line-4 e1 tl" v-html="object.msg"></p>
					</div>
				</div>
				<div :class="object.temp != 1 ? 'btns' : 'btns2 f-14'">
					<div class="cancelBtn btn bg-water" @click="close()">{{ object.btn.close }}</div>
					<div class="okBtn btn bg-water" @click="confirm()">{{ object.btn.confirm }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import errorIcon from "@/static/error.png";
import warningIcon from "@/static/warning.png";
import successIcon from "@/static/success.png";
import primaryIcon from "@/static/primary.png";
import infoIcon from "@/static/info.png";

export default {
	data() {
		return {
			isShow: true,
			object: {
				type: "提示",
				msg: "确定删除此条信息？",
				btn: {
					confirm: "确定",
					close: "取消",
				},
			},
			icon: {
				error: errorIcon,
				warning: warningIcon,
				success: successIcon,
				primary: primaryIcon,
				info: infoIcon,
			},
		};
	},
	methods: {
		close() {
			this.isShow = false;
			console.log("关闭");
		},
		confirm() {
			console.log("确定");
		},
	},
};
</script>

<style lang="scss" scoped>
.confirm-height1{
    height:185px;
}
.confirm-height2{
    height:210px;
}
.icon-image{
    width:40px;
    height:40px;
    // margin-top:12px;
    margin-right:5px;
}
@keyframes myfirst {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

.content15 {
	min-height: 85px;
	padding: 15px 15px 10px 15px;
	font-size: 15px;
    line-height: 20px;
}
.icon-for-close {
	font-size: 24px;
	color: #666666;
}
.icon-for-close:hover,
.btns .btn:hover {
	cursor: pointer;
}

.confirm {
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999999;
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	animation: myfirst 0.2s;

	.tip {
		margin: 0 auto;
		text-align: center;

		p {
			font-size: 14px;
			color: #787993;
			margin-top: 20px;
			margin-bottom: 36px;
		}
	}

	.header {
		// width: 318px;
		width: 100%;
		height: 42px;
		background-color: #f9f9fd;
		border-radius: 16px 16px 0px 0px;
		padding: 0 20px;
		box-sizing: border-box;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		font-size: 15px;
	}

	.con_box {
		width: 420px;
		max-width: 90%;
		background-color: #ffffff;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		border-radius: 5px;
	}

	.btns2 {
		display: flex;
		flex-direction: row;
		width: 100%;
		justify-content: flex-end;
		align-items: center;

		.btn {
			height: 35px;
			font-size: 13px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.cancelBtn {
			font-size: 15px;
			margin-right: 15px;
			color: #777;
		}

		.okBtn {
			font-size: 15px;
			color: #1890ff;
			padding-right: 25px;
			font-weight: 600;
		}
	}

	.btns {
		display: flex;
		flex-direction: row;
		width: 100%;
		justify-content: center;
		align-items: center;

		.btn {
			border-radius: 6px;
			width: 120px;
			height: 35px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.cancelBtn {
			background-color: #f9f9fd;
			border: solid 1px #f1f1f1;
			font-size: 14px;
			color: #787993;
			margin-right: 15px;
		}

		.okBtn {
			background-color: #409eff;
			font-size: 14px;
			color: #ffffff;
		}
	}
}
</style>
