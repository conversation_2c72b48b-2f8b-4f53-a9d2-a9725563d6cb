<template>
	<div class="alert" v-if="isShow" @click="closeModel()">
		<div class="con_box flex-column" @click.stop>
			<div class="header">
				<div class="left">{{ object.title }}</div>
				<div class="tr icon-for-close" @click="close()">×</div>
			</div>
            <div class="content15" :class="object.temp != 1 ? 'tip' : 'tl c333'">
					<div class="fc">
						<div class="fs" v-if="object.icon != ''">
							<img :src="icon[object.icon]" alt class="icon-image"/>
						</div>
						<p class="max-line-4 e1 tl">{{ object.msg }}</p>
					</div>
            </div>
            <div class="" :class="object.temp != 1 ? 'btns tc  border-top-info-1' : 'btns2 tr c777'">
                <div class="cancelBtn btn tr " >
					<span @click="close()" class="pl-20 bg-water" > {{ object.btn.close }}</span>
                </div>
            </div>
		</div>
	</div>
</template>

<script>
import errorIcon from "@/static/error.png";
import warningIcon from "@/static/warning.png";
import successIcon from "@/static/success.png";
import primaryIcon from "@/static/primary.png";
import infoIcon from "@/static/info.png";

export default {
	data() {
		return {
			isShow: true,
			object: {
				title: "提示",
				msg: "确定删除此条信息？",
				btn: {
					confirm: "确定",
					close: "取消",
				},
				icon:"",
			},
			icon: {
				error: errorIcon,
				warning: warningIcon,
				success: successIcon,
				primary: primaryIcon,
				info: infoIcon,
			},
		};
	},
	methods: {
		close() {
			this.isShow = false;
			console.log("关闭");
		},
		confirm() {
			console.log("确定");
		},
		closeModel(){
			if(this.object.allowModelClose){
				console.log("closeModel");
				this.close();
			}
		},
	},
};
</script>

<style lang="scss" scoped>
.icon-image{
    width:40px;
    height:40px;
    margin-top:12px;
    margin-right:5px;
}
.content-15{
	font-size: 15px;
}
.content15{
    min-height: 80px;
    padding: 15px 20px 10px 15px;
    font-size: 15px;
	line-height: 20px;
}
@keyframes myfirst {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

.icon-for-close {
	font-size: 24px;
	color: #666666;
}
.icon-for-close:hover,
.btns .btn:hover {
	cursor: pointer;
}
.btns2{
	padding:15px 20px;
	font-size: 14px;
}

.alert {
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999999;
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	animation: myfirst 0.2s;

	.tip {
		margin: 0 auto;
	    // text-align: center;

		p {
			font-size: 14px;
			color: #444444;
			margin: 20px 20px;
			// margin-bottom: px;
		}
	}

	.header {
		width: 100%;
		// max-width: 320px;
		height: 42px;
		background-color: #f9f9f9;
        color: #444444;
		border-radius: 16px 16px 0px 0px;
		padding: 0 20px;
		box-sizing: border-box;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.left {
			font-size: 14px;
			font-weight: bold;
		}
	}

	.con_box {
		width: 75%;
		width: 420px;
		max-width: 90%;
		max-height: 180px;
		background-color: #ffffff;
		position: absolute;
		top: 30%;
		// bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		border-radius: 5px;
	}

	.btns {
		display: flex;
		flex-direction: row;
		width: 100%;
		justify-content: center;
		align-items: center;

		.btn {
            margin-top:6px;
            margin-bottom:6px;
			border-radius: 6px;
			width: 120px;
			height: 35px;
			display: flex;
			align-items: center;
			justify-content: center;
            font-size: 14px;
		}

		.cancelBtn {
			font-size: 14px;
			color: #777;
			margin-right: 15px;
		}
	}
}
</style>
