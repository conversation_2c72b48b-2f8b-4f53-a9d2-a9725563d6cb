/*
 * @Description:
 * @Author: 徐静(parkhansung)
 * @Date: 2022-10-29 22:58:21
 * @LastEditTime: 2025-04-23 17:51:46
 * @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
 */
import Vue from "vue";
import confirm from "./alert.vue";

import ecs from '../../../script/ecs'

let confirmConstructor = Vue.extend(confirm);

let _ecsAlert =  (...a) => {
    return new Promise((res, rej) => {
        //promise封装，ok执行resolve，no执行rejectlet
        let confirmDom = new confirmConstructor({
            el: document.createElement("div")
        });
        document.body.appendChild(confirmDom.$el); //new一个对象，然后插入body里面
        // 类型
        let _obj = ecs.args(a, {
            msg: '',
            title: {
                type:"string",
                value: "提示",
                items:1,
            },
            closeBtn: {
                type:"string",
                value: "关闭",
                items:2,
            },
            temp:1,
            allowModelClose: true,
            icon: {
                type: "string",
                items: ['primary', 'success', 'warning', 'danger', 'info'],
                value: "primary"
            },
        });
        _obj.btn = {
            confirm: _obj?.confirmBtn || '确认',
            close: _obj?.closeBtn || '关闭',
        }
        confirmDom.object = _obj; //为了使confirm的扩展性更强，这个采用对象的方式传入，所有的字段都可以根据需求自定义
        // console.log("🚀 -> file: index.js:48 -> returnnewPromise -> _obj:", _obj)
        confirmDom.confirm = function () {
            res(1);
            confirmDom.isShow = false;
        };
        confirmDom.close = function () {
            res(0);
            confirmDom.isShow = false;
        };
    });
};



export default _ecsAlert;

//暴露出去，别忘记挂载到vue的原型上
// => 在main.js里面先引入 import theConfirm from './components/confirm/confirm.js'
// => 再挂载 Vue.prototype.$confirm = theConfirm;
//在需要的地方直接用以下方法调用即可：
            // this.$confirm({
            //         type: '提示',
            //         msg: '是否删除这条信息222？',
            //         btn: {
            //             confirm: '确认',
            //             close: '取消'
            //         }
            //     }).then(() => {
            //         console.log('ok')
            //     })
            //     .catch(() => {
            //         console.log('close')
            //     })

