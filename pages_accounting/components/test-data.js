/**
 * @Description: 测试数据生成器
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
 */

import storage from '../data/storage.js'
import { ACCOUNT_TYPES } from '../data/categories.js'

// 生成测试数据
export function generateTestData() {
  // 清空现有数据
  storage.clearAllData()
  
  // 生成测试记录
  const testRecords = [
    // 支出记录
    {
      type: ACCOUNT_TYPES.EXPENSE,
      categoryId: 'food',
      amount: 35.50,
      note: '午餐 - 麻辣烫',
      date: '2025-07-16',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.EXPENSE,
      categoryId: 'transport',
      amount: 12.00,
      note: '地铁费用',
      date: '2025-07-16',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.EXPENSE,
      categoryId: 'shopping',
      amount: 299.00,
      note: '购买衣服',
      date: '2025-07-15',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.EXPENSE,
      categoryId: 'entertainment',
      amount: 68.00,
      note: '电影票',
      date: '2025-07-14',
      tags: []
    },
    
    // 收入记录
    {
      type: ACCOUNT_TYPES.INCOME,
      categoryId: 'salary',
      amount: 8000.00,
      note: '月工资',
      date: '2025-07-01',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.INCOME,
      categoryId: 'bonus',
      amount: 500.00,
      note: '项目奖金',
      date: '2025-07-10',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.INCOME,
      categoryId: 'red_packet',
      amount: 88.00,
      note: '朋友红包',
      date: '2025-07-12',
      tags: []
    },
    
    // 不计入收入记录
    {
      type: ACCOUNT_TYPES.TRANSFER,
      categoryId: 'investment',
      amount: 1000.00,
      note: '购买基金',
      date: '2025-07-05',
      tags: []
    },
    {
      type: ACCOUNT_TYPES.TRANSFER,
      categoryId: 'loan',
      amount: 200.00,
      note: '借给朋友',
      date: '2025-07-08',
      tags: []
    }
  ]
  
  // 添加测试记录
  testRecords.forEach(record => {
    storage.addRecord(record)
  })
  
  console.log('测试数据生成完成')
  
  return {
    recordCount: testRecords.length,
    message: '测试数据生成成功'
  }
}

// 清空所有数据
export function clearAllData() {
  storage.clearAllData()
  console.log('所有数据已清空')
  
  return {
    message: '数据清空成功'
  }
}

// 获取数据统计
export function getDataStats() {
  const records = storage.getRecords()
  const tags = storage.getTags()
  
  const stats = {
    totalRecords: records.length,
    totalTags: tags.length,
    expenseRecords: records.filter(r => r.type === ACCOUNT_TYPES.EXPENSE).length,
    incomeRecords: records.filter(r => r.type === ACCOUNT_TYPES.INCOME).length,
    transferRecords: records.filter(r => r.type === ACCOUNT_TYPES.TRANSFER).length
  }
  
  console.log('数据统计:', stats)
  return stats
}
