<template>
  <view class="statistics-container">
    <!-- 时间范围选择器 -->
    <view class="time-range-selector">
      <view
        v-for="range in timeRanges"
        :key="range.key"
        class="time-range-item"
        :class="{ active: currentRange === range.key }"
        @click="selectTimeRange(range.key)"
      >
        {{ range.label }}
      </view>
    </view>

    <!-- 自定义时间选择 -->
    <view v-if="currentRange === 'custom'" class="custom-time-section">
      <view class="custom-time-row">
        <view class="time-label">开始日期</view>
        <picker
          mode="date"
          :value="customStartDate"
          @change="onStartDateChange"
        >
          <view class="time-picker">{{ customStartDate }}</view>
        </picker>
      </view>
      <view class="custom-time-row">
        <view class="time-label">结束日期</view>
        <picker
          mode="date"
          :value="customEndDate"
          @change="onEndDateChange"
        >
          <view class="time-picker">{{ customEndDate }}</view>
        </picker>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stats-card">
        <view class="stats-header">
          <view class="stats-title">{{ currentRangeText }}</view>
          <view class="stats-period">{{ formatDateRange() }}</view>
        </view>
        <view class="stats-body">
          <view class="stats-row">
            <view class="stats-label">收入</view>
            <view class="stats-value income">{{ formatAmount(statistics.income) }}</view>
          </view>
          <view class="stats-row">
            <view class="stats-label">支出</view>
            <view class="stats-value expense">{{ formatAmount(statistics.expense) }}</view>
          </view>
          <view class="stats-row">
            <view class="stats-label">不计入收入</view>
            <view class="stats-value transfer">{{ formatAmount(statistics.transfer) }}</view>
          </view>
          <view class="stats-row balance-row">
            <view class="stats-label">结余</view>
            <view class="stats-value balance" :class="{ negative: statistics.balance < 0 }">
              {{ formatAmount(statistics.balance) }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 柱状图 -->
    <view class="chart-section">
      <view class="chart-card">
        <view class="chart-header">
          <view class="chart-title">收支对比</view>
        </view>
        <view class="chart-container">
          <view class="chart-bars">
            <view class="chart-bar">
              <view class="bar-label">收入</view>
              <view class="bar-container">
                <view
                  class="bar-fill income-bar"
                  :style="{ height: getBarHeight(statistics.income) + '%' }"
                ></view>
              </view>
              <view class="bar-value">{{ formatAmount(statistics.income) }}</view>
            </view>

            <view class="chart-bar">
              <view class="bar-label">支出</view>
              <view class="bar-container">
                <view
                  class="bar-fill expense-bar"
                  :style="{ height: getBarHeight(statistics.expense) + '%' }"
                ></view>
              </view>
              <view class="bar-value">{{ formatAmount(statistics.expense) }}</view>
            </view>

            <view class="chart-bar">
              <view class="bar-label">不计入</view>
              <view class="bar-container">
                <view
                  class="bar-fill transfer-bar"
                  :style="{ height: getBarHeight(statistics.transfer) + '%' }"
                ></view>
              </view>
              <view class="bar-value">{{ formatAmount(statistics.transfer) }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分类统计 -->
    <view class="category-stats-section">
      <view class="category-tabs">
        <view
          v-for="(config, type) in accountTypeConfigs"
          :key="type"
          class="category-tab"
          :class="{ active: currentCategoryType === type }"
          @click="switchCategoryType(type)"
        >
          <text class="tab-icon">{{ config.icon }}</text>
          <text class="tab-text">{{ config.name }}</text>
        </view>
      </view>

      <view class="category-list">
        <view v-if="categoryStats.length > 0">
          <view
            v-for="item in categoryStats"
            :key="item.categoryId"
            class="category-item"
          >
            <view class="category-info">
              <view
                class="category-icon"
                :style="{ backgroundColor: item.color }"
              >
                {{ item.icon }}
              </view>
              <view class="category-details">
                <view class="category-name">{{ item.name }}</view>
                <view class="category-count">{{ item.count }}笔记录</view>
              </view>
            </view>
            <view class="category-amount">
              <view class="amount-value" :class="currentCategoryType">
                {{ formatAmount(item.amount) }}
              </view>
              <view class="amount-percent">
                {{ getPercentage(item.amount, getCategoryTotal()) }}%
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <view class="empty-icon">📊</view>
          <view class="empty-text">暂无{{ currentCategoryTypeConfig.name }}记录</view>
        </view>
      </view>
    </view>

    <!-- 记录数量统计 -->
    <view class="record-count-section">
      <view class="count-card">
        <view class="count-header">
          <view class="count-title">记录统计</view>
        </view>
        <view class="count-body">
          <view class="count-item">
            <view class="count-label">总记录数</view>
            <view class="count-value">{{ statistics.recordCount }}笔</view>
          </view>
          <view class="count-item">
            <view class="count-label">日均记录</view>
            <view class="count-value">{{ getDailyAverage() }}笔</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import storage from '../data/storage.js'
import { ACCOUNT_TYPES, ACCOUNT_TYPE_CONFIG, getCategoryById } from '../data/categories.js'
import { formatAmount, formatDate, getDateRange, getDaysBetween } from '../data/utils.js'

export default {
  name: 'Statistics',
  data() {
    return {
      currentRange: 'month',
      currentCategoryType: ACCOUNT_TYPES.EXPENSE,
      customStartDate: '',
      customEndDate: '',
      statistics: {
        income: 0,
        expense: 0,
        transfer: 0,
        balance: 0,
        recordCount: 0
      },
      categoryStats: [],
      timeRanges: [
        { key: 'today', label: '今天' },
        { key: 'week', label: '本周' },
        { key: 'month', label: '本月' },
        { key: 'quarter', label: '本季度' },
        { key: 'year', label: '本年' },
        { key: 'custom', label: '自定义' }
      ]
    }
  },

  computed: {
    accountTypeConfigs() {
      return ACCOUNT_TYPE_CONFIG
    },

    currentCategoryTypeConfig() {
      return ACCOUNT_TYPE_CONFIG[this.currentCategoryType]
    },

    currentRangeText() {
      const range = this.timeRanges.find(r => r.key === this.currentRange)
      return range ? range.label : '自定义时间'
    }
  },

  onLoad() {
    this.initCustomDates()
    this.loadStatistics()
  },

  methods: {
    // 初始化自定义日期
    initCustomDates() {
      const now = new Date()
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

      this.customStartDate = formatDate(startOfMonth, 'date')
      this.customEndDate = formatDate(now, 'date')
    },

    // 选择时间范围
    selectTimeRange(range) {
      this.currentRange = range
      this.loadStatistics()
    },

    // 自定义开始日期变化
    onStartDateChange(e) {
      this.customStartDate = e.detail.value
      if (this.currentRange === 'custom') {
        this.loadStatistics()
      }
    },

    // 自定义结束日期变化
    onEndDateChange(e) {
      this.customEndDate = e.detail.value
      if (this.currentRange === 'custom') {
        this.loadStatistics()
      }
    },

    // 获取日期范围
    getDateRange() {
      if (this.currentRange === 'custom') {
        return {
          startDate: this.customStartDate,
          endDate: this.customEndDate
        }
      }
      return getDateRange(this.currentRange)
    },

    // 格式化日期范围显示
    formatDateRange() {
      const { startDate, endDate } = this.getDateRange()
      if (startDate === endDate) {
        return formatDate(startDate, 'date')
      }
      return `${formatDate(startDate, 'date')} 至 ${formatDate(endDate, 'date')}`
    },

    // 加载统计数据
    loadStatistics() {
      const { startDate, endDate } = this.getDateRange()

      // 加载基础统计
      this.statistics = storage.getStatistics(startDate, endDate)

      // 加载分类统计
      this.loadCategoryStats()
    },

    // 加载分类统计
    loadCategoryStats() {
      const { startDate, endDate } = this.getDateRange()
      const records = storage.getRecordsByDateRange(startDate, endDate)

      // 按分类统计
      const categoryMap = new Map()

      records
        .filter(record => record.type === this.currentCategoryType)
        .forEach(record => {
          const categoryId = record.categoryId
          if (!categoryMap.has(categoryId)) {
            const category = getCategoryById(record.type, categoryId)
            categoryMap.set(categoryId, {
              categoryId,
              name: category && category.name || '未知分类',
              icon: category && category.icon || '❓',
              color: category && category.color || '#8c8c8c',
              amount: 0,
              count: 0
            })
          }

          const item = categoryMap.get(categoryId)
          item.amount += record.amount / 100 // 转换为元
          item.count += 1
        })

      // 转换为数组并排序
      this.categoryStats = Array.from(categoryMap.values())
        .sort((a, b) => b.amount - a.amount)
    },

    // 切换分类类型
    switchCategoryType(type) {
      this.currentCategoryType = type
      this.loadCategoryStats()
    },

    // 获取柱状图高度百分比
    getBarHeight(value) {
      const maxValue = Math.max(
        this.statistics.income,
        this.statistics.expense,
        this.statistics.transfer
      )

      if (maxValue === 0) return 0
      return Math.max((value / maxValue) * 100, 5) // 最小高度5%
    },

    // 获取分类总额
    getCategoryTotal() {
      return this.categoryStats.reduce((total, item) => total + item.amount, 0)
    },

    // 获取百分比
    getPercentage(value, total) {
      if (total === 0) return 0
      return Math.round((value / total) * 100)
    },

    // 获取日均记录数
    getDailyAverage() {
      const { startDate, endDate } = this.getDateRange()
      const days = getDaysBetween(startDate, endDate) || 1
      return Math.round(this.statistics.recordCount / days * 10) / 10
    },

    formatAmount,
    formatDate
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.statistics-container {
  background-color: $background-color;
  min-height: 100vh;
  padding-bottom: $spacing-lg;
}

.custom-time-section {
  background-color: $card-background;
  margin: $spacing-md;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;

  .custom-time-row {
    display: flex;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    &:last-child {
      border-bottom: none;
    }

    .time-label {
      width: 200rpx;
      font-size: $font-size-md;
      color: $text-primary;
    }

    .time-picker {
      flex: 1;
      text-align: right;
      font-size: $font-size-md;
      color: $primary-color;
      padding: $spacing-sm;
    }
  }
}

.stats-overview {
  margin: $spacing-md;
}

.chart-section {
  margin: $spacing-md;

  .chart-card {
    @extend .card;

    .chart-header {
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      .chart-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .chart-container {
      padding: $spacing-lg;
      height: 500rpx;

      .chart-bars {
        display: flex;
        align-items: flex-end;
        justify-content: space-around;
        height: 100%;

        .chart-bar {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          max-width: 150rpx;

          .bar-label {
            font-size: $font-size-sm;
            color: $text-secondary;
            margin-bottom: $spacing-sm;
          }

          .bar-container {
            flex: 1;
            width: 60rpx;
            background-color: $background-color;
            border-radius: $border-radius-sm;
            display: flex;
            align-items: flex-end;
            margin-bottom: $spacing-sm;

            .bar-fill {
              width: 100%;
              border-radius: $border-radius-sm;
              transition: height 0.3s ease;

              &.income-bar {
                background-color: $income-color;
              }

              &.expense-bar {
                background-color: $expense-color;
              }

              &.transfer-bar {
                background-color: $transfer-color;
              }
            }
          }

          .bar-value {
            font-size: $font-size-xs;
            color: $text-light;
            text-align: center;
          }
        }
      }
    }
  }
}

.category-stats-section {
  margin: $spacing-md;

  .category-tabs {
    background-color: $card-background;
    display: flex;
    border-radius: $border-radius-lg $border-radius-lg 0 0;
    box-shadow: $shadow-sm;

    .category-tab {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: $spacing-md;
      transition: all 0.2s ease;

      .tab-icon {
        font-size: 32rpx;
        margin-bottom: $spacing-xs;
      }

      .tab-text {
        font-size: $font-size-sm;
        color: $text-secondary;
      }

      &.active {
        background-color: rgba($primary-color, 0.1);

        .tab-icon,
        .tab-text {
          color: $primary-color;
        }
      }
    }
  }

  .category-list {
    background-color: $card-background;
    border-radius: 0 0 $border-radius-lg $border-radius-lg;
    box-shadow: $shadow-sm;

    .category-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      &:last-child {
        border-bottom: none;
      }

      .category-info {
        display: flex;
        align-items: center;
        flex: 1;

        .category-icon {
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: $spacing-md;
          font-size: 32rpx;
          color: white;
        }

        .category-details {
          .category-name {
            font-size: $font-size-md;
            font-weight: 600;
            color: $text-primary;
            margin-bottom: 4rpx;
          }

          .category-count {
            font-size: $font-size-xs;
            color: $text-light;
          }
        }
      }

      .category-amount {
        text-align: right;

        .amount-value {
          font-size: $font-size-md;
          font-weight: 600;
          margin-bottom: 4rpx;

          &.income {
            color: $income-color;
          }

          &.expense {
            color: $expense-color;
          }

          &.transfer {
            color: $transfer-color;
          }
        }

        .amount-percent {
          font-size: $font-size-xs;
          color: $text-light;
        }
      }
    }
  }
}

.record-count-section {
  margin: $spacing-md;

  .count-card {
    @extend .card;

    .count-header {
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      .count-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .count-body {
      padding: $spacing-lg;

      .count-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-md;

        &:last-child {
          margin-bottom: 0;
        }

        .count-label {
          font-size: $font-size-md;
          color: $text-secondary;
        }

        .count-value {
          font-size: $font-size-md;
          font-weight: 600;
          color: $primary-color;
        }
      }
    }
  }
}

.balance-row {
  border-top: 1rpx solid $divider-color;
  padding-top: $spacing-md !important;
  margin-top: $spacing-md;

  .stats-value.negative {
    color: $expense-color;
  }
}
</style>
