# 记账系统 API 接口文档

## 分类管理

### 1. 获取分类列表
**接口地址：** `GET /accounting/categories`

**请求参数：**
```javascript
{
  "type": 1  // 可选，分类类型：1支出 2入账 3不计入收入，不传则返回所有
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "餐饮",
      "type": 1,
      "color": "#ff7875",
      "emoji": "🍽️",
      "weight": 1,
      "isSystem": 1,           // 1系统分类 0用户自定义
      "createTime": **********,
      "updateTime": **********
    }
  ]
}
```

**特殊说明：**
- 新用户首次请求分类时，系统会自动初始化默认分类
- 系统分类（isSystem=1）不能删除和修改
- 用户可以创建自定义分类（isSystem=0）

### 2. 保存分类（新增/编辑）
**接口地址：** `POST /accounting/category/save`

**请求参数：**
```javascript
{
  "id": 1,           // 可选，编辑时传入，新增时不传
  "name": "餐饮",     // 必填，分类名称
  "type": 1,         // 必填，分类类型：1支出 2入账 3不计入收入
  "color": "#ff6b6b", // 可选，背景颜色
  "emoji": "🍽️",     // 可选，图标emoji
  "weight": 1        // 可选，权重排序
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "保存分类",
  "data": null
}
```

**注意事项：**
- 系统分类不允许修改，尝试修改会返回失败
- 只能新增或编辑用户自定义分类

### 3. 删除分类
**接口地址：** `POST /accounting/category/delete`

**请求参数：**
```javascript
{
  "id": 1  // 必填，分类ID
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "删除分类",
  "data": null
}
```

**注意事项：**
- 系统分类不允许删除，尝试删除会返回失败
- 如果分类下有记账记录，不允许删除

## 标签管理

### 4. 获取标签列表
**接口地址：** `GET /accounting/tags`

**请求参数：**
```javascript
{
  "type": 1  // 可选，标签类型：1支出 2入账 3不计入收入，不传则返回所有
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "msg": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "外卖",
      "type": 1,
      "color": "#4ecdc4",
      "createTime": **********,
      "updateTime": **********
    }
  ]
}
```

### 5. 保存标签（新增/编辑）
**接口地址：** `POST /accounting/tag/save`

**请求参数：**
```javascript
{
  "id": 1,           // 可选，编辑时传入，新增时不传
  "name": "外卖",     // 必填，标签名称
  "type": 1,         // 必填，标签类型：1支出 2入账 3不计入收入
  "color": "#4ecdc4" // 可选，标签颜色
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "保存标签",
  "data": null
}
```

### 6. 删除标签
**接口地址：** `POST /accounting/tag/delete`

**请求参数：**
```javascript
{
  "id": 1  // 必填，标签ID
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "删除标签",
  "data": null
}
```

## 记账记录管理

### 7. 获取记账记录列表
**接口地址：** `GET /accounting/records`

**请求参数：**
```javascript
{
  "type": 1,         // 可选，记录类型：1支出 2入账 3不计入收入
  "categoryId": 1,   // 可选，分类ID
  "startTime": **********,  // 可选，开始时间戳
  "endTime": **********,    // 可选，结束时间戳
  "pageNum": 1,      // 可选，页码，默认1
  "pageSize": 20     // 可选，每页条数，默认20
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "rows": [
    {
      "id": 1,
      "type": 1,
      "categoryId": 1,
      "categoryName": "餐饮",
      "categoryEmoji": "🍽️",
      "amount": 25.50,
      "description": "午餐",
      "images": ["https://example.com/image1.jpg"],
      "recordTime": **********,
      "createTime": **********,
      "updateTime": **********,
      "tags": [
        {
          "id": 1,
          "name": "外卖",
          "color": "#4ecdc4"
        }
      ]
    }
  ],
  "total": 100
}
```

### 8. 保存记账记录（新增/编辑）
**接口地址：** `POST /accounting/record/save`

**请求参数：**
```javascript
{
  "id": 1,                    // 可选，编辑时传入，新增时不传
  "type": 1,                  // 必填，类型：1支出 2入账 3不计入收入
  "categoryId": 1,            // 必填，分类ID
  "amount": 25.50,            // 必填，金额
  "description": "午餐",       // 可选，备注描述
  "images": ["image1.jpg"],   // 可选，图片数组
  "recordTime": **********,   // 必填，记账时间戳
  "tags": ["外卖", "快餐"]     // 可选，标签名称数组
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "保存记账记录",
  "data": null
}
```

### 9. 删除记账记录
**接口地址：** `POST /accounting/record/delete`

**请求参数：**
```javascript
{
  "id": 1  // 必填，记录ID
}
```

**返回结构：**
```javascript
{
  "back": 1,      // 1成功 0失败
  "code": 200,
  "msg": "删除记账记录",
  "data": null
}
```

## 统计数据

### 10. 获取统计数据
**接口地址：** `GET /accounting/statistics`

**请求参数：**
```javascript
{
  "startTime": **********,  // 可选，开始时间戳
  "endTime": **********     // 可选，结束时间戳
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "msg": "获取统计数据成功",
  "data": {
    "expense": 1500.50,    // 支出总额
    "income": 8000.00,     // 收入总额
    "noCount": 200.00,     // 不计入收入总额
    "balance": 6499.50,    // 结余（收入-支出）
    "startTime": **********,
    "endTime": **********,
    "typeStats": [         // 按类型统计详情
      {
        "type": 1,
        "totalAmount": 1500.50,
        "count": 45
      },
      {
        "type": 2,
        "totalAmount": 8000.00,
        "count": 8
      }
    ]
  }
}
```

## 系统默认分类

### 支出分类（type=1）
- 餐饮 🍽️ #ff7875
- 交通 🚗 #40a9ff
- 服饰 👕 #b37feb
- 购物 🛍️ #ff9c6e
- 服务 🔧 #36cfc9
- 教育 📚 #73d13d
- 娱乐 🎮 #ffb347
- 运动 ⚽ #95de64
- 生活缴费 💡 #ffd666
- 旅行 ✈️ #87e8de
- 宠物 🐕 #ffadd2
- 医疗 🏥 #ff85c0
- 其他 🔍 #8c8c8c

### 入账分类（type=2）
- 生意 💼 #1890ff
- 工资 💰 #52c41a
- 奖金 🎁 #faad14
- 其他人情 🧧 #ff7875
- 收红包 🧧 #f759ab
- 收转账 📱 #40a9ff
- 商家转账 🏪 #36cfc9
- 退款 ↩️ #95de64
- 其他 🔍 #8c8c8c

### 不计入收入分类（type=3）
- 理财 📈 #722ed1
- 借还款 🤝 #faad14
- 其他 🔍 #8c8c8c

## 数据类型说明

### 记账类型
- `1`: 支出
- `2`: 入账（收入）
- `3`: 不计入收入

### 分类类型
- `isSystem = 1`: 系统默认分类，不可删除和修改
- `isSystem = 0`: 用户自定义分类，可以删除和修改

### 时间格式
- 所有时间字段均为10位时间戳格式

### 注意事项
1. 所有接口都需要用户登录鉴权
2. 新用户首次获取分类时自动初始化默认分类
3. 系统分类不允许删除和修改，确保基础功能可用
4. 标签支持数组传入，系统会自动创建不存在的标签
5. 删除操作为软删除，数据可恢复
6. 分类和标签在同类型下名称不能重复
7. 图片字段为字符串数组，存储图片路径或URL

### 11. 获取记账记录列表（新版本）
**接口地址：** `GET /accounting/record/list`

**请求参数：**
```javascript
{
  "type": "income",                    // 必填，记录类型：income=收入，expense=支出，all=全部
  "dates": "2024-11-11,2024-11-12,2024-11-13",  // 可选，日期数组，逗号分隔，格式：YYYY-MM-DD
  "page": 1,                          // 可选，页码，默认1
  "pageSize": 20                      // 可选，每页条数，默认20
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "type": 1,
      "categoryId": 1,
      "categoryName": "午餐",
      "categoryEmoji": "🍽️",
      "categoryColor": "#FF6B6B",
      "amount": "35.50",
      "description": "麻辣烫",
      "images": [],
      "recordTime": **********,
      "createTime": **********
    }
  ],
  "count": 100
}
```

**特点说明：**
- 支持多日期筛选：可以传入多个日期，用逗号分隔
- 类型筛选：支持按收入、支出、全部进行筛选
- 性能优化：使用索引优化查询性能
- 数据格式：返回数据已转换为小驼峰格式

**前端接入状态：** ✅ 已接入
- 文件：`pages_accounting/index/components/AccountingMode.vue`
- 方法：`loadRecentRecords()`, `filterRecordsByDate()`, `setFilter()`
- 功能：支持分页加载、类型筛选、日期筛选

### 12. 获取指定月份有记录的日期列表
**接口地址：** `GET /accounting/month/dates`

**请求参数：**
```javascript
{
  "month": "2024-05"  // 必填，月份格式：YYYY-MM
}
```

**返回结构：**
```javascript
{
  "back": 1,
  "code": 200,
  "msg": "查询成功",
  "data": [
    "2024-05-01",
    "2024-05-03",
    "2024-05-15",
    "2024-05-28"
  ]
}
```

**前端接入状态：** ✅ 已接入
- 文件：`pages_accounting/index/components/AccountingMode.vue`
- 方法：`loadMonthDates()`, `onDateChange()`
- 功能：日历组件标记有记录的日期，支持月份切换自动加载
