<template>
	<view class="record-detail-container">
		<view v-if="record" class="detail-content">
			<view class="content-wrapper">
			<!-- 记录头部 -->
			<view class="record-header">
				<view class="record-category">
					<view
						class="category-icon"
						:style="{ backgroundColor: record.categoryColor }"
					>
						{{ record.categoryEmoji }}
					</view>
					<view class="category-info">
						<view class="category-name">{{
							record.categoryName
						}}</view>
						<view class="category-type">{{
							getAccountTypeName(record.type)
						}}</view>
					</view>
				</view>
				<view class="record-amount">
					<view class="amount-value" :class="record.type">
						{{ formatAmountWithSign(record.amount, record.type) }}
					</view>
				</view>
			</view>

			<!-- 记录详情 -->
			<view class="detail-sections">
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="section-title">基本信息</view>
					<view class="detail-item">
						<view class="item-label">
							<text class="label-icon">📅</text>
							<text>记录日期</text>
						</view>
						<view class="item-value">{{
							formatDate(record.date, "date")
						}}</view>
					</view>
					<view class="detail-item">
						<view class="item-label">
							<text class="label-icon">⏰</text>
							<text>创建时间</text>
						</view>
						<view class="item-value">{{
							formatDate(record.timestamp, "datetime")
						}}</view>
					</view>
					<view
						v-if="record.updateTime !== record.timestamp"
						class="detail-item"
					>
						<view class="item-label">
							<text class="label-icon">✏️</text>
							<text>更新时间</text>
						</view>
						<view class="item-value">{{
							formatDate(record.updateTime, "datetime")
						}}</view>
					</view>
				</view>

				<!-- 备注信息 -->
				<view v-if="record.note" class="detail-section">
					<view class="section-title">备注信息</view>
					<view class="note-content">{{ record.note }}</view>
				</view>

				<!-- 标签信息 -->
				<view
					v-if="record.tags && record.tags.length > 0"
					class="detail-section"
				>
					<view class="section-title">标签</view>
					<view class="tags-container">
						<view
							v-for="tag in record.tags"
							:key="tag.id"
							class="tag"
							:style="{
								backgroundColor: `rgba(${hexToRgb(
									tag.color
								)}, 0.1)`,
								color: tag.color,
							}"
						>
							{{ tag.name }}
						</view>
					</view>
				</view>
			</view>
			</view>

		</view>

		<!-- 加载状态 -->
		<view v-else-if="loading" class="loading-state">
			<view class="loading-icon">⏳</view>
			<view class="loading-text">加载中...</view>
		</view>

		<!-- 错误状态 -->
		<view v-else class="empty-state">
			<view class="empty-icon">❌</view>
			<view class="empty-text">记录不存在</view>
			<view class="empty-desc">该记录可能已被删除</view>
			<button class="btn btn-primary" @click="goBack">返回</button>
		</view>

		<!-- 悬浮操作按钮 - 只在有记录时显示 -->
		<view v-if="record" class="floating-actions">
			<button class="action-btn edit-btn" @click="editRecord">
				<text class="btn-icon">✏️</text>
				<text class="btn-text">编辑记录</text>
			</button>
			<button class="action-btn delete-btn" @click="confirmDelete">
				<text class="btn-icon">🗑️</text>
				<text class="btn-text">删除记录</text>
			</button>
		</view>
	</view>
</template>

<script>
import { getAccountTypeConfig } from "../data/categories.js";
import {
	formatAmount,
	formatAmountWithSign,
	formatDate,
} from "../data/utils.js";

export default {
	name: "RecordDetail",
	data() {
		return {
			recordId: "",
			record: null,
			loading: true,
			categoriesData: [], // 从API获取的分类数据
			tagsData: [], // 从API获取的标签数据
		};
	},

	async onLoad(options) {
		if (options.id) {
			this.recordId = options.id;
		} else {
			this.loading = false;
		}
  },
  onShow() {
    this.loadRecord();
  },

	methods: {
		// 加载记录详情
		async loadRecord() {
			this.loading = true;

			try {
				// 调用详情接口获取记录数据
				const _res = await this.$ajax.get("/accounting/record/detail", {
					id: this.recordId,
				});

				if (_res?.code == 200 && _res.data) {
					// 直接使用API返回的数据，不做多余转换
					const apiData = _res.data;
					this.record = {
						id: apiData.id,
						type: this.getTypeString(apiData.type), // 转换数字类型为字符串
						categoryId: apiData.categoryId, // 直接使用API返回的categoryId
						categoryName: apiData.categoryName, // 直接使用API返回的分类名称
						categoryEmoji: apiData.categoryEmoji, // 直接使用API返回的emoji
						categoryColor: apiData.categoryColor, // 直接使用API返回的颜色
						amount: apiData.amount,
						note: apiData.description, // API返回description，页面使用note
						tags: apiData.tags || [], // 直接使用API返回的标签数组
						date: apiData.recordTime * 1000, // 转换时间戳为毫秒
						timestamp: apiData.createTime * 1000, // 创建时间
						updateTime: apiData.updateTime * 1000, // 更新时间
						images: apiData.images || [], // 图片数组
					};
				} else {
					throw new Error(_res?.msg || "记录不存在");
				}

				this.loading = false;
			} catch (error) {
				console.error("加载记录失败:", error);
				this.loading = false;
				this.record = null; // 确保记录为空，显示错误状态
				uni.showToast({
					title: error.message || "加载失败",
					icon: "error",
				});
			}
		},

		// 编辑记录
		editRecord() {
			uni.navigateTo({
				url: `/pages_accounting/add/index?id=${this.recordId}&mode=edit`,
			});
		},

		// 确认删除
		async confirmDelete() {
			console.log("🚀 -> confirmDelete -> confirmDelete:")
			if (await this.$confirm1("确定要删除这条记录吗？") != 1) return;
			try {
				const _res = await this.$ajax.post(
					"/accounting/record/delete",
					{ id: this.recordId }
				);
				if (_res?.code == 200) {
					uni.showToast({
						title: "删除成功",
						icon: "success",
					});

					this.showDeleteDialog = false;

					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					throw new Error(_res?.msg || "删除失败");
				}
			} catch (error) {
				console.error("删除记录失败:", error);
				uni.showToast({
					title: error.message || "删除失败",
					icon: "error",
				});
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 获取账户类型名称
		getAccountTypeName(type) {
			const config = getAccountTypeConfig(type);
			return config.name;
		},

		// 类型转换方法
		getTypeNumber(typeString) {
			const typeMap = {
				expense: 1, // 支出
				income: 2, // 入账
				transfer: 3, // 不计入收入
			};
			return typeMap[typeString] || 1;
		},

		// 数字类型转换为字符串类型
		getTypeString(typeNumber) {
			const typeMap = {
				1: "expense", // 支出
				2: "income", // 入账
				3: "transfer", // 不计入收入
			};
			return typeMap[typeNumber] || "expense";
		},

		// 十六进制颜色转RGB
		hexToRgb(hex) {
			const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(
				hex
			);
			if (result) {
				const r = parseInt(result[1], 16);
				const g = parseInt(result[2], 16);
				const b = parseInt(result[3], 16);
				return `${r}, ${g}, ${b}`;
			}
			return "24, 144, 255";
		},

		// 工具方法
		formatAmount,
		formatAmountWithSign,
		formatDate,
	},
};
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.record-detail-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	position: relative;
}

.detail-content {
	min-height: 100vh;
}

.content-wrapper {
	padding-bottom: 200rpx; /* 为悬浮按钮留出空间 */
}

.record-header {
	background-color: $card-background;
	padding: $spacing-xl;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: $shadow-sm;

	.record-category {
		display: flex;
		align-items: center;
		flex: 1;

		.category-icon {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: $spacing-lg;
			font-size: 48rpx;
			color: white;
		}

		.category-info {
			.category-name {
				font-size: $font-size-xl;
				font-weight: 600;
				color: $text-primary;
				margin-bottom: $spacing-xs;
			}

			.category-type {
				font-size: $font-size-sm;
				color: $text-light;
			}
		}
	}

	.record-amount {
		.amount-value {
			font-size: $font-size-xxl;
			font-weight: 700;
			text-align: right;

			&.income {
				color: $income-color;
			}

			&.expense {
				color: $expense-color;
			}

			&.transfer {
				color: $transfer-color;
			}
		}
	}
}

.detail-sections {
	margin: $spacing-md;
}

.detail-section {
	background-color: $card-background;
	border-radius: $border-radius-lg;
	margin-bottom: $spacing-md;
	box-shadow: $shadow-sm;
	overflow: hidden;

	.section-title {
		padding: $spacing-lg;
		font-size: $font-size-lg;
		font-weight: 600;
		color: $text-primary;
		border-bottom: 1rpx solid $divider-color;
		background-color: #fafbfc;
	}

	.detail-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: $spacing-lg;
		border-bottom: 1rpx solid $divider-color;

		&:last-child {
			border-bottom: none;
		}

		.item-label {
			display: flex;
			align-items: center;
			font-size: $font-size-md;
			color: $text-secondary;

			.label-icon {
				margin-right: $spacing-sm;
				font-size: $font-size-lg;
			}
		}

		.item-value {
			font-size: $font-size-md;
			color: $text-primary;
			font-weight: 500;
		}
	}

	.note-content {
		padding: $spacing-lg;
		font-size: $font-size-md;
		color: $text-primary;
		line-height: 1.6;
		background-color: #f8f9fa;
	}

	.tags-container {
		padding: $spacing-lg;
		display: flex;
		flex-wrap: wrap;
		gap: $spacing-sm;

		.tag {
			padding: $spacing-xs $spacing-sm;
			border-radius: $border-radius-xl;
			font-size: $font-size-sm;
			font-weight: 500;
		}
	}
}

// 悬浮操作按钮
.floating-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	gap: 20rpx;
	padding: 20rpx;
	background: white;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	z-index: 999;

	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 12rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		font-size: 28rpx;
		font-weight: 600;

		&.edit-btn {
			background: #1890ff;
			color: white;
		}

		&.delete-btn {
			background: #ff4d4f;
			color: white;
		}

		.btn-icon {
			font-size: 32rpx;
		}

		.btn-text {
			font-size: 28rpx;
		}
	}
}

// 弹窗样式
.delete-dialog {
	background-color: $card-background;
	border-radius: $border-radius-lg;
	width: 600rpx;
	max-width: 90vw;

	.dialog-header {
		padding: $spacing-lg;
		border-bottom: 1rpx solid $divider-color;

		.dialog-title {
			font-size: $font-size-lg;
			font-weight: 600;
			color: $text-primary;
			text-align: center;
		}
	}

	.dialog-body {
		padding: $spacing-lg;
	}

	.dialog-actions {
		display: flex;
		gap: $spacing-md;
		padding: $spacing-lg;
		border-top: 1rpx solid $divider-color;

		.btn {
			flex: 1;
		}
	}
}

.delete-warning {
	text-align: center;

	.warning-icon {
		font-size: 120rpx;
		margin-bottom: $spacing-md;
	}

	.warning-text {
		font-size: $font-size-md;
		color: $text-primary;
		margin-bottom: $spacing-sm;
	}

	.warning-desc {
		font-size: $font-size-sm;
		color: $text-light;
		line-height: 1.5;
	}
}
</style>
