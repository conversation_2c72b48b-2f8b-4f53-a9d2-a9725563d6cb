需要你设计一个基于uniapp+vue2的记账软件
首页顶部是2个切换   分别是会话模式和记账模式 两个图标 点击 首页可以切换界面 会话界面和记账首页界面 都是在pages_accounting/index/index内的组件  。   会话界面暂时留空   记账首页  就是最近的记账记录  顶部1个日历  下面是消费列表  右下角是创建记账信息

新建记账页面  单条账单 应该分支出、入账和不计入收入3大类。 每类下面 有如下的小类。
  支出: 餐饮 交通 服饰 购物 服务 教育 娱乐 运动 生活缴费 旅行 宠物 医疗
  入账: 生意 工资 奖金 其他人情 收红包 收转账 商家转账 退款 其他
  不计入收入: 理财 借还款 其他
  支出 入账 不计入收入

账单还可以添加tag  tag也是可以人为的设置和编辑的  tag也需要区分分组。 比如是支出下的  还是入账下的 或者不计入收入下的。  每条账单均可以添加多个tag

账单有统计界面
默认是本月（当前1号到月末）  支出、收入、结余。
再来个 柱状图  3柱 对应这支出、收入、不计入。  可以手动选择时间范围。 默认是当月  有额外几个按钮（今天、本周、本月、本季度、本年、手动选择）

我的设计  可能存在缺陷 你要大胆指正。  现在  帮我设计这一整个功能模块  记得 全部放在 都是在pages_accounting下。 记得在pages.json内分包注册！

