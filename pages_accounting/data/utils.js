/**
 * @Description: 记账系统工具函数
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
 */

/**
 * 格式化金额显示
 * @param {number} amount - 金额（分为单位）
 * @param {boolean} showSymbol - 是否显示符号
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount(amount, showSymbol = true) {
  const yuan = (amount / 100).toFixed(2)
  return showSymbol ? `¥${yuan}` : yuan
}

/**
 * 格式化金额显示（带正负号）
 * @param {number} amount - 金额（分为单位）
 * @param {string} type - 账户类型
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmountWithSign(amount, type) {
  const yuan = (amount / 1).toFixed(2)
  const prefix = type === 'expense' ? '-' : '+'
  return `${prefix}¥${yuan}`
}

/**
 * 解析金额输入
 * @param {string} input - 输入的金额字符串
 * @returns {number} 解析后的金额（分为单位）
 */
export function parseAmount(input) {
  const cleaned = input.replace(/[^\d.]/g, '')
  const amount = parseFloat(cleaned) || 0
  return Math.round(amount * 100)
}

/**
 * 格式化日期显示
 * @param {string|number} date - 日期字符串或时间戳
 * @param {string} format - 格式类型：'date'|'datetime'|'time'|'relative'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'date') {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date)

  if (isNaN(dateObj.getTime())) {
    return '无效日期'
  }

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const targetDate = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate())

  switch (format) {
    case 'date':
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '-')

    case 'datetime':
      return dateObj.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })

    case 'time':
      return dateObj.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })

    case 'relative':
      const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === -1) {
        return '昨天'
      } else if (diffDays === 1) {
        return '明天'
      } else if (diffDays > 1 && diffDays <= 7) {
        return `${diffDays}天后`
      } else if (diffDays < -1 && diffDays >= -7) {
        return `${Math.abs(diffDays)}天前`
      } else {
        return formatDate(date, 'date')
      }

    default:
      return formatDate(date, 'date')
  }
}

/**
 * 获取日期范围
 * @param {string} type - 范围类型：'today'|'week'|'month'|'quarter'|'year'
 * @returns {object} {startDate, endDate}
 */
export function getDateRange(type) {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()
  const date = now.getDate()

  let startDate, endDate

  switch (type) {
    case 'today':
      startDate = new Date(year, month, date)
      endDate = new Date(year, month, date, 23, 59, 59)
      break

    case 'week':
      const dayOfWeek = now.getDay()
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek
      startDate = new Date(year, month, date + mondayOffset)
      endDate = new Date(year, month, date + mondayOffset + 6, 23, 59, 59)
      break

    case 'month':
      startDate = new Date(year, month, 1)
      endDate = new Date(year, month + 1, 0, 23, 59, 59)
      break

    case 'quarter':
      const quarterStartMonth = Math.floor(month / 3) * 3
      startDate = new Date(year, quarterStartMonth, 1)
      endDate = new Date(year, quarterStartMonth + 3, 0, 23, 59, 59)
      break

    case 'year':
      startDate = new Date(year, 0, 1)
      endDate = new Date(year, 11, 31, 23, 59, 59)
      break

    default:
      startDate = new Date(year, month, 1)
      endDate = new Date(year, month + 1, 0, 23, 59, 59)
  }

  return {
    startDate: formatDate(startDate, 'date'),
    endDate: formatDate(endDate, 'date')
  }
}

/**
 * 获取月份名称
 * @param {number} month - 月份（0-11）
 * @returns {string} 月份名称
 */
export function getMonthName(month) {
  const months = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ]
  return months[month] || '未知月份'
}

/**
 * 获取星期名称
 * @param {number} day - 星期（0-6）
 * @returns {string} 星期名称
 */
export function getDayName(day) {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return days[day] || '未知'
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

/**
 * 生成随机颜色
 * @returns {string} 十六进制颜色值
 */
export function generateRandomColor() {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

/**
 * 验证金额格式
 * @param {string} amount - 金额字符串
 * @returns {boolean} 是否有效
 */
export function validateAmount(amount) {
  const regex = /^\d+(\.\d{1,2})?$/
  return regex.test(amount) && parseFloat(amount) > 0
}

/**
 * 获取时间问候语
 * @returns {string} 问候语
 */
export function getGreeting() {
  const hour = new Date().getHours()

  if (hour < 6) {
    return '夜深了'
  } else if (hour < 9) {
    return '早上好'
  } else if (hour < 12) {
    return '上午好'
  } else if (hour < 14) {
    return '中午好'
  } else if (hour < 18) {
    return '下午好'
  } else if (hour < 22) {
    return '晚上好'
  } else {
    return '夜深了'
  }
}

/**
 * 计算两个日期之间的天数
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {number} 天数差
 */
export function getDaysBetween(startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end - start)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}
