/**
 * @Description: 记账分类数据配置
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
 */

// 记账类型枚举
export const ACCOUNT_TYPES = {
  EXPENSE: 'expense',    // 支出
  INCOME: 'income',      // 入账
  TRANSFER: 'transfer'   // 不计入收入
}

// 注意：所有分类数据现在都从API获取，不再使用硬编码数据

// 账户类型显示配置
export const ACCOUNT_TYPE_CONFIG = {
  [ACCOUNT_TYPES.EXPENSE]: {
    name: '支出',
    color: '#ff4d4f',
    icon: '💸'
  },
  [ACCOUNT_TYPES.INCOME]: {
    name: '入账',
    color: '#52c41a',
    icon: '💰'
  },
  [ACCOUNT_TYPES.TRANSFER]: {
    name: '不计入收入',
    color: '#faad14',
    icon: '🔄'
  }
}

// 获取账户类型配置
export function getAccountTypeConfig(type) {
  return ACCOUNT_TYPE_CONFIG[type] || {
    name: '未知',
    color: '#8c8c8c',
    icon: '❓'
  }
}
