/**
 * @Description: 记账数据存储管理
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
 */

import { ACCOUNT_TYPES } from './categories.js'

// 存储键名常量
const STORAGE_KEYS = {
  RECORDS: 'accounting_records',
  TAGS: 'accounting_tags',
  SETTINGS: 'accounting_settings'
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取当前时间戳
function getCurrentTimestamp() {
  return new Date().getTime()
}

// 格式化日期为YYYY-MM-DD
function formatDate(timestamp) {
  const date = new Date(timestamp)
  return date.toISOString().split('T')[0]
}

/**
 * 记账记录数据结构
 * {
 *   id: string,              // 唯一标识
 *   type: string,            // 账户类型：expense/income/transfer
 *   categoryId: string,      // 分类ID
 *   amount: number,          // 金额（分为单位）
 *   note: string,            // 备注
 *   tags: string[],          // 标签ID数组
 *   date: string,            // 日期 YYYY-MM-DD
 *   timestamp: number,       // 创建时间戳
 *   updateTime: number       // 更新时间戳
 * }
 */

/**
 * 标签数据结构
 * {
 *   id: string,              // 唯一标识
 *   name: string,            // 标签名称
 *   color: string,           // 标签颜色
 *   type: string,            // 所属类型：expense/income/transfer
 *   createTime: number,      // 创建时间戳
 *   updateTime: number       // 更新时间戳
 * }
 */

class AccountingStorage {
  constructor() {
    this.initDefaultData()
  }

  // 初始化默认数据
  initDefaultData() {
    // 初始化默认标签
    const defaultTags = [
      { name: '日常', color: '#1890ff', type: ACCOUNT_TYPES.EXPENSE },
      { name: '必需', color: '#52c41a', type: ACCOUNT_TYPES.EXPENSE },
      { name: '奢侈', color: '#faad14', type: ACCOUNT_TYPES.EXPENSE },
      { name: '主要收入', color: '#52c41a', type: ACCOUNT_TYPES.INCOME },
      { name: '额外收入', color: '#1890ff', type: ACCOUNT_TYPES.INCOME },
      { name: '投资理财', color: '#722ed1', type: ACCOUNT_TYPES.TRANSFER }
    ]

    if (!this.getTags().length) {
      defaultTags.forEach(tag => this.addTag(tag))
    }
  }

  // 记录相关方法
  
  // 获取所有记录
  getRecords() {
    try {
      const records = uni.getStorageSync(STORAGE_KEYS.RECORDS) || []
      return records.sort((a, b) => b.timestamp - a.timestamp)
    } catch (error) {
      console.error('获取记录失败:', error)
      return []
    }
  }

  // 添加记录
  addRecord(recordData) {
    try {
      const records = this.getRecords()
      const newRecord = {
        id: generateId(),
        type: recordData.type,
        categoryId: recordData.categoryId,
        amount: Math.round(recordData.amount * 100), // 转换为分
        note: recordData.note || '',
        tags: recordData.tags || [],
        date: recordData.date || formatDate(Date.now()),
        timestamp: getCurrentTimestamp(),
        updateTime: getCurrentTimestamp()
      }
      
      records.unshift(newRecord)
      uni.setStorageSync(STORAGE_KEYS.RECORDS, records)
      return newRecord
    } catch (error) {
      console.error('添加记录失败:', error)
      throw error
    }
  }

  // 更新记录
  updateRecord(recordId, updateData) {
    try {
      const records = this.getRecords()
      const index = records.findIndex(record => record.id === recordId)
      
      if (index === -1) {
        throw new Error('记录不存在')
      }

      records[index] = {
        ...records[index],
        ...updateData,
        updateTime: getCurrentTimestamp()
      }

      if (updateData.amount !== undefined) {
        records[index].amount = Math.round(updateData.amount * 100)
      }

      uni.setStorageSync(STORAGE_KEYS.RECORDS, records)
      return records[index]
    } catch (error) {
      console.error('更新记录失败:', error)
      throw error
    }
  }

  // 删除记录
  deleteRecord(recordId) {
    try {
      const records = this.getRecords()
      const filteredRecords = records.filter(record => record.id !== recordId)
      uni.setStorageSync(STORAGE_KEYS.RECORDS, filteredRecords)
      return true
    } catch (error) {
      console.error('删除记录失败:', error)
      throw error
    }
  }

  // 根据ID获取记录
  getRecordById(recordId) {
    const records = this.getRecords()
    return records.find(record => record.id === recordId)
  }

  // 根据日期范围获取记录
  getRecordsByDateRange(startDate, endDate) {
    const records = this.getRecords()
    return records.filter(record => {
      return record.date >= startDate && record.date <= endDate
    })
  }

  // 根据类型获取记录
  getRecordsByType(type) {
    const records = this.getRecords()
    return records.filter(record => record.type === type)
  }

  // 标签相关方法

  // 获取所有标签
  getTags() {
    try {
      return uni.getStorageSync(STORAGE_KEYS.TAGS) || []
    } catch (error) {
      console.error('获取标签失败:', error)
      return []
    }
  }

  // 添加标签
  addTag(tagData) {
    try {
      const tags = this.getTags()
      const newTag = {
        id: generateId(),
        name: tagData.name,
        color: tagData.color || '#1890ff',
        type: tagData.type,
        createTime: getCurrentTimestamp(),
        updateTime: getCurrentTimestamp()
      }
      
      tags.push(newTag)
      uni.setStorageSync(STORAGE_KEYS.TAGS, tags)
      return newTag
    } catch (error) {
      console.error('添加标签失败:', error)
      throw error
    }
  }

  // 更新标签
  updateTag(tagId, updateData) {
    try {
      const tags = this.getTags()
      const index = tags.findIndex(tag => tag.id === tagId)
      
      if (index === -1) {
        throw new Error('标签不存在')
      }

      tags[index] = {
        ...tags[index],
        ...updateData,
        updateTime: getCurrentTimestamp()
      }

      uni.setStorageSync(STORAGE_KEYS.TAGS, tags)
      return tags[index]
    } catch (error) {
      console.error('更新标签失败:', error)
      throw error
    }
  }

  // 删除标签
  deleteTag(tagId) {
    try {
      const tags = this.getTags()
      const filteredTags = tags.filter(tag => tag.id !== tagId)
      uni.setStorageSync(STORAGE_KEYS.TAGS, filteredTags)
      
      // 同时从所有记录中移除该标签
      const records = this.getRecords()
      const updatedRecords = records.map(record => ({
        ...record,
        tags: record.tags.filter(id => id !== tagId)
      }))
      uni.setStorageSync(STORAGE_KEYS.RECORDS, updatedRecords)
      
      return true
    } catch (error) {
      console.error('删除标签失败:', error)
      throw error
    }
  }

  // 根据类型获取标签
  getTagsByType(type) {
    const tags = this.getTags()
    return tags.filter(tag => tag.type === type)
  }

  // 根据ID获取标签
  getTagById(tagId) {
    const tags = this.getTags()
    return tags.find(tag => tag.id === tagId)
  }

  // 统计相关方法

  // 获取指定日期范围的统计数据
  getStatistics(startDate, endDate) {
    const records = this.getRecordsByDateRange(startDate, endDate)
    
    let totalIncome = 0
    let totalExpense = 0
    let totalTransfer = 0

    records.forEach(record => {
      const amount = record.amount / 100 // 转换为元
      
      switch (record.type) {
        case ACCOUNT_TYPES.INCOME:
          totalIncome += amount
          break
        case ACCOUNT_TYPES.EXPENSE:
          totalExpense += amount
          break
        case ACCOUNT_TYPES.TRANSFER:
          totalTransfer += amount
          break
      }
    })

    return {
      income: totalIncome,
      expense: totalExpense,
      transfer: totalTransfer,
      balance: totalIncome - totalExpense,
      recordCount: records.length
    }
  }

  // 清空所有数据
  clearAllData() {
    try {
      uni.removeStorageSync(STORAGE_KEYS.RECORDS)
      uni.removeStorageSync(STORAGE_KEYS.TAGS)
      uni.removeStorageSync(STORAGE_KEYS.SETTINGS)
      this.initDefaultData()
      return true
    } catch (error) {
      console.error('清空数据失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export default new AccountingStorage()
