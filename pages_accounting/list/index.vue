<template>
  <view class="list-page">
    <!-- 筛选区域 -->
    <view class="filter-container">
      <!-- 类型筛选 -->
      <view class="type-filter-section">
        <view class="filter-tabs">
          <view
            class="filter-tab"
            :class="{ active: currentType === 'all' }"
            @click="setTypeFilter('all')"
          >
            <text class="tab-text">全部</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: currentType === 'income' }"
            @click="setTypeFilter('income')"
          >
            <text class="tab-text">收入</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: currentType === 'expense' }"
            @click="setTypeFilter('expense')"
          >
            <text class="tab-text">支出</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选 -->
      <view class="time-filter-section">
        <view class="time-tabs">
          <view
            class="time-tab"
            :class="{ active: currentTimeRange === 'all' }"
            @click="setTimeRange('all')"
          >
            <text class="time-text">全部</text>
          </view>
          <view
            class="time-tab"
            :class="{ active: currentTimeRange === 'week' }"
            @click="setTimeRange('week')"
          >
            <text class="time-text">最近1周</text>
          </view>
          <view
            class="time-tab"
            :class="{ active: currentTimeRange === 'month' }"
            @click="setTimeRange('month')"
          >
            <text class="time-text">最近1月</text>
          </view>
          <!-- <view
            class="time-tab"
            :class="{ active: currentTimeRange === 'custom' }"
            @click="showCalendar = true"
          >
            <text class="time-text">自定义</text>
          </view> -->
        </view>

        <!-- 显示选中的日期范围 -->
        <view v-if="startDate && endDate" class="selected-date-range">
          <text class="date-range-text">{{ startDate }} 至 {{ endDate }}</text>
        </view>
      </view>

      <!-- 标签筛选 -->
      <view class="tags-section" v-if="availableTags.length > 0">
        <view class="section-header">
          <text class="section-title">标签筛选</text>
          <view class="clear-all-btn" @click="clearAllFilters">
            <text class="clear-icon">🗑️</text>
            <text class="clear-all-text">清空</text>
          </view>
        </view>
        <view class="tags-container">
          <view
            v-for="tag in availableTags"
            :key="tag.id"
            class="tag-chip"
            :class="{ selected: selectedTags.includes(tag.id) }"
            :style="getTagChipStyle(tag)"
            @click="toggleTag(tag.id)"
          >
            <text class="tag-name" :style="{ color: selectedTags.includes(tag.id) ? '#ffffff' : tag.color }">{{ tag.name }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="records-container">
      <view v-if="records.length > 0" class="records-list">
        <view
          v-for="record in records"
          :key="record.id"
          class="record-card"
          @click="goToRecordDetail(record)"
        >
          <!-- 左侧图标 -->
          <view class="record-icon">
            <view class="icon-circle" :style="{ backgroundColor: record.categoryColor }">
              <text class="icon-emoji">{{ record.categoryEmoji }}</text>
            </view>
          </view>

          <!-- 中间内容 -->
          <view class="record-info">
            <view class="info-header">
              <text class="category-name">{{ record.categoryName }}</text>
            </view>
            <view class="record-tags" v-if="record.tags && record.tags.length > 0">
              <view
                v-for="tag in record.tags"
                :key="tag.id"
                class="mini-tag"
                :style="{ backgroundColor: tag.color + '15', borderColor: tag.color + '40' }"
              >
                <text class="mini-tag-text" :style="{ color: tag.color }">{{ tag.name }}</text>
              </view>
            </view>
            <view class="info-details">
              <text class="record-time">{{ formatRecordTime(record.timestamp) }}</text>
              <text class="record-note" v-if="record.note">{{ record.note }}</text>
            </view>
          </view>

          <!-- 右侧金额 -->
          <view class="record-amount" :class="record.type">
            <text class="amount-text">
              {{ record.type === 'expense' ? '-' : record.type === 'income' ? '+' : '' }}￥{{ formatAmountNumber(record.amount, record.type) }}
            </text>
          </view>
        </view>

        <!-- 加载中 -->
        <view v-if="isLoading" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多 -->
        <view v-if="!hasMoreData && records.length > 0" class="no-more-state">
          <text class="no-more-text">没有更多记录了</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!isLoading" class="empty-container">
        <view class="empty-icon">📋</view>
        <view class="empty-title">暂无记录</view>
        <view class="empty-desc">调整筛选条件或添加新记录</view>
      </view>
    </view>

    <!-- 日期范围选择 -->
    <u-calendar
      :show="showCalendar"
      mode="range"
      :max-date="todayDate"
      :min-date="minDate"
      :default-date="defaultRange"
      :month-num="60"
      :allow-same-day="true"
      @confirm="confirmDateRange"
      @close="showCalendar = false"
    ></u-calendar>
  </view>
</template>

<script>
import { formatDate } from '../data/utils.js'

export default {
  name: 'AccountingList',
  data() {
    return {
      // 筛选条件
      currentType: 'all',
      currentTimeRange: 'all',
      selectedTags: [],
      startDate: '',
      endDate: '',

      // 数据
      records: [],
      availableTags: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      hasMoreData: true,
      isLoading: false,

      // UI状态
      showCalendar: false
    }
  },

  computed: {
    // 今天的日期，用于日历最大日期限制
    todayDate() {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 最小日期，设置为5年前
    minDate() {
      const fiveYearsAgo = new Date()
      fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5)
      const year = fiveYearsAgo.getFullYear()
      const month = String(fiveYearsAgo.getMonth() + 1).padStart(2, '0')
      const day = String(fiveYearsAgo.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 默认日期范围，用于日历双向绑定
    defaultRange() {
      if (this.currentTimeRange === 'custom' && this.startDate && this.endDate) {
        // 如果是自定义时间范围且已选择日期，返回选中的日期范围
        return [this.startDate, this.endDate]
      }
      // 默认返回今天往前7天的时间范围，让日历定位到当前时间附近
      const today = new Date()
      const sevenDaysAgo = new Date(today)
      sevenDaysAgo.setDate(today.getDate() - 7)

      const formatDate = (date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }

      return [formatDate(sevenDaysAgo), formatDate(today)]
    }
  },

  onLoad() {
    this.loadAvailableTags()
  },
  onShow() {
    this.loadRecords()

  },

  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },

  onReachBottom() {
    if (!this.hasMoreData || this.isLoading) return
    this.currentPage++
    this.loadRecords()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 设置类型筛选
    setTypeFilter(type) {
      this.currentType = type
      this.refreshData()
    },

    // 设置时间范围
    setTimeRange(range) {
      this.currentTimeRange = range
      if (range !== 'custom') {
        // 切换到非自定义时间范围时，清空自定义日期设置
        this.startDate = ''
        this.endDate = ''
      }
      this.refreshData()
    },

    // 切换标签选择
    toggleTag(tagId) {
      console.log('toggleTag called with tagId:', tagId, 'type:', typeof tagId)
      const index = this.selectedTags.indexOf(tagId)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tagId)
      }
      console.log('selectedTags after toggle:', this.selectedTags)
      this.refreshData()
    },

    // 清空所有筛选条件
    clearAllFilters() {
      this.selectedTags = []
      this.startDate = ''
      this.endDate = ''
      this.currentTimeRange = 'all'
      this.refreshData()
    },

    // 刷新数据
    refreshData() {
      this.currentPage = 1
      this.hasMoreData = true
      this.loadRecords()
    },



    // 获取标签样式
    getTagChipStyle(tag) {
      const isSelected = this.selectedTags.includes(tag.id)
      return {
        backgroundColor: isSelected ? tag.color : tag.color + '15',
        borderColor: tag.color + (isSelected ? '' : '40')
      }
    },

    // 格式化记录时间
    formatRecordTime(timestamp) {
      return formatDate(timestamp, 'MM-dd HH:mm')
    },

    // 格式化金额数字
    formatAmountNumber(amount, type) {
      return parseFloat(amount).toFixed(2)
    },

    // 跳转到记录详情
    goToRecordDetail(record) {
      uni.navigateTo({
        url: `/pages_accounting/record-detail/index?id=${record.id}`
      })
    },

    // 加载记录数据
    async loadRecords() {
      if (this.isLoading) return

      this.isLoading = true
      try {
        const response = await this.fetchRecords(this.currentPage, this.pageSize)

        if (this.currentPage === 1) {
          this.records = response.data
        } else {
          this.records.push(...response.data)
        }

        this.hasMoreData = response.hasMore
      } catch (error) {
        console.error('加载记录失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },



    // 加载可用标签
    async loadAvailableTags() {
      try {
        const response = await this.fetchTags()
        this.availableTags = response.data
      } catch (error) {
        console.error('加载标签失败:', error)
      }
    },

    // 获取记录数据
    async fetchRecords(page, size) {
      try {
        // 构建请求参数
        const params = {
          page: page,
          pageSize: size
        }

        // 添加类型筛选
        if (this.currentType !== 'all') {
          params.type = this.currentType
        }

        // 添加标签筛选
        if (this.selectedTags.length > 0) {
          // 将选中的标签ID转换为标签名称数组
          const selectedTagNames = this.selectedTags.map(tagId => {
            const tag = this.availableTags.find(t => t.id === tagId)
            return tag ? tag.name : ''
          }).filter(name => name !== '')

          params.tags = selectedTagNames  // 提交标签名称数组
          console.log('API request params.tags:', params.tags, 'type:', typeof params.tags, 'isArray:', Array.isArray(params.tags))
        }

        // 添加时间范围筛选
        if (this.currentTimeRange !== 'all') {
          const now = Date.now()
          let startTime = 0
          let endTime = now

          if (this.currentTimeRange === 'week') {
            startTime = now - (7 * 24 * 60 * 60 * 1000)
          } else if (this.currentTimeRange === 'month') {
            startTime = now - (30 * 24 * 60 * 60 * 1000)
          } else if (this.currentTimeRange === 'custom' && this.startDate && this.endDate) {
            startTime = new Date(this.startDate).getTime()
            endTime = new Date(this.endDate).getTime() + (24 * 60 * 60 * 1000) - 1
          }

          if (startTime > 0) {
            params.startTime = Math.floor(startTime / 1000)
            params.endTime = Math.floor(endTime / 1000)
          }
        }

        // 调用API - 使用POST方法提交数组参数
        const response = await this.$ajax.post('/accounting/records', params)

        if (response.back === 1) {
          // 转换数据格式
          const records = response.rows.map(record => ({
            id: record.id,
            type: record.type === 1 ? 'expense' : record.type === 2 ? 'income' : 'transfer',
            amount: record.amount,
            categoryName: record.categoryName,
            categoryEmoji: record.categoryEmoji,
            categoryColor: record.categoryColor || '#999999',
            note: record.description || '',
            timestamp: record.recordTime * 1000,
            tags: record.tags || []
          }))

          return {
            data: records,
            hasMore: response.total > page * size
          }
        } else {
          throw new Error(response.msg || '获取记录失败')
        }
      } catch (error) {
        console.error('获取记录失败:', error)
        throw error
      }
    },

    // 获取标签数据
    async fetchTags() {
      try {
        const response = await this.$ajax.get('/accounting/tags')

        if (response.back === 1) {
          return {
            data: response.data
          }
        } else {
          throw new Error(response.msg || '获取标签失败')
        }
      } catch (error) {
        console.error('获取标签失败:', error)
        throw error
      }
    },

    // 确认日期范围
    confirmDateRange(dates) {
      console.log('confirmDateRange called with:', dates)

      // 处理不同格式的日期数据
      let startDate = ''
      let endDate = ''

      if (dates) {
        if (Array.isArray(dates) && dates.length >= 1) {
          // 数组格式：取第一个和最后一个日期
          startDate = dates[0]
          endDate = dates[dates.length - 1]
        } else if (dates.startDate && dates.endDate) {
          // 对象格式：{startDate: '', endDate: ''}
          startDate = dates.startDate
          endDate = dates.endDate
        } else if (typeof dates === 'string') {
          // 单个日期字符串，设为开始和结束日期
          startDate = endDate = dates
        }

        if (startDate && endDate) {
          this.startDate = startDate
          this.endDate = endDate
          this.currentTimeRange = 'custom'
          this.showCalendar = false
          this.refreshData()

          console.log('Date range set:', { startDate, endDate })
        } else {
          console.warn('Invalid date format:', dates)
          uni.showToast({
            title: '日期格式错误',
            icon: 'none'
          })
        }
      } else {
        console.warn('No dates provided')
      }
    }
  }
}
</script>

<style scoped>
.list-page {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
}

.navbar-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 20px;
  color: #333333;
  font-weight: 500;
}

.navbar-title {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.navbar-right {
  width: 44px;
}

/* 筛选区域 */
.filter-container {
  background-color: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px 16px;
  flex-shrink: 0;
}

.type-filter-section {
  margin-bottom: 16px;
}

.filter-tabs {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 4px;
}

.filter-tab {
  flex: 1;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: #007AFF;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #666666;
}

.filter-tab.active .tab-text {
  color: #ffffff;
}

.time-filter-section {
  margin-bottom: 16px;
}

.time-tabs {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-tab {
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.time-tab.active {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.time-text {
  font-size: 13px;
  color: #666666;
}

.time-tab.active .time-text {
  color: #ffffff;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border-radius: 12px;
  background-color: transparent;
  transition: all 0.3s ease;
}

.clear-all-btn:active {
  background-color: #f5f5f5;
}

.clear-icon {
  font-size: 14px;
  opacity: 0.7;
}

.clear-all-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

.selected-date-range {
  margin-top: 8px;
  padding: 6px 12px;
  background-color: #e3f2fd;
  border-radius: 8px;
}

.date-range-text {
  font-size: 12px;
  color: #1976d2;
}

/* 标签筛选 */
.tags-section {
  margin-top: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}



.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-chip {
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.tag-chip.selected {
  transform: scale(1.05);
}

.tag-name {
  font-size: 12px;
  font-weight: 500;
}

/* 记录列表 */
.records-container {
  flex: 1;
  padding: 16px;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-card {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.record-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
}

.record-icon {
  margin-right: 12px;
}

.icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-emoji {
  font-size: 24px;
}

.record-info {
  flex: 1;
  min-width: 0;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.record-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 4px;
}

.mini-tag {
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid;
}

.mini-tag-text {
  font-size: 10px;
  font-weight: 500;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.record-time {
  font-size: 12px;
  color: #999999;
}

.record-note {
  font-size: 13px;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.record-amount {
  margin-left: 12px;
}

.amount-text {
  font-size: 18px;
  font-weight: 700;
}

.record-amount.expense .amount-text {
  color: #FF5722;
}

.record-amount.income .amount-text {
  color: #4CAF50;
}

.record-amount.transfer .amount-text {
  color: #2196F3;
}

/* 加载状态 */

.loading-state, .no-more-state {
  margin-top: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text, .no-more-text {
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  height: 100%;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
  text-align: center;
}



/* 响应式适配 */
@media screen and (max-width: 375px) {
  .filter-container {
    padding: 12px;
  }

  .records-container {
    padding: 12px;
  }

  .record-card {
    padding: 12px;
  }


}
</style>
