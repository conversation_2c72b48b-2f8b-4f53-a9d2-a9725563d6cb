<template>
  <view class="category-manage-container">
    <!-- 类型切换标签 -->
    <view class="type-tabs">
      <view
        v-for="(config, type) in accountTypeConfigs"
        :key="type"
        class="type-tab"
        :class="{ active: currentType === type }"
        @click="switchType(type)"
      >
        <text class="tab-icon">{{ config.icon }}</text>
        <text class="tab-text">{{ config.name }}</text>
      </view>
    </view>

    <!-- 分类列表 -->
    <view class="category-list">
      <view class="list-header">
        <view class="header-title">{{ currentTypeConfig.name }}分类</view>
        <view class="header-desc">系统预设分类，暂不支持自定义</view>
      </view>

      <view class="category-grid">
        <view
          v-for="category in currentCategories"
          :key="category.id"
          class="category-item"
        >
          <view
            class="category-icon"
            :style="{ backgroundColor: category.color }"
          >
            {{ category.icon }}
          </view>
          <view class="category-name">{{ category.name }}</view>
          <view class="category-stats">
            {{ getCategoryStats(category.id) }}笔记录
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-header">
          <view class="stats-title">分类统计</view>
        </view>
        <view class="stats-body">
          <view class="stats-item">
            <view class="stats-label">总分类数</view>
            <view class="stats-value">{{ currentCategories.length }}个</view>
          </view>
          <view class="stats-item">
            <view class="stats-label">已使用分类</view>
            <view class="stats-value">{{ getUsedCategoriesCount() }}个</view>
          </view>
          <view class="stats-item">
            <view class="stats-label">总记录数</view>
            <view class="stats-value">{{ getTotalRecordsCount() }}笔</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="help-section">
      <view class="help-card">
        <view class="help-header">
          <view class="help-title">
            <text class="help-icon">💡</text>
            <text>使用说明</text>
          </view>
        </view>
        <view class="help-body">
          <view class="help-item">
            <text class="help-bullet">•</text>
            <text class="help-text">系统提供了丰富的预设分类，覆盖日常记账需求</text>
          </view>
          <view class="help-item">
            <text class="help-bullet">•</text>
            <text class="help-text">每个分类都有独特的图标和颜色，便于快速识别</text>
          </view>
          <view class="help-item">
            <text class="help-bullet">•</text>
            <text class="help-text">可以通过标签功能对记录进行更细致的分类</text>
          </view>
          <view class="help-item">
            <text class="help-bullet">•</text>
            <text class="help-text">未来版本将支持自定义分类功能</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import storage from '../data/storage.js' // 暂时保留，用于统计记录数据
import { ACCOUNT_TYPES, ACCOUNT_TYPE_CONFIG } from '../data/categories.js'

export default {
  name: 'CategoryManage',
  data() {
    return {
      currentType: ACCOUNT_TYPES.EXPENSE,
      categoryStats: new Map(),
      apiCategories: [] // 从API获取的分类数据
    }
  },

  computed: {
    accountTypeConfigs() {
      return ACCOUNT_TYPE_CONFIG
    },

    currentTypeConfig() {
      return ACCOUNT_TYPE_CONFIG[this.currentType]
    },

    currentCategories() {
      // 完全使用API获取的分类数据
      return this.apiCategories.filter(cat => cat.type === this.getTypeNumber(this.currentType)) || []
    }
  },

    async onLoad() {
    await this.loadCategories()
    this.loadCategoryStats()
  },

  async onShow() {
    await this.loadCategories()
    this.loadCategoryStats()
  },

  methods: {
    // 加载分类数据
    async loadCategories() {
      try {
        const _res = await this.$ajax.get('/accounting/categories')
        if (_res?.code == 200) {
          this.apiCategories = _res.data || []
        }
      } catch (error) {
        console.error('加载分类失败:', error)
        // 加载失败时使用本地配置数据
        this.apiCategories = []
      }
    },

    // 类型转换方法
    getTypeNumber(typeString) {
      const typeMap = {
        'expense': 1,   // 支出
        'income': 2,    // 入账
        'transfer': 3   // 不计入收入
      }
      return typeMap[typeString] || 1
    },

    // 切换类型
    async switchType(type) {
      this.currentType = type
      this.loadCategoryStats()
    },

    // 加载分类统计
    loadCategoryStats() {
      const records = storage.getRecordsByType(this.currentType)
      const statsMap = new Map()

      // 初始化所有分类的统计为0
      this.currentCategories.forEach(category => {
        statsMap.set(category.id, 0)
      })

      // 统计每个分类的记录数
      records.forEach(record => {
        const count = statsMap.get(record.categoryId) || 0
        statsMap.set(record.categoryId, count + 1)
      })

      this.categoryStats = statsMap
    },

    // 获取分类统计
    getCategoryStats(categoryId) {
      return this.categoryStats.get(categoryId) || 0
    },

    // 获取已使用的分类数量
    getUsedCategoriesCount() {
      let count = 0
      this.categoryStats.forEach(value => {
        if (value > 0) count++
      })
      return count
    },

    // 获取总记录数
    getTotalRecordsCount() {
      let total = 0
      this.categoryStats.forEach(value => {
        total += value
      })
      return total
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.category-manage-container {
  background-color: $background-color;
  min-height: 100vh;
  padding-bottom: $spacing-lg;
}

.type-tabs {
  background-color: $card-background;
  display: flex;
  box-shadow: $shadow-nav;

  .type-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-md;
    transition: all 0.2s ease;

    .tab-icon {
      font-size: 36rpx;
      margin-bottom: $spacing-xs;
    }

    .tab-text {
      font-size: $font-size-sm;
      color: $text-secondary;
    }

    &.active {
      background-color: rgba($primary-color, 0.1);

      .tab-icon,
      .tab-text {
        color: $primary-color;
      }
    }
  }
}

.category-list {
  margin: $spacing-md;

  .list-header {
    background-color: $card-background;
    padding: $spacing-lg;
    border-radius: $border-radius-lg $border-radius-lg 0 0;
    box-shadow: $shadow-sm;

    .header-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }

    .header-desc {
      font-size: $font-size-sm;
      color: $text-light;
    }
  }

  .category-grid {
    background-color: $card-background;
    border-radius: 0 0 $border-radius-lg $border-radius-lg;
    box-shadow: $shadow-sm;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rpx;
    background-color: $divider-color;

    .category-item {
      background-color: $card-background;
      padding: $spacing-lg;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .category-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: $spacing-sm;
        font-size: 36rpx;
        color: white;
      }

      .category-name {
        font-size: $font-size-sm;
        color: $text-primary;
        font-weight: 500;
        margin-bottom: $spacing-xs;
      }

      .category-stats {
        font-size: $font-size-xs;
        color: $text-light;
      }
    }
  }
}

.stats-section {
  margin: $spacing-md;

  .stats-card {
    @extend .card;

    .stats-header {
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      .stats-title {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .stats-body {
      padding: $spacing-lg;

      .stats-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-md;

        &:last-child {
          margin-bottom: 0;
        }

        .stats-label {
          font-size: $font-size-md;
          color: $text-secondary;
        }

        .stats-value {
          font-size: $font-size-md;
          font-weight: 600;
          color: $primary-color;
        }
      }
    }
  }
}

.help-section {
  margin: $spacing-md;

  .help-card {
    @extend .card;

    .help-header {
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      .help-title {
        display: flex;
        align-items: center;
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-primary;

        .help-icon {
          margin-right: $spacing-sm;
          font-size: $font-size-xl;
        }
      }
    }

    .help-body {
      padding: $spacing-lg;

      .help-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: $spacing-md;

        &:last-child {
          margin-bottom: 0;
        }

        .help-bullet {
          color: $primary-color;
          font-weight: bold;
          margin-right: $spacing-sm;
          margin-top: 2rpx;
        }

        .help-text {
          flex: 1;
          font-size: $font-size-sm;
          color: $text-secondary;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
