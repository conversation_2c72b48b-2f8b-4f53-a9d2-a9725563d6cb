<!--
 * @Description: 快速编辑记录弹窗组件
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01 17:04:13
 * @Props:
 *   - show: Boolean - 控制弹窗显示/隐藏
 *   - record: Object - 要编辑的记录数据
 *   - index: Number - 记录索引
 * @Events:
 *   - close: 关闭弹窗
 *   - save: 保存编辑，参数为编辑后的记录数据
-->
<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="bottom"
    height="60vh"
    :round="20"
    :duration="200"
    class="edit-simple-popup-box"
  >
    <view class="edit-simple-popup" v-if="show">
      <!-- 拖拽指示器 -->
      <view class="drag-indicator"></view>

      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">✏️ 编辑记录</text>
        <view class="popup-close" @click="handleClose">
          <text>×</text>
        </view>
      </view>

      <!-- 编辑表单 -->
      <view class="edit-form" v-if="formData">
        <!-- 记录类型选择 -->
        <view class="form-row">
          <text class="row-label">类型：</text>
          <view class="type-selector">
            <view
              class="type-option"
              :class="{ 'active': formData && formData.type === 1 }"
              @click="setType(1)"
            >
              支出
            </view>
            <view
              class="type-option"
              :class="{ 'active': formData && formData.type === 2 }"
              @click="setType(2)"
            >
              入账
            </view>
            <view
              class="type-option"
              :class="{ 'active': formData && formData.type === 3 }"
              @click="setType(3)"
            >
              不计入收入
            </view>
          </view>
        </view>

        <!-- 金额输入 -->
        <view class="form-row">
          <text class="row-label">金额：</text>
          <view class="amount-input-container">
            <text class="currency-symbol">¥</text>
            <input
              class="amount-input"
              placeholder="0.00"
              v-model="formData.amount"
            />
          </view>
        </view>

        <!-- 分类选择 -->
        <view class="form-row">
          <text class="row-label">分类：</text>
          <view class="category-selector">
            <view
              v-for="category in currentCategories"
              :key="category.id"
              class="category-option oneline"
              :class="{ 'active': formData.category_id == category.id }"
              @click="setCategory(category.id)"
            >
              {{ category.name }}
            </view>
          </view>
        </view>

        <!-- 标签选择 -->
        <view class="form-row">
          <text class="row-label">标签：</text>
          <view class="tags-selector">
            <view
              v-for="tag in currentTags"
              :key="tag.id"
              class="tag-option oneline"
              :class="{ 'active': formData.tags.includes(tag.name) }"
              @click="toggleTag(tag.name)"
            >
              {{ tag.name }}
            </view>
          </view>
        </view>

        <!-- 描述输入 -->
        <view class="form-row">
          <text class="row-label">描述：</text>
          <textarea
            class="description-textarea"
            placeholder="请输入描述..."
            :value="formData ? formData.description : ''"
            @input="onDescriptionInput"
            maxlength="100"
          />
        </view>

        <!-- 时间选择 -->
        <view class="form-row">
          <text class="row-label">时间：</text>
          <view class="time-selector" @click="openTimePicker">
            <text class="time-display">{{ formatDisplayTime(formData ? formData.record_time : '') }}</text>
            <text class="time-icon">📅</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮组 -->
      <view class="popup-footer">
        <view class="button-group">
          <button class="cancel-btn" @click="handleCancel">
            取消
          </button>
          <button class="save-btn" @click="handleSave" :disabled="!canSave">
            <text class="save-icon">💾</text>
            <text>保存</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 时间选择器 -->
    <u-datetime-picker
      :show="showTimePicker"
      v-model="selectedDateTime"
      mode="datetime"
      @confirm="confirmTime"
      @cancel="showTimePicker = false"
    ></u-datetime-picker>
  </u-popup>
</template>
<script>
import { formatDate, validateAmount } from '../data/utils.js'

export default {
  name: 'EditSimple',
  props: {
    // 控制弹窗显示/隐藏
    show: {
      type: Boolean,
      default: false
    },
    // 要编辑的记录数据
    record: {
      type: Object,
      default: () => null
    },
    // 记录索引
    index: {
      type: Number,
      default: -1
    },
    // 分类数据
    categoriesData: {
      type: Array,
      default: () => []
    },
    // 标签数据
    tagsData: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      formData: null, // 表单数据
      showTimePicker: false, // 是否显示时间选择器
      selectedDateTime: 0, // 选中的时间戳
      selectedTagIds: [] // 选中的标签ID数组
    }
  },

  mounted() {
    console.log('EditSimple mounted')
    console.log('categoriesData:', this.categoriesData)
    console.log('tagsData:', this.tagsData)
    console.log('record:', this.record)
  },

  computed: {
    // 是否可以保存
    canSave() {
      if (!this.formData) return false

      return this.formData.amount &&
             validateAmount(this.formData.amount.toString()) &&
             this.formData.description &&
             this.formData.description.trim()
    },

    // 当前类型的分类
    currentCategories() {
      if (!this.formData) return []
      return this.categoriesData.filter(cat => cat.type == this.formData.type)
    },

    // 当前类型的标签
    currentTags() {
      if (!this.formData) return []
      return this.tagsData.filter(tag => tag.type == this.formData.type)
    }
  },

  watch: {
    // 监听show变化，弹窗打开时初始化表单数据
    show(newVal) {
      if (newVal && this.record) {
        this.initFormData()
      } else if (!newVal) {
        this.formData = null
      }
    },

    // 监听record变化
    record: {
      handler(newVal) {
        if (newVal && this.show) {
          this.initFormData()
        }
      },
      deep: true
    },

    // 监听分类数据变化
    categoriesData: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && this.show && this.record) {
          this.initFormData()
        }
      },
      deep: true
    },

    // 监听标签数据变化
    tagsData: {
      handler(newVal) {
        if (newVal && newVal.length > 0 && this.show && this.record) {
          this.initFormData()
        }
      },
      deep: true
    }
  },

  methods: {
    // 初始化表单数据
    initFormData() {
      if (!this.record) return

      console.log('初始化表单数据:', this.record)
      console.log('分类数据:', this.categoriesData)
      console.log('标签数据:', this.tagsData)

      this.formData = {
        type: this.record.type || 1,
        amount: this.record.amount !== undefined ? this.record.amount.toString() : '',
        description: this.record.description || '',
        record_time: this.record.record_time || new Date().toISOString().slice(0, 19).replace('T', ' '),
        category_id: this.record.category_id || null,
        tags: Array.isArray(this.record.tags) ? [...this.record.tags] : [],
        confirmed: this.record.confirmed || false
      }

      // 设置时间选择器的值
      try {
        this.selectedDateTime = new Date(this.formData.record_time).getTime()
      } catch (error) {
        this.selectedDateTime = Date.now()
      }

      // 初始化选中的标签ID - tags是字符串数组["点广告"]
      this.selectedTagIds = []
      if (this.formData.tags && this.formData.tags.length > 0 && this.tagsData.length > 0) {
        this.selectedTagIds = this.tagsData
          .filter(tag => this.formData.tags.includes(tag.name))
          .map(tag => tag.id)
        console.log('匹配到的标签IDs:', this.selectedTagIds)
      }

      console.log('初始化后的formData:', this.formData)
      console.log('选中的标签IDs:', this.selectedTagIds)
    },

    // 设置类型
    setType(type) {
      if (!this.formData) return
      this.formData.type = type
    },

    // 设置分类
    setCategory(categoryId) {
      if (!this.formData) return
      this.formData.category_id = categoryId
    },

    // 打开时间选择器
    openTimePicker() {
      if (!this.formData) return
      this.showTimePicker = true
    },

    // 确认时间选择
    confirmTime(e) {
      if (!this.formData) return
      const selectedDate = new Date(e.value)
      this.formData.record_time = selectedDate.toISOString().slice(0, 19).replace('T', ' ')
      this.selectedDateTime = selectedDate.getTime()
      this.showTimePicker = false
    },

    // 格式化显示时间
    formatDisplayTime(timeStr) {
      if (!timeStr) return '选择时间'

      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return timeStr
      }
    },

    // 切换标签选择
    toggleTag(tagName) {
      if (!this.formData) return

      const index = this.formData.tags.indexOf(tagName)
      if (index > -1) {
        this.formData.tags.splice(index, 1)
      } else {
        this.formData.tags.push(tagName)
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 取消编辑
    handleCancel() {
      this.formData = null
      this.handleClose()
    },

    // 保存编辑
    async handleSave() {
      if (!this.canSave) {
        this.$toast('请完善记录信息')
        return
      }

      // 验证金额
      if (!validateAmount(this.formData.amount)) {
        this.$toast('请输入正确的金额')
        return
      }

      // 验证描述
      if (!this.formData.description.trim()) {
        this.$toast('请输入描述信息')
        return
      }

      // 构造保存的数据
      const saveData = {
        ...this.formData,
        amount: parseFloat(this.formData.amount),
        description: this.formData.description.trim()
      }

      // 先保存编辑到本地
      this.$emit('save', saveData)

      // 然后调用父组件的保存到服务器方法，并等待完成
      this.$emit('saveToServer', this.index)

      // 关闭编辑弹窗
      this.$emit('close')

      this.$toast('记录已更新并保存')
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../styles/common.scss";

.edit-simple-popup-box {
  .edit-simple-popup {
    background-color: $card-background;
    border-radius: 20rpx 20rpx 0 0;
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// 拖拽指示器
.drag-indicator {
  width: 60rpx;
  height: 6rpx;
   // background-color: $text-disabled;
  border-radius: 3rpx;
  margin: $spacing-md auto $spacing-sm;
}

// 标题栏
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 $spacing-lg $spacing-md;
  border-bottom: 1rpx solid $border-color;

  .popup-title {
    font-size: $font-size-lg;
    font-weight: 700;
    color: $text-primary;
  }

  .popup-close {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48rpx;
    color: $text-secondary;

    &:active {
      opacity: 0.6;
    }
  }
}

// 编辑表单
.edit-form {
  flex: 1;
  padding: $spacing-md;
  overflow-y: auto;
  max-height: 50vh;

  // 表单行
  .form-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: $spacing-sm;
    min-height: 60rpx;

    .row-label {
      font-size: $font-size-sm;
      color: $text-primary;
      width: 80rpx;
      flex-shrink: 0;
      padding-top: 12rpx; // 对齐换行内容
    }

    // 对于分类和标签，允许换行
    &:has(.category-selector), &:has(.tags-selector) {
      align-items: flex-start;

      .row-label {
        padding-top: 8rpx;
      }
    }
  }
}

// 类型选择器
.type-selector {
  display: flex;
  gap: $spacing-md;

  .type-option {
    padding: $spacing-xs $spacing-sm;
    border: 1rpx solid $border-color;
    border-radius: $border-radius-sm;
    background-color: $background-color;
    font-size: $font-size-xs;
    color: $text-primary;
    text-align: center;
    min-width: 80rpx;

    &.active {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
  }
}

// 分类选择器
.category-selector {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  flex: 1;

  .category-option {
    padding: 8rpx 16rpx;
    border: 1rpx solid $border-color;
    border-radius: $border-radius-sm;
    background-color: $background-color;
    font-size: $font-size-xs;
    color: $text-primary;
    text-align: center;
    width: calc(25% - 6rpx); // 1行5个
    margin-bottom: 8rpx;

    &.active {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
  }
}

// 标签选择器
.tags-selector {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  flex: 1;

  .tag-option {
    padding: 8rpx 16rpx;
    border: 1rpx solid $border-color;
    border-radius: $border-radius-sm;
    background-color: $background-color;
    font-size: $font-size-xs;
    color: $text-primary;
    text-align: center;
    width: calc(25% - 6rpx); // 1行5个
    margin-bottom: 8rpx;

    &.active {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
  }
}

// 金额输入容器
.amount-input-container {
  display: flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  background-color: $background-color;
  flex: 1;
  height: 60rpx;

  &:focus-within {
    border-color: $primary-color;
  }

  .currency-symbol {
    font-size: $font-size-sm;
    color: $text-primary;
    margin-right: $spacing-xs;
  }

  .amount-input {
    flex: 1;
    font-size: $font-size-sm;
    text-align: left;
    color: $text-primary;
    border: none;
    background: transparent;
    padding: 0 !important;

    &::placeholder {
      color: #cccccc;
    }
  }
}

// 描述输入
.description-textarea {
  flex: 1;
  height: 50rpx;
  padding: $spacing-xs $spacing-sm;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  color: $text-primary;
  background-color: $background-color;

  &:focus {
    border-color: $primary-color;
    outline: none;
  }

  &::placeholder {
    color: #cccccc;
  }
}

.char-count {
  text-align: right;
  margin-top: $spacing-xs;
  font-size: $font-size-xs;
  color: $text-secondary;
}

// 时间选择器
.time-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-md;
  border: 2rpx solid $border-color;
  border-radius: $border-radius-md;
  background-color: $background-color;
  height: 60rpx;
  width: 100%;

  &:active {
    background-color: rgba($primary-color, 0.05);
  }

  .time-display {
    font-size: 26rpx;
    color: #666;
    width: 100%;
  }

  .time-icon {
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

// 底部按钮组
.popup-footer {
  padding: $spacing-lg;
  border-top: 1rpx solid $border-color;
  background-color: $card-background;

  .button-group {
    display: flex;
    gap: $spacing-md;

    .cancel-btn, .save-btn, .save-server-btn {
      flex: 1;
      padding: $spacing-xs $spacing-sm;
      border: none;
      border-radius: $border-radius-sm;
      font-size: $font-size-xs;
      font-weight: 600;
      transition: all $transition-fast;
      height: 60rpx;
    }

    .cancel-btn {
      background-color: #f0f0f0;
      color: #1a1a1a;

      &:active {
        background-color: #e0e0e0;
      }
    }

    .save-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      color: white;

      &:disabled {
        opacity: 0.6;
        background: #f0f0f0;
      }

      &:active:not(:disabled) {
        background: linear-gradient(135deg, #389e0d 0%, #237804 100%);
      }

      .save-icon {
        font-size: $font-size-md;
      }
    }

    .save-server-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      color: white;

      &:disabled {
        opacity: 0.6;
        background: #f0f0f0;
      }

      &:active:not(:disabled) {
        background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
      }

      .save-icon {
        font-size: $font-size-md;
      }
    }
  }
}
</style>
