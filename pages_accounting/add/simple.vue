<!--
 * @Description: 简单记录列表页面 - 显示预置的记录数据列表
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01 17:03:43
-->
<template>
	<view class="simple-container">
		<!-- 记录列表 -->
		<view class="records-list" v-if="recordsList && recordsList.length > 0">
			<view
				v-for="(record, index) in recordsList"
				:key="index"
				class="record-item"
				:class="{
					confirmed: record.confirmed,
					saving: savingIndexes.includes(index)
				}"
				@click="handleEditRecord(index)"
			>
				<!-- 左侧类型图标 -->
				<view
					class="type-icon-wrapper"
					:class="getTypeClass(record.type)"
				>
					<text class="type-icon">{{
						getTypeIcon(record.type)
					}}</text>
				</view>

				<!-- 中间内容区域 -->
				<view class="content-area">
					<!-- 第一行：金额和类型 -->
					<view class="first-line">
						<view class="amount-container" :class="getTypeClass(record.type)">
							<text class="currency-symbol">¥</text>
							<text class="amount-value">{{ formatAmount(record.amount) }}</text>
						</view>
						<text class="type-text">{{
							getTypeName(record.type)
						}}</text>
						<text class="category-text">{{
							getCategoryName(record.category_id)
						}}</text>
					</view>

					<!-- 第二行：描述 -->
					<view class="second-line">
						<text class="description">{{
							record.description || "无描述"
						}}</text>
						<!-- 标签显示 -->
						<view
							class="tags-container"
							v-if="record.tags && record.tags.length > 0"
						>
							<text
								class="tag-item"
								v-for="tag in record.tags"
								:key="tag"
								>{{ tag }}</text
							>
						</view>
					</view>

					<!-- 第三行：时间 -->
					<view class="third-line">
						<text class="record-time f-24 c888">{{ formatRecordTime(record.record_time) }}</text>
					</view>
				</view>

				<!-- 右侧操作按钮 -->
				<view class="action-buttons" @click.stop="">
					<view
						class="action-btn delete-btn"
						@click="handleDeleteRecord(index)"
					>
						<text class="btn-icon">🗑️</text>
					</view>
					<view
						class="action-btn edit-btn"
						@click="handleEditRecord(index)"
					>
						<text class="btn-icon">✏️</text>
					</view>
					<view
						class="action-btn confirm-btn"
						:class="{
							confirmed: record.confirmed,
							saving: savingIndexes.includes(index)
						}"
						@click="handleConfirmRecord(index)"
					>
						<text class="btn-icon">{{
							savingIndexes.includes(index) ? "⏳" : (record.confirmed ? "✅" : "☑️")
						}}</text>
					</view>
				</view>
			</view>
		</view>

		<view class="empty-state mt-40" v-else>
			<view v-if="showAISimple">
				<view class="empty-icon">📝</view>
				<view class="empty-desc">点击右下角对话按钮</view>
				<view class="empty-desc-sub">使用AI识别账单信息</view>
			</view>
		</view>

		<!-- 编辑弹窗 -->
		<edit-simple
			:show="showEditDialog"
			:record="editingRecord"
			:index="editingIndex"
			:categoriesData="categoriesData"
			:tagsData="tagsData"
			@close="handleCloseEdit"
			@save="handleSaveEdit"
			@saveToServer="handleSaveToServer"
		/>

		<!-- AI识别弹窗组件 -->
		<xj-aitextarea-popup
			ref="aiPopupRef"
			:show="showAiRecognition"
			@close="handleAiPopupClose"
			@submit="handleAiSubmit"
		/>

    <xj-float-button
      icon="💬"
			v-if="showAISimple"
      @click="handleAiRecognitionClick"
    />

		<!-- AI分析加载动画组件 -->
		<ai-loading
			:show="showAiLoading"
			mainText="AI正在努力帮您分析"
			subText="智能识别账单信息中..."
		/>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions" v-if="showAISimple && recordsList && recordsList.length > 0">
			<button class="action-btn clear-btn" @click="handleClearAndClose">
				<text class="btn-icon">🗑️</text>
				<text>清空并关闭</text>
			</button>
			<button
				class="action-btn save-all-btn"
				:class="{ 'disabled': isAllSaved || isSavingAll }"
				:disabled="isAllSaved || isSavingAll"
				@click="handleSaveAll"
			>
				<text class="btn-icon">💾</text>
				<text>{{ getSaveAllButtonText() }}</text>
			</button>
		</view>
	</view>
</template>

<script>
import EditSimple from "./editSimple.vue";
import { formatDate } from "../data/utils.js";
import XjAitextareaPopup from "@/components/xj-aitextarea-popup/index.vue";
import XjFloatButton from "@/components/xj-float-button/index.vue";
import AiLoading from "@/components/ai-loading/aiLoading.vue";

export default {
	name: "SimpleAiAccounting",
	components: {
		EditSimple,
		XjAitextareaPopup,
		XjFloatButton,
		AiLoading,
	},
	props: {
		showAISimple: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			recordsList: [], // 记录列表
			showEditDialog: false, // 是否显示编辑弹窗
			showAiRecognition: false, // 是否显示AI识别弹窗
			editingRecord: null, // 正在编辑的记录
			editingIndex: -1, // 正在编辑的记录索引
			tagsData: [], // 从API获取的标签数据
			categoriesData: [], // 从API获取的分类数据
			savingIndexes: [], // 正在保存的记录索引数组
			hasTriggeredAutoActions: false, // 标志：是否已经触发过自动操作
			isSavingAll: false, // 是否正在全部保存
			showAiLoading: false, // AI分析加载状态
		};
	},

	computed: {
		// 是否所有记录都已保存
		isAllSaved() {
			return this.recordsList &&
				   this.recordsList.length > 0 &&
				   this.recordsList.every(record => record.confirmed);
		}
	},

	async onLoad(options) {
		console.log("🚀 -> onLoad -> options:", options);

		// 加载分类和标签数据
		await this.loadCategories();
		await this.loadTags();

		// 处理AI对话相关参数 - 只执行一次
		console.log('页面参数:', options);
		console.log('是否已触发过自动操作:', this.hasTriggeredAutoActions);

		if (!this.hasTriggeredAutoActions && (options?.ai == '1' || options?.ai == 1)) {
			console.log('检测到AI参数，准备弹出AI识别弹窗');
			this.hasTriggeredAutoActions = true; // 标记已经触发过

			// 延迟一下确保页面渲染完成
			this.$nextTick(() => {
				setTimeout(() => {
					this.showAiRecognition = true;
					console.log('AI识别弹窗已显示');

					// 如果同时设置了自动录音
					if (options?.autoVoice == '1' || options?.autoVoice == 1) {
						console.log('检测到自动录音参数，准备触发自动录音');
						// 再延迟一下确保AI弹窗已经显示
						setTimeout(() => {
							this.triggerAutoVoiceRecording();
						}, 300);
					}
				}, 100);
			});
		}

		// 页面加载时预置测试数据（开发阶段）
		// this.loadMockData();
	},

	methods: {
		// 处理AI识别按钮点击
		handleAiRecognitionClick() {
			this.showAiRecognition = true;
		},

		// 处理AI弹窗关闭
		handleAiPopupClose() {
			this.showAiRecognition = false;
			// 重置自动操作标志，允许用户再次手动触发
			// 注意：这里不重置，避免用户关闭后重新触发自动操作
			// this.hasTriggeredAutoActions = false
		},

		// AI识别提交回调
		async handleAiSubmit(recognizedContent) {
			if (recognizedContent.trim() == "") {
				// this.$toast('识别结果为空')
				return;
			}

			// AI识别只在新建模式下可用
			if (this.isEditMode) {
				this.$toast("AI识别仅在新建待办时可用");
				return;
			}

			try {
				// 显示AI分析加载动画
				this.showAiLoading = true;

				const _res = await this.$ajax.post("/accounting/ai/extract", {
					content: recognizedContent,
				},false);

				if (_res?.code == 200 && _res?.data) {
					const _list = _res.data?.arguments?.records || []

					// 只处理addTask类型的识别结果
					if (_list.length > 0) {
						// 合并关系
						this.recordsList = [...this.recordsList,..._list]	;

						// this.$toast("AI识别完成，已自动填充表单");
					} else {
						this.$toast("AI识别结果格式不正确");
					}
				} else {
					this.$toast(_res?.msg || "AI识别失败");
				}
			} catch (error) {
				console.error("AI识别失败:", error);
				this.$toast("AI识别失败，请重试");
			} finally {
				// 隐藏AI分析加载动画
				this.showAiLoading = false;
			}
		},

		// 获取类型样式类名
		getTypeClass(type) {
			const typeMap = {
				1: "expense",
				2: "income",
				3: "transfer",
			};
			return typeMap[type] || "expense";
		},

		// 获取类型图标
		getTypeIcon(type) {
			const iconMap = {
				1: "💸",
				2: "💰",
				3: "🔄",
			};
			return iconMap[type] || "💸";
		},

		// 获取类型名称
		getTypeName(type) {
			const nameMap = {
				1: "支出",
				2: "入账",
				3: "不计入收入",
			};
			return nameMap[type] || "支出";
		},

		// 获取分类名称
		getCategoryName(categoryId) {
			// 优先从API数据中查找
			const category = this.categoriesData.find(
				(cat) => cat.id === categoryId
			);
			if (category) {
				return category.name;
			}
		},

		// 格式化金额显示
		formatAmount(amount) {
			return parseFloat(amount || 0).toFixed(2);
		},

		// 格式化记录时间显示（只显示年月日）
		formatRecordTime(timeStr) {
			if (!timeStr) return '';

			try {
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			} catch (error) {
				console.error('时间格式化失败:', error);
				return timeStr;
			}
		},

		// 加载分类数据
		async loadCategories() {
			try {
				const _res = await this.$ajax.get("/accounting/categories");
				if (_res?.code == 200) {
					this.categoriesData = _res.data || [];
				}
			} catch (error) {
				this.categoriesData = [];
			}
		},

		// 加载标签数据
		async loadTags() {
			try {
				// 获取所有类型的标签
				const _res = await this.$ajax.get("/accounting/tags");
				if (_res?.code == 200) {
					this.tagsData = _res.data || [];
				}
			} catch (error) {
				this.tagsData = [];
			}
		},

		// 删除记录
		async handleDeleteRecord(index) {
			if (await this.$confirm1("确定要删除这条记录吗？") == 1) {
				this.recordsList.splice(index, 1);
				this.$toast("记录已删除");
			 }
		},

		// 编辑记录
		handleEditRecord(index) {
			// 如果记录已保存，不允许编辑
			if (this.recordsList[index].confirmed) {
				this.$toast("已保存的记录不能编辑");
				return;
			}

			// 如果正在保存，不允许编辑
			if (this.savingIndexes.includes(index)) {
				this.$toast("记录正在保存中，请稍候...");
				return;
			}

			this.editingIndex = index;
			this.editingRecord = { ...this.recordsList[index] };
			this.showEditDialog = true;
		},

		// 确认记录 - 保存单条记录到服务器
		async handleConfirmRecord(index) {
			const record = this.recordsList[index];

			// 检查是否已经保存过
			if (record.confirmed) {
				this.$toast("该记录已保存，不能重复保存");
				return;
			}

			// 检查是否正在保存
			if (this.savingIndexes.includes(index)) {
				this.$toast("正在保存中，请稍候...");
				return;
			}

			try {
				// 添加到保存中的索引数组
				this.savingIndexes.push(index);
				// 构造保存数据
				const recordData = {
					type: record.type,
					categoryId: record.category_id,
					amount: parseFloat(record.amount),
					description: record.description || "",
					recordTime: Math.floor(new Date(record.record_time).getTime() / 1000),
					tags: Array.isArray(record.tags) ? record.tags : []
				};

				const _res = await this.$ajax.post("/accounting/record/save", recordData);

				if (_res?.code == 200) {
					// 保存成功，标记为已确认
					this.recordsList[index].confirmed = true;
					this.$toast("记录保存成功");

					// 检查是否所有记录都已保存完毕
					this.checkAllRecordsSaved();
				} else {
					throw new Error(_res?.msg || "保存失败");
				}

			} catch (error) {
				this.$toast(error.message || "保存失败，请重试");
			} finally {
				// 从保存中的索引数组中移除
				const savingIndex = this.savingIndexes.indexOf(index);
				if (savingIndex > -1) {
					this.savingIndexes.splice(savingIndex, 1);
				}
			}
		},

		// 关闭编辑弹窗
		handleCloseEdit() {
			this.showEditDialog = false;
			this.editingRecord = null;
			this.editingIndex = -1;
		},

		// 保存编辑
		handleSaveEdit(editedRecord) {
			if (this.editingIndex >= 0) {
				this.recordsList[this.editingIndex] = { ...editedRecord };
				this.$toast("记录已更新");
			}
			this.handleCloseEdit();
		},

		// 处理从编辑弹窗保存到服务器
		async handleSaveToServer(index) {
			// 不在这里关闭弹窗，由editSimple组件自己处理关闭
			// 直接调用确认记录方法保存到服务器
			await this.handleConfirmRecord(index);
		},

		// 检查所有记录是否都已保存完毕
		checkAllRecordsSaved() {
			// 检查条件：列表数量>0 且不为空 且showAISimple为true 且所有数据均保存完毕
			if (this.recordsList &&
				this.recordsList.length > 0 &&
				this.showAISimple &&
				this.recordsList.every(record => record.confirmed)) {

				// 延迟1秒后自动关闭当前窗口
				setTimeout(() => {
					this.$toast("所有记录已保存完毕，即将关闭");
					setTimeout(() => {
						uni.navigateBack();
					}, 300);
				}, 300);
			}
		},

		// 触发自动录音功能
		triggerAutoVoiceRecording() {
			try {
				console.log('开始触发自动录音功能');
				console.log('AI弹窗组件引用:', this.$refs.aiPopupRef);

				// 通过ref调用AI弹窗组件的自动录音方法
				if (this.$refs.aiPopupRef && this.$refs.aiPopupRef.triggerAutoVoice) {
					console.log('调用AI弹窗组件的自动录音方法');
					this.$refs.aiPopupRef.triggerAutoVoice();
				} else {
					console.warn('AI弹窗组件未找到或不支持自动录音功能', {
						ref: this.$refs.aiPopupRef,
						method: this.$refs.aiPopupRef?.triggerAutoVoice
					});
				}
			} catch (error) {
				console.error('触发自动录音失败:', error);
			}
		},

		// 清空并关闭
		async handleClearAndClose() {
			if (await this.$confirm1("确定要清空所有记录并关闭吗？") == 1) {
				this.recordsList = [];
				this.$toast("已清空所有记录");
				setTimeout(() => {
					uni.navigateBack();
				}, 500);
			}
		},

		// 全部保存
		async handleSaveAll() {
			if (this.isSavingAll) {
				this.$toast("正在保存中，请稍候...");
				return;
			}

			if (this.isAllSaved) {
				this.$toast("所有记录已保存完毕");
				return;
			}

			// 获取未保存的记录
			const unsavedIndexes = this.recordsList
				.map((record, index) => ({ record, index }))
				.filter(item => !item.record.confirmed)
				.map(item => item.index);

			if (unsavedIndexes.length === 0) {
				this.$toast("没有需要保存的记录");
				return;
			}

			try {
				this.isSavingAll = true;
				this.$toast(`开始保存 ${unsavedIndexes.length} 条记录...`);

				// 逐个保存记录
				for (let i = 0; i < unsavedIndexes.length; i++) {
					const index = unsavedIndexes[i];
					await this.saveRecordByIndex(index);

					// 每保存一条记录后稍微延迟，避免请求过于频繁
					if (i < unsavedIndexes.length - 1) {
						await new Promise(resolve => setTimeout(resolve, 200));
					}
				}

				this.$toast("所有记录保存完成");

				// 检查是否所有记录都已保存完毕，触发自动关闭
				this.checkAllRecordsSaved();

			} catch (error) {
				console.error("批量保存失败:", error);
				this.$toast("部分记录保存失败，请重试");
			} finally {
				this.isSavingAll = false;
			}
		},

		// 保存单条记录（内部方法）
		async saveRecordByIndex(index) {
			const record = this.recordsList[index];

			if (record.confirmed) {
				return; // 已保存的跳过
			}

			try {
				// 构造保存数据
				const recordData = {
					type: record.type,
					categoryId: record.category_id,
					amount: parseFloat(record.amount),
					description: record.description || "",
					recordTime: Math.floor(new Date(record.record_time).getTime() / 1000),
					tags: Array.isArray(record.tags) ? record.tags : []
				};

				const _res = await this.$ajax.post("/accounting/record/save", recordData);

				if (_res?.code == 200) {
					// 保存成功，标记为已确认
					this.recordsList[index].confirmed = true;
				} else {
					throw new Error(_res?.msg || "保存失败");
				}

			} catch (error) {
				console.error(`保存第${index + 1}条记录失败:`, error);
				throw error;
			}
		},

		// 获取全部保存按钮文本
		getSaveAllButtonText() {
			if (this.isAllSaved) {
				return "全部已保存";
			} else if (this.isSavingAll) {
				return "保存中...";
			} else {
				const unsavedCount = this.recordsList.filter(record => !record.confirmed).length;
				return `保存全部 (${unsavedCount})`;
			}
		},

	},
};
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.simple-container {
	background-color: $background-color;
	min-height: 100vh;
	padding: $spacing-lg;
	padding-bottom: 140rpx; // 为底部按钮留出空间，从160rpx减小到140rpx
}

// 记录列表
.records-list {
	display: flex;
	flex-direction: column;
	gap: $spacing-sm;
}

// 记录项
.record-item {
	display: flex;
	align-items: center;
	background-color: $card-background;
	border-radius: $border-radius-md;
	padding: $spacing-md;
	border: 1rpx solid $border-color;
	transition: all $transition-fast;

	&.confirmed {
		border-color: #d9d9d9;
		background-color: #f5f5f5;
		opacity: 0.6;

		// 已保存记录的文字变灰
		.amount, .type-text, .category-text, .description {
			color: #999 !important;
		}
	}

	&.saving {
		border-color: $primary-color;
		background-color: rgba($primary-color, 0.05);
	}
}

// 左侧类型图标
.type-icon-wrapper {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: $spacing-md;
	flex-shrink: 0;

	&.expense {
		background-color: rgba($expense-color, 0.1);
	}

	&.income {
		background-color: rgba($income-color, 0.1);
	}

	&.transfer {
		background-color: rgba($transfer-color, 0.1);
	}

	.type-icon {
		font-size: 24rpx;
	}
}

// 中间内容区域
.content-area {
	flex: 1;

	.first-line {
		display: flex;
		align-items: center;
		margin-bottom: 4rpx;

		.amount-container {
			display: flex;
			align-items: baseline;
			margin-right: $spacing-sm;

			.currency-symbol {
				font-size: 22rpx; // 很小的￥符号
				font-weight: 400;
				margin-right: 2rpx;
			}

			.amount-value {
				font-size: $font-size-lg;
				font-weight: 700;
			}

			&.expense {
				color: $expense-color;
			}

			&.income {
				color: $income-color;
			}

			&.transfer {
				color: $transfer-color;
			}
		}

		.type-text {
			font-size: $font-size-xs;
			color: $text-secondary;
			margin-right: $spacing-sm;
		}

		.category-text {
			font-size: $font-size-xs;
			color: $text-secondary;
		}
	}

	.second-line {
		.description {
			font-size: $font-size-sm;
			color: $text-primary;
			margin-bottom: 4rpx;
		}

		.tags-container {
			display: flex;
			flex-wrap: wrap;
			gap: 4rpx;

			.tag-item {
				padding: 2rpx 6rpx;
				background-color: rgba($primary-color, 0.1);
				color: $primary-color;
				border-radius: 4rpx;
				font-size: $font-size-xs;
			}
		}

		.third-line {
			margin-top: 4rpx;

			.record-time {
				font-size: 24rpx;
				color: #ccc;
			}
		}
	}
}

// 右侧操作按钮
.action-buttons {
	display: flex;
	gap: $spacing-sm; // 从$spacing-xs增加到$spacing-sm

	.action-btn {
		width: 56rpx; // 从40rpx增加到56rpx
		height: 56rpx; // 从40rpx增加到56rpx
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: $spacing-sm; // 增加内边距
		transition: all $transition-fast;

		&:active {
			transform: scale(0.95);
		}

		.btn-icon {
			font-size: 20rpx; // emoji尺寸保持不变
		}



		&.delete-btn {
			background-color: rgba($error-color, 0.1);

			.btn-icon {
				color: $error-color;
			}

			&:active {
				background-color: rgba($error-color, 0.2);
			}
		}

		&.edit-btn {
			background-color: rgba($primary-color, 0.1);

			.btn-icon {
				color: $primary-color;
			}

			&:active {
				background-color: rgba($primary-color, 0.2);
			}
		}

		&.confirm-btn {
			background-color: rgba($income-color, 0.1);

			.btn-icon {
				color: $income-color;
			}

			&:active:not(.confirmed):not(.saving) {
				background-color: rgba($income-color, 0.2);
			}

			&.confirmed {
				background-color: #52c41a;
				.btn-icon {
					color: white;
				}
			}

			&.saving {
				background-color: rgba($primary-color, 0.2);
				.btn-icon {
					color: $primary-color;
				}
			}
		}
	}
}

// 底部操作按钮
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: $spacing-lg;
	background-color: $card-background;
	border-top: 1rpx solid $border-color;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	gap: $spacing-md;

	.action-btn {
		flex: 1;
		height: 64rpx; // 从80rpx减小到64rpx
		display: flex;
		align-items: center;
		justify-content: center;
		gap: $spacing-xs; // 从$spacing-sm减小到$spacing-xs
		border: none;
		border-radius: $border-radius-md;
		font-size: $font-size-sm; // 从$font-size-md减小到$font-size-sm
		font-weight: 600;
		transition: all $transition-fast;

		&:active:not(.disabled) {
			transform: scale(0.98);
		}

		&.disabled {
			opacity: 0.6;
		}

		.btn-icon {
			font-size: $font-size-md; // 从$font-size-lg减小到$font-size-md
		}

		&.clear-btn {
			background-color: white;
			color: #ff4d4f;
			border: 1rpx solid #ff4d4f;

			&:active {
				background-color: #fff2f0;
			}
		}

		&.save-all-btn {
			background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
			color: white;

			&:active:not(.disabled) {
				background: linear-gradient(135deg, #389e0d 0%, #237804 100%);
			}

			&.disabled {
				background: #f0f0f0;
				color: #999;
			}
		}
	}
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: $spacing-xl;
	text-align: center;

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: $spacing-lg;
		opacity: 0.6;
	}

	.empty-desc {
		font-size: $font-size-lg;
		color: $text-primary;
		font-weight: 600;
		margin-bottom: $spacing-xs;
	}

	.empty-desc-sub {
		font-size: $font-size-sm;
		color: $text-secondary;
		line-height: 1.4;
	}
}

</style>
