<template>
  <view class="add-record-container">
    <!-- 金额输入区域 -->
    <view class="amount-section">
      <view class="amount-label">{{ currentTypeConfig.name }}</view>
      <input
        class="amount-input"
        :class="currentType"
        type="digit"
        placeholder="0.00"
        v-model="formData.amount"
        @input="onAmountInput"
        :focus="true"
      />
    </view>

    <!-- 账户类型切换 -->
    <view class="category-selector">
      <view class="category-tabs">
        <view
          v-for="(config, type) in accountTypeConfigs"
          :key="type"
          class="category-tab"
          :class="{ active: currentType === type }"
          @click="switchType(type)"
        >
          <text class="tab-icon">{{ config.icon }}</text>
          <text class="tab-text">{{ config.name }}</text>
        </view>
      </view>

      <!-- 分类网格 -->
      <view class="category-grid">
        <view
          v-for="category in currentCategories"
          :key="category.id"
          class="category-item"
          :class="{ active: formData.categoryId == category.id }"
          @click="selectCategory(category.id)"
        >
          <view
            class="category-icon"
            :style="{ backgroundColor: category.color }"
          >
            {{ category.emoji }}
          </view>
          <view class="category-name">{{ category.name }}</view>
        </view>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="detail-section">
      <!-- 日期选择 -->
      <view class="detail-item" @click="openDatePicker">
        <view class="detail-label">
          <text class="label-icon">📅</text>
          <text>日期</text>
        </view>
        <view class="detail-value">
          <text>{{ formatDisplayDate(formData.date) }}</text>
          <u-icon name="arrow-right" size="12" color="#aaa"></u-icon>
        </view>
      </view>

      <!-- 备注输入 -->
      <view class="detail-item note-item">
        <view class="detail-label">
          <text class="label-icon">📝</text>
          <text>备注</text>
        </view>
        <textarea
          class="detail-textarea"
          placeholder="添加备注..."
          v-model="formData.note"
          :maxlength="200"
          :auto-height="true"
          :show-confirm-bar="false"
        />
      </view>

      <!-- 标签选择 -->
      <view class="detail-item" @click="openTagSelector">
        <view class="detail-label">
          <text class="label-icon">🏷️</text>
          <text>标签</text>
        </view>
        <view class="detail-value">
          <view v-if="selectedTags.length > 0" class="selected-tags">
            <view
              v-for="tag in selectedTags"
              :key="tag.id"
              class="tag tag-primary"
            >
              {{ tag.name }}
            </view>
          </view>
          <text v-else class="placeholder">选择标签</text>
          <u-icon name="arrow-right" size="12" color="#aaa"></u-icon>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="save-section">
      <view class="action-buttons">
        <button class="btn btn-ai" @click="showAiRecognition = true">
          <text class="btn-icon">⚡</text>
          <text>AI识别</text>
        </button>
        <button
          class="btn btn-primary save-btn"
          :disabled="!canSave"
          @click="saveRecord"
        >
          <text class="btn-icon">💾</text>
          <text>{{ isEditMode ? '更新记录' : '保存记录' }}</text>
        </button>
      </view>
    </view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      v-model="selectedDateTime"
      mode="datetime"
      @confirm="confirmDate"
      @cancel="showDatePicker = false"
    ></u-datetime-picker>

    <!-- 自定义分类弹窗 -->
    <u-popup :show="showCustomCategoryDialog" mode="bottom"  @close="closeCustomCategoryDialog">
      <view class="custom-category-dialog" v-if="showCustomCategoryDialog">
        <view class="dialog-header">
          <view class="dialog-title">自定义分类</view>
          <view class="dialog-close" @click="closeCustomCategoryDialog">×</view>
        </view>

        <view class="dialog-content">
          <view class="form-item">
            <view class="form-label">分类名称</view>
            <input
              class="form-input"
              v-model="customCategoryForm.name"
              placeholder="请输入分类名称"
              :maxlength="8"
            />
          </view>
        </view>

        <view class="dialog-actions">
          <button class="btn btn-secondary" @click="closeCustomCategoryDialog">取消</button>
          <button
            class="btn btn-primary"
            :disabled="!customCategoryForm.name.trim()"
            @click="saveCustomCategory"
          >
            确定
          </button>
        </view>
      </view>
    </u-popup>

    <!-- AI识别弹窗组件 -->
    <xj-aitextarea-popup
      :show="showAiRecognition"
      @close="showAiRecognition = false"
      @submit="handleAiSubmit"
    />

  </view>
</template>

<script>
import storage from '../data/storage.js' // 暂时保留，用于编辑模式获取记录详情
import { ACCOUNT_TYPES, ACCOUNT_TYPE_CONFIG } from '../data/categories.js'
import { formatDate, parseAmount, validateAmount } from '../data/utils.js'
import XjAitextareaPopup from '@/components/xj-aitextarea-popup/index.vue'

export default {
  name: 'AddRecord',
  components: {
    XjAitextareaPopup
  },
  data() {
    return {
      isEditMode: false,
      editRecordId: '',
      currentType: ACCOUNT_TYPES.EXPENSE,
      formData: {
        amount: '',
        categoryId: '',
        note: '',
        date: '', // 在initDateTime中设置
        tags: []
      },
      showDatePicker: false,
      showCustomCategoryDialog: false,
      showAiRecognition: false, // AI识别弹窗状态
      selectedDateTime: 0, // 在initDateTime中设置
      selectedTags: [],
      customCategoryForm: {
        name: '',
      },
      tagsData: [], // 从API获取的标签数据
      categoriesData: [] // 从API获取的分类数据
    }
  },

  computed: {
    accountTypeConfigs() {
      return ACCOUNT_TYPE_CONFIG
    },

    currentTypeConfig() {
      return ACCOUNT_TYPE_CONFIG[this.currentType]
    },

    currentCategories() {
      return this.categoriesData.filter(cat => cat.type === this.getTypeNumber(this.currentType)) || []
    },

    availableTags() {
      // 通过API获取标签，在mounted中异步加载
      return this.tagsData || []
    },

    canSave() {
      // 始终返回true，让按钮可点击，在saveRecord方法中进行具体验证
      return true
    }
  },

  async onLoad(options) {
    this.initDateTime()
    await this.loadCategories()
    await this.loadTags()

    // 检查是否为编辑模式
    if (options.id && options.mode === 'edit') {
      await this.loadRecordForEdit(options.id)
    }
  },

  methods: {
        // 初始化日期时间
    initDateTime() {
      // 设置默认日期为当前时间
      const now = new Date()
      this.selectedDateTime = Number(now)
      // 确保使用当前时间的ISO字符串格式
      this.formData.date = now.toISOString()
    },

    // 打开日期选择器
    openDatePicker() {
      // 根据当前formData.date设置选择器的值
      if (this.formData.date) {
        this.selectedDateTime = Number(new Date(this.formData.date))
      }
      this.showDatePicker = true
    },

    // 确认日期选择
    confirmDate(e) {
      const selectedDate = new Date(e.value)
      this.formData.date = selectedDate.toISOString()
      this.selectedDateTime = Number(selectedDate)
      this.showDatePicker = false
    },

    // 加载记录用于编辑
    async loadRecordForEdit(recordId) {
      try {
        // 调用详情接口获取记录数据，与详情页面保持一致
        const _res = await this.$ajax.get('/accounting/record/detail', {
          id: recordId
        })

        if (_res?.code == 200 && _res.data) {
          const apiData = _res.data

          this.isEditMode = true
          this.editRecordId = recordId

          // 转换API数字类型为字符串类型
          this.currentType = this.getTypeStringFromNumber(apiData.type)

          this.formData = {
            amount: parseFloat(apiData.amount).toString(), // 转换为浮点数再转字符串，避免强制小数点
            categoryId: apiData.categoryId,
            note: apiData.description || '', // API返回description，表单使用note
            date: this.formatDateFromTimestamp(apiData.recordTime), // 转换时间戳为日期字符串
            tags: (apiData.tags || []).map(tag => tag.name) // 提取标签名称数组
          }

          // 设置日期时间选择器的值
          this.selectedDateTime = apiData.recordTime * 1000 // 转换为毫秒

          // 重新加载对应类型的标签和分类
          await this.loadTags()

          // 直接设置选中的标签
          this.selectedTags = apiData.tags || []

          // 设置页面标题
          uni.setNavigationBarTitle({
            title: '编辑记录'
          })
        } else {
          throw new Error(_res?.msg || '记录不存在')
        }
      } catch (error) {
        console.error('加载记录失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'error'
        })
        // 加载失败时返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 300)
      }
    },

    // 加载标签
    async loadTags() {
      try {
        // 获取对应类型的标签
        const _res = await this.$ajax.get('/accounting/tags', { type: this.getTypeNumber(this.currentType) })
        if (_res?.code == 200) {
          this.tagsData = _res.data || []

          // 根据标签名称设置选中的标签
          const tags = this.tagsData.filter(tag =>
            this.formData.tags.includes(tag.name)
          )
          this.selectedTags = tags
        }
      } catch (error) {
        console.error('加载标签失败:', error)
        this.tagsData = []
        this.selectedTags = []
      }
    },

    // 切换账户类型
    async switchType(type) {
      this.currentType = type
      this.formData.categoryId = ''
      this.formData.tags = []
      this.selectedTags = []
      await this.loadTags()
    },

    // 选择分类
    selectCategory(categoryId) {
      if (categoryId === 'custom' || categoryId === 'custom_income' || categoryId === 'custom_transfer') {
        this.openCustomCategoryDialog()
        return
      }
      this.formData.categoryId = categoryId
    },

    // 打开自定义分类对话框
    openCustomCategoryDialog() {
      this.showCustomCategoryDialog = true
    },

    // 关闭自定义分类对话框
    closeCustomCategoryDialog() {
      this.showCustomCategoryDialog = false
      this.customCategoryForm = {
        name: '',
      }
    },

    // 保存自定义分类
    async saveCustomCategory() {
      if (!this.customCategoryForm.name.trim()) {
        uni.showToast({
          title: '请输入分类名称',
          icon: 'none'
        })
        return
      }

      try {
        // 这里暂时直接选中自定义分类，实际项目中可以保存到本地
        this.formData.categoryId = `custom_${Date.now()}`
        this.closeCustomCategoryDialog()

        uni.showToast({
          title: '自定义分类已选择',
          icon: 'success'
        })
      } catch (error) {
        console.error('保存自定义分类失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    },

    // 金额输入处理
    onAmountInput(e) {
      let value = e.detail.value
      // 限制小数点后两位
      if (value.includes('.')) {
        const parts = value.split('.')
        if (parts[1] && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
          this.formData.amount = value
        }
      }
    },

    // 标签相关方法
    openTagSelector() {
      // 传递当前选中的标签名称数组
      const selectedTagNames = this.selectedTags.map(tag => tag.name)
      const tagValues = encodeURIComponent(JSON.stringify(selectedTagNames))
      uni.navigateTo({
        url: `../tagSelect/index?type=${this.currentType}&value=${tagValues}`,
        events: {
          acceptDataFromOpenedPage: async (data) => {
            console.log(data, "eventBus回调")
            if (data != undefined && data.data != undefined) {
              // 接收选中的标签名称数组
              this.formData.tags = data.data.value || [];
              // 重新加载标签数据以更新显示
              await this.loadTags();
              this.$forceUpdate();
            }
          }
        }
      })
    },

    // 保存记录
    async saveRecord() {
      // 具体的验证提示
      if (!this.formData.amount || !validateAmount(this.formData.amount)) {
        this.$toast('请填写金额')
        return
      }

      if (!this.formData.categoryId) {
        this.$toast('请选择分类')
        return
      }

      try {
        const recordData = {
          type: this.getTypeNumber(this.currentType),
          categoryId: this.formData.categoryId,
          amount: parseFloat(this.formData.amount),
          description: this.formData.note,
          recordTime: Math.floor(new Date(this.formData.date).getTime() / 1000),
          tags: this.selectedTags.map(tag => tag.name) // API需要标签名称数组
        }

        if (this.isEditMode) {
          recordData.id = this.editRecordId
        }

        const _res = await this.$ajax.post('/accounting/record/save', recordData)
        if (_res?.code == 200) {
          uni.showToast({
            title: this.isEditMode ? '更新成功' : '保存成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 300)
        } else {
          throw new Error(_res?.msg || '保存失败')
        }

      } catch (error) {
        console.error('保存记录失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'error'
        })
      }
    },

    // 加载分类数据
    async loadCategories() {
      try {
        const _res = await this.$ajax.get('/accounting/categories')
        if (_res?.code == 200) {
          this.categoriesData = _res.data || []
        }
      } catch (error) {
        console.error('加载分类失败:', error)
        this.categoriesData = []
      }
    },

    // 类型转换方法
    getTypeNumber(typeString) {
      const typeMap = {
        'expense': 1,   // 支出
        'income': 2,    // 入账
        'transfer': 3   // 不计入收入
      }
      return typeMap[typeString] || 1
    },

    // 数字类型转换为字符串类型（用于编辑模式）
    getTypeStringFromNumber(typeNumber) {
      const typeMap = {
        1: 'expense',   // 支出
        2: 'income',    // 入账
        3: 'transfer'   // 不计入收入
      }
      return typeMap[typeNumber] || 'expense'
    },

    // 时间戳转换为日期字符串（用于编辑模式）
    formatDateFromTimestamp(timestamp) {
      const date = new Date(timestamp * 1000) // 转换为毫秒
      return date.toISOString().slice(0, 19).replace('T', ' ') // 格式：YYYY-MM-DD HH:mm:ss
    },

    formatDate,

    // 格式化显示日期
    formatDisplayDate(dateStr) {
      if (!dateStr) return '选择日期'
      return formatDate(dateStr, 'datetime')
    },

    // AI识别提交回调
    handleAiSubmit(recognizedContent) {
      // 将识别结果添加到备注输入框
      if (this.formData.note.trim()) {
        this.formData.note += '\n\n' + recognizedContent
      } else {
        this.formData.note = recognizedContent
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.add-record-container {
  background-color: $background-color;
  min-height: 100vh;
  padding-bottom: 160rpx; // 为悬浮按钮预留空间
}

.amount-section {
  background-color: $card-background;
  padding: $spacing-xl;
  text-align: center;
  box-shadow: $shadow-sm;

  .amount-label {
    font-size: $font-size-md;
    color: $text-secondary;
    margin-bottom: $spacing-md;
  }
}

.detail-section {
  background-color: $card-background;
  margin: $spacing-md;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;

  .detail-item {
    display: flex;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    &:last-child {
      border-bottom: none;
    }

    .detail-label {
      display: flex;
      align-items: center;
      width: 200rpx;
      font-size: $font-size-md;
      color: $text-primary;

      .label-icon {
        margin-right: $spacing-sm;
        font-size: $font-size-lg;
      }
    }

    .detail-value {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: $font-size-md;
      color: $text-secondary;

      .placeholder {
        color: $text-light;
      }

      .arrow {
        color: $text-light;
        font-weight: bold;
      }
    }

    .detail-input {
      flex: 1;
      font-size: $font-size-md;
      color: $text-primary;
      text-align: right;
    }

    .detail-textarea {
      flex: 1;
      font-size: $font-size-md;
      color: $text-primary;
      text-align: left;
      min-height: 80rpx;
      background: transparent;
      border: none;
      outline: none;
      resize: none;
    }

    &.note-item {
      align-items: flex-start;
      min-height: 120rpx;

      .detail-label {
        padding-top: 8rpx;
      }
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      flex: 1;
    }
  }
}

.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: $spacing-lg;
  background-color: $card-background;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;

  .action-buttons {
    display: flex;
    gap: $spacing-md;

    .btn {
      height: 72rpx;
      border-radius: $border-radius-md;
      font-size: 26rpx;
      font-weight: 500;
      transition: all $transition-fast;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 24rpx;
        line-height: 1;
      }

      &.btn-ai {
        flex: 1;
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        border: 1rpx solid rgba(24, 144, 255, 0.2);

        &:active {
          background-color: rgba(24, 144, 255, 0.15);
          transform: scale(0.98);
        }
      }

      &.btn-primary {
        flex: 2;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        color: $white;
        box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);

        &:active {
          background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
          transform: scale(0.98);
          box-shadow: 0 2rpx 12rpx rgba(24, 144, 255, 0.4);
        }

        &:disabled {
          background: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.3);
          box-shadow: none;
          opacity: 0.6;
        }
      }
    }
  }
}



// 自定义分类弹窗样式
.custom-category-dialog {
  background-color: $card-background;
  border-radius: $border-radius-lg;
  overflow: hidden;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .dialog-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }

    .dialog-close {
      font-size: $font-size-xl;
      color: $text-light;
      cursor: pointer;
    }
  }

  .dialog-content {
    padding: $spacing-lg;

    .form-item {
      margin-bottom: $spacing-lg;

      .form-label {
        font-size: $font-size-md;
        color: $text-primary;
        margin-bottom: $spacing-sm;
      }

      .form-input {
        width: 100%;
        padding: $spacing-md;
        border: 1rpx solid $divider-color;
        border-radius: $border-radius-md;
        font-size: $font-size-md;
        color: $text-primary;

        &:focus {
          border-color: $primary-color;
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;

    .btn {
      flex: 1;
    }
  }
}


</style>
