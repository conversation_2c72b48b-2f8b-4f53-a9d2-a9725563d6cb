# 记账系统功能模块

## 📋 项目概述

基于 uniapp + Vue2 开发的完整记账软件功能模块，采用简约大气的设计风格，提供完整的记账、统计、分析功能。

## 🚀 功能特性

### 核心功能
- ✅ **双模式界面**: 会话模式 + 记账模式切换
- ✅ **完整记账流程**: 新建、编辑、删除记账记录
- ✅ **三大账户类型**: 支出、入账、不计入收入
- ✅ **丰富分类系统**: 12个支出分类 + 9个收入分类 + 3个转账分类
- ✅ **标签管理**: 支持按类型分组的标签系统
- ✅ **统计分析**: 多维度数据统计和可视化图表
- ✅ **时间筛选**: 支持今天、本周、本月、本季度、本年、自定义时间范围

### 界面特色
- 🎨 **简约大气设计**: 白色背景，现代化UI风格
- 📱 **响应式布局**: 适配各种屏幕尺寸
- 🌈 **丰富图标**: 每个分类都有独特的emoji图标和颜色
- ⚡ **流畅交互**: 优化的动画效果和用户体验

## 📁 项目结构

```
pages_accounting/
├── index/                  # 首页主界面
│   └── index.vue          # 会话/记账模式切换，最近记录展示
├── add/                   # 新建/编辑记账
│   └── index.vue          # 记账表单，支持新建和编辑模式
├── statistics/            # 统计分析
│   └── index.vue          # 收支统计，图表展示，分类分析
├── tag-manage/            # 标签管理
│   └── index.vue          # 标签的增删改查
├── category-manage/       # 分类管理
│   └── index.vue          # 分类查看和统计
├── record-detail/         # 记录详情
│   └── index.vue          # 单条记录的详细信息
├── data/                  # 数据层
│   ├── categories.js      # 分类配置和工具函数
│   ├── storage.js         # 数据存储管理
│   └── utils.js           # 通用工具函数
├── styles/                # 样式文件
│   └── common.scss        # 通用样式和主题配置
├── components/            # 组件
│   └── test-data.js       # 测试数据生成器
└── README.md              # 项目文档
```

## 🎯 分类系统

### 支出分类 (12个)
- 🍽️ 餐饮 - 日常用餐支出
- 🚗 交通 - 出行交通费用
- 👕 服饰 - 服装购买
- 🛍️ 购物 - 日用品购买
- 🔧 服务 - 各类服务费用
- 📚 教育 - 学习培训支出
- 🎮 娱乐 - 娱乐休闲消费
- ⚽ 运动 - 运动健身支出
- 💡 生活缴费 - 水电煤气等
- ✈️ 旅行 - 旅游度假支出
- 🐕 宠物 - 宠物相关支出
- 🏥 医疗 - 医疗健康支出

### 入账分类 (9个)
- 💼 生意 - 经营收入
- 💰 工资 - 工资收入
- 🎁 奖金 - 奖金收入
- 🧧 其他人情 - 人情收入
- 🧧 收红包 - 红包收入
- 📱 收转账 - 转账收入
- 🏪 商家转账 - 商家付款
- ↩️ 退款 - 退款收入
- 💡 其他 - 其他收入

### 不计入收入分类 (3个)
- 📈 理财 - 投资理财
- 🤝 借还款 - 借贷往来
- 🔄 其他 - 其他转账

## 💾 数据结构

### 记账记录
```javascript
{
  id: string,              // 唯一标识
  type: string,            // 账户类型：expense/income/transfer
  categoryId: string,      // 分类ID
  amount: number,          // 金额（分为单位）
  note: string,            // 备注
  tags: string[],          // 标签ID数组
  date: string,            // 日期 YYYY-MM-DD
  timestamp: number,       // 创建时间戳
  updateTime: number       // 更新时间戳
}
```

### 标签数据
```javascript
{
  id: string,              // 唯一标识
  name: string,            // 标签名称
  color: string,           // 标签颜色
  type: string,            // 所属类型：expense/income/transfer
  createTime: number,      // 创建时间戳
  updateTime: number       // 更新时间戳
}
```

## 🛠️ 技术栈

- **框架**: uniapp + Vue2
- **样式**: SCSS + 响应式设计
- **组件库**: uview-ui + uni-ui
- **存储**: uni.storage (本地存储)
- **图表**: 自定义CSS柱状图
- **图标**: Emoji + 自定义图标

## 📱 页面路由

```javascript
// pages.json 配置
{
  "root": "pages_accounting",
  "pages": [
    { "path": "index/index" },           // 首页
    { "path": "add/index" },             // 新建记账
    { "path": "statistics/index" },      // 统计分析
    { "path": "tag-manage/index" },      // 标签管理
    { "path": "category-manage/index" }, // 分类管理
    { "path": "record-detail/index" }    // 记录详情
  ]
}
```

## 🎨 设计规范

### 色彩系统
- **主色调**: #2979ff (蓝色)
- **收入色**: #52c41a (绿色)
- **支出色**: #ff4d4f (红色)
- **转账色**: #faad14 (橙色)
- **背景色**: #f8f9fa (浅灰)
- **卡片色**: #ffffff (白色)

### 字体规范
- **特大**: 42rpx (标题)
- **大**: 38rpx (重要信息)
- **中**: 30rpx (正文)
- **小**: 26rpx (辅助信息)
- **特小**: 22rpx (次要信息)

### 间距规范
- **特小**: 8rpx
- **小**: 12rpx
- **中**: 16rpx
- **大**: 24rpx
- **特大**: 32rpx
- **超大**: 48rpx

## 🚀 快速开始

1. **导入项目**: 将 `pages_accounting` 文件夹复制到项目根目录
2. **配置路由**: 在 `pages.json` 中添加分包配置
3. **安装依赖**: 确保项目已安装 uview-ui 和相关组件
4. **启动项目**: 运行项目并访问记账功能

## 🧪 测试功能

项目提供了测试数据生成器，可以快速生成测试数据：

```javascript
import { generateTestData, clearAllData } from './components/test-data.js'

// 生成测试数据
generateTestData()

// 清空所有数据
clearAllData()
```

## 📈 功能扩展

### 已实现功能
- ✅ 基础记账功能
- ✅ 分类和标签管理
- ✅ 统计分析
- ✅ 数据可视化
- ✅ 时间筛选

### 未来扩展
- 🔄 数据导入导出
- 🔄 云端同步
- 🔄 预算管理
- 🔄 定期记账提醒
- 🔄 更多图表类型
- 🔄 自定义分类

## 📝 更新日志

### v1.0.0 (2025-07-16)
- ✨ 初始版本发布
- ✨ 完整的记账功能
- ✨ 统计分析功能
- ✨ 标签管理系统
- ✨ 简约大气的UI设计

## 👨‍💻 开发者

**徐静 (parkhansung)**
- 设计并开发了完整的记账系统
- 实现了简约大气的UI设计
- 提供了完善的文档和注释

## 📄 许可证

本项目仅供学习和参考使用。
