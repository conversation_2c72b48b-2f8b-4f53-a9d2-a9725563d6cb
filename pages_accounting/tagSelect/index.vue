<template>
  <view class="tag-select-container">
    <!-- 已选标签 -->
    <view v-if="selectedTags.length > 0" class="selected-section">
      <view class="section-title">已选标签</view>
      <view class="tags-wrap">
        <view
          v-for="tag in selectedTags"
          :key="tag.id"
          class="tag tag-selected"
          @click="unselectTag(tag.name)"
        >
          <text class="tag-text">{{ tag.name }}</text>
          <text class="tag-remove">×</text>
        </view>
      </view>
    </view>

    <!-- 标签列表 -->
    <view class="tags-section">
      <view class="section-header flc">
        <view class="section-title e1">所有标签</view>
        <view class="add-tag-btn" @click="openAddTagDialog">
          <text>+添加</text>
        </view>
      </view>

      <view v-if="allTags.filter(tag => tag.type === getTypeNumber(currentType)).length === 0" class="empty-state fc">
        <text>暂无标签，点击右上角添加</text>
      </view>

      <view v-else class="tags-wrap">
        <view
          v-for="tag in allTags.filter(tag => tag.type === getTypeNumber(currentType))"
          :key="tag.id"
          class="tag"
          :class="{ 'tag-active': isTagSelected(tag.name) }"
          @click="toggleTag(tag)"
        >
          <text class="tag-text">{{ tag.name }}</text>
          <text class="tag-delete" @click.stop="deleteTag(tag)">🗑️</text>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <button class="btn btn-cancel" @click="cancel">取消</button>
      <button class="btn btn-confirm" @click="confirm">
        确定 ({{ selectedTags.length }})
      </button>
    </view>

    <!-- 新建标签弹窗 -->
    <view v-if="showAddDialog" class="dialog-mask fc" @click="closeAddDialog">
      <view class="dialog-content" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">新建标签</text>
          <text class="dialog-close" @click="closeAddDialog">×</text>
        </view>
        <view class="dialog-body">
          <input
            class="tag-input"
            v-model="newTagName"
            placeholder="请输入标签名称"
            maxlength="10"
          />

          <!-- 颜色选择器 -->
          <view class="color-selector">
            <view class="color-label">选择颜色</view>
            <view class="color-options">
              <view
                v-for="color in colorPool"
                :key="color"
                class="color-option"
                :class="{ 'color-selected': selectedColor === color }"
                :style="{ backgroundColor: color }"
                @click="selectColor(color)"
              >
                <text v-if="selectedColor === color" class="color-check">✓</text>
              </view>
            </view>
          </view>
        </view>
        <view class="dialog-footer">
          <button class="btn btn-dialog-cancel" @click="closeAddDialog">取消</button>
          <button
            class="btn btn-dialog-confirm"
            :disabled="!newTagName.trim()"
            @click="saveNewTag"
          >
            保存
          </button>
        </view>
      </view>
    </view>

    <!-- 删除确认弹窗 -->
    <view v-if="showDeleteDialog" class="dialog-mask fc" @click="closeDeleteDialog">
      <view class="dialog-content" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">删除标签</text>
        </view>
        <view class="dialog-body">
          <text>确定要删除标签"{{ deleteTagData && deleteTagData.name }}"吗？</text>
        </view>
        <view class="dialog-footer">
          <button class="btn btn-dialog-cancel" @click="closeDeleteDialog">取消</button>
          <button class="btn btn-dialog-confirm" @click="confirmDelete">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ACCOUNT_TYPES } from '../data/categories.js'

export default {
  name: 'TagSelect',
  data() {
    return {
      currentType: ACCOUNT_TYPES.EXPENSE,
      selectedTagNames: [], // 改为存储标签名称数组
      allTags: [],
      showAddDialog: false,
      showDeleteDialog: false,
      newTagName: '',
      selectedColor: '#1890ff', // 新增：选中的颜色
      deleteTagData: null,
      // 预定义颜色池，用于随机分配
      colorPool: [
        '#1890ff', '#52c41a', '#faad14', '#f5222d',
        '#722ed1', '#13c2c2', '#eb2f96', '#fa541c',
        '#096dd9', '#389e0d', '#d48806', '#cf1322'
      ]
    }
  },

  computed: {
    selectedTags() {
      return this.allTags.filter(tag =>
        this.selectedTagNames.includes(tag.name)
      )
    }
  },

  async onLoad(options) {
    if (options.type) {
      this.currentType = options.type
    }

    if (options.value) {
      try {
        // 解析传入的标签名称数组
        this.selectedTagNames = JSON.parse(decodeURIComponent(options.value)) || []
        console.log('接收到的已选标签名称:', this.selectedTagNames)
      } catch (error) {
        console.error('解析已选标签失败:', error)
        this.selectedTagNames = []
      }
    }

    await this.loadTags()
  },

  methods: {
    // 加载标签数据
    async loadTags() {
      try {
        const _res = await this.$ajax.get('/accounting/tags')
        if (_res?.code == 200) {
          this.allTags = _res.data || []
        } else {
          this.allTags = []
          uni.showToast({
            title: '加载标签失败',
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('加载标签失败:', error)
        this.allTags = []
        uni.showToast({
          title: '加载标签失败',
          icon: 'error'
        })
      }
    },

    // 类型转换方法
    getTypeNumber(typeString) {
      const typeMap = {
        'expense': 1,   // 支出
        'income': 2,    // 入账
        'transfer': 3   // 不计入收入
      }
      return typeMap[typeString] || 1
    },

    // 切换标签选中状态
    toggleTag(tag) {
      if (this.isTagSelected(tag.name)) {
        this.unselectTag(tag.name)
      } else {
        this.selectTag(tag.name)
      }
    },

    selectTag(tagName) {
      if (!this.selectedTagNames.includes(tagName)) {
        this.selectedTagNames.push(tagName)
      }
    },

    unselectTag(tagName) {
      const index = this.selectedTagNames.indexOf(tagName)
      if (index > -1) {
        this.selectedTagNames.splice(index, 1)
      }
    },

    isTagSelected(tagName) {
      return this.selectedTagNames.includes(tagName)
    },

    // 打开新建标签对话框
    openAddTagDialog() {
      this.newTagName = ''
      this.selectedColor = this.colorPool[0] // 默认选择第一个颜色
      this.showAddDialog = true
    },

    closeAddDialog() {
      this.showAddDialog = false
      this.newTagName = ''
      this.selectedColor = this.colorPool[0]
    },

    // 选择颜色
    selectColor(color) {
      this.selectedColor = color
    },

    // 保存新标签
    async saveNewTag() {
      const tagName = this.newTagName.trim()

      if (!tagName) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        })
        return
      }

      // 检查标签是否已存在
      const existingTag = this.allTags.find(tag =>
        tag.name === tagName && tag.type === this.getTypeNumber(this.currentType)
      )

      if (existingTag) {
        uni.showToast({
          title: '标签已存在',
          icon: 'none'
        })
        return
      }

      try {
        const tagData = {
          name: tagName,
          color: this.selectedColor, // 使用用户选择的颜色
          type: this.getTypeNumber(this.currentType)
        }

        const _res = await this.$ajax.post('/accounting/tag/save', tagData)
        if (_res?.code == 200) {
          await this.loadTags()

          // 自动选中新建的标签
          this.selectTag(tagName)

          this.closeAddDialog()

          uni.showToast({
            title: '标签创建成功',
            icon: 'success'
          })
        } else {
          throw new Error(_res?.msg || '创建失败')
        }
      } catch (error) {
        console.error('保存标签失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    },

    // 删除标签
    deleteTag(tag) {
      this.deleteTagData = tag
      this.showDeleteDialog = true
    },

    closeDeleteDialog() {
      this.showDeleteDialog = false
      this.deleteTagData = null
    },

    async confirmDelete() {
      if (!this.deleteTagData) return

      try {
        const _res = await this.$ajax.post('/accounting/tag/delete', { id: this.deleteTagData.id })
        if (_res?.code == 200) {
          // 从已选标签中移除
          this.unselectTag(this.deleteTagData.name)

          await this.loadTags()
          this.closeDeleteDialog()

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } else {
          throw new Error(_res?.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除标签失败:', error)
        uni.showToast({
          title: error.message || '删除失败',
          icon: 'error'
        })
      }
    },

    // 确认选择
    confirm() {
      const eventChannel = this.getOpenerEventChannel()
      if (eventChannel) {
        // 返回选中的标签名称数组
        console.log('返回选中的标签名称:', this.selectedTagNames)
        eventChannel.emit('acceptDataFromOpenedPage', {
          data: {
            value: this.selectedTagNames
          }
        })
      }
      uni.navigateBack()
    },

    // 取消选择
    cancel() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-select-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 160rpx;
}


.selected-section {
  background-color: white;
  margin: 20rpx;
  margin-top: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.tags-section {
  flex: 1;
  margin: 20rpx;

  .section-header {
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .add-tag-btn {
      padding: 16rpx 24rpx;
      background-color: #1890ff;
      color: white;
      border-radius: 20rpx;
      font-size: 26rpx;
      font-weight: 600;
    }
  }

  .empty-state {
    padding: 120rpx 0;
    color: #999;
    font-size: 28rpx;
  }
}

.tags-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 16rpx;

  .tag {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #f5f5f5;
    color: #666;
    border-radius: 26rpx;
    font-size: 26rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;
    line-height: 1;
    min-width: 120rpx;

    .tag-text {
      line-height: 1;
    }

    &.tag-selected {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
      border-color: #1890ff;
      box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);

      .tag-remove {
        margin-left: 12rpx;
        font-weight: bold;
        font-size: 28rpx;
        line-height: 1;
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }
    }

    &.tag-active {
      background-color: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      border-color: #1890ff;
    }

    .tag-delete {
      margin-left: 12rpx;
      padding: 8rpx;
      background-color: rgba(255, 77, 79, 0.1);
      color: #ff4d4f;
      border-radius: 50%;
      font-size: 24rpx;
      line-height: 1;
      transition: all 0.3s ease;

      &:hover {
        background-color: #ff4d4f;
        color: white;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: white;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 20;

  .btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    border: none;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    position: relative;

    &::after {
      border: none !important;
    }

    &.btn-cancel {
      background-color: #f5f5f5;
      color: #666;
      border: none !important;
    }

    &.btn-confirm {
      background-color: #1890ff;
      color: white;
      border: none !important;
    }
  }
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;

  .dialog-content {
    background-color: white;
    border-radius: 20rpx;
    width: 600rpx;
    overflow: hidden;
    margin: 0 30rpx;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 30rpx 20rpx;

    .dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      line-height: 1;
    }

    .dialog-close {
      font-size: 40rpx;
      color: #999;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
    }
  }

  .dialog-body {
    padding: 20rpx 30rpx 40rpx;

    .tag-input {
      width: 100%;
      height: 88rpx;
      border: 2rpx solid #e8e8e8;
      border-radius: 16rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      color: #333;
      background-color: #fafafa;
      line-height: 1;

      &:focus {
        border-color: #1890ff;
        background-color: white;
      }
    }

    text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }

    .color-selector {
      margin-top: 30rpx;

      .color-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 600;
      }

      .color-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .color-option {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          border: 3rpx solid transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          position: relative;

          &.color-selected {
            border-color: #333;
            transform: scale(1.1);
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
          }

          .color-check {
            color: white;
            font-size: 24rpx;
            font-weight: bold;
            text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    gap: 20rpx;
    padding: 0 30rpx 40rpx;

    .btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      border: none;
      font-size: 28rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      position: relative;

      &::after {
        border: none !important;
      }

      &.btn-dialog-cancel {
        background-color: #f5f5f5;
        color: #666;
        border: none !important;
      }

      &.btn-dialog-confirm {
        background-color: #1890ff;
        color: white;
        border: none !important;

        &:disabled {
          background-color: #d9d9d9;
          color: #999;
          border: none !important;
        }
      }
    }
  }
}
</style>