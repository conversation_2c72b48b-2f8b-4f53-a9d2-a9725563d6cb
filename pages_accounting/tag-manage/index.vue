<template>
  <view class="tag-manage-container">
    <!-- 类型切换标签 -->
    <view class="type-tabs">
      <view
        v-for="(config, type) in accountTypeConfigs"
        :key="type"
        class="type-tab"
        :class="{ active: currentType === type }"
        @click="switchType(type)"
      >
        <text class="tab-icon">{{ config.icon }}</text>
        <text class="tab-text">{{ config.name }}</text>
      </view>
    </view>

    <!-- 标签列表 -->
    <view class="tag-list">
      <view v-if="currentTags.length > 0">
        <view
          v-for="tag in currentTags"
          :key="tag.id"
          class="tag-item"
        >
          <view class="tag-info">
            <view class="tag-color" :style="{ backgroundColor: tag.color }"></view>
            <view class="tag-details">
              <view class="tag-name">{{ tag.name }}</view>
              <view class="tag-meta">
                创建于 {{ formatDate(tag.createTime, 'date') }}
              </view>
            </view>
          </view>
          <view class="tag-actions">
            <view class="action-btn edit-btn" @click="editTag(tag)">
              <text class="action-icon">✏️</text>
            </view>
            <view class="action-btn delete-btn" @click="deleteTag(tag)">
              <text class="action-icon">🗑️</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">🏷️</view>
        <view class="empty-text">暂无{{ currentTypeConfig.name }}标签</view>
        <view class="empty-desc">点击右下角按钮添加标签</view>
      </view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab-add" @click="showAddDialog = true">
      <view class="fab-icon">+</view>
    </view>

    <!-- 添加/编辑标签弹窗 -->
    <u-popup v-model="showAddDialog" mode="center">
      <view class="tag-dialog">
        <view class="dialog-header">
          <view class="dialog-title">{{ editingTag ? '编辑标签' : '添加标签' }}</view>
          <view class="dialog-close" @click="closeDialog">✕</view>
        </view>

        <view class="dialog-body">
          <!-- 标签名称 -->
          <view class="input-group">
            <view class="input-label">标签名称</view>
            <input
              class="input-field"
              placeholder="请输入标签名称"
              v-model="tagForm.name"
              maxlength="10"
            />
          </view>

          <!-- 颜色选择 -->
          <view class="input-group">
            <view class="input-label">标签颜色</view>
            <view class="color-picker">
              <view
                v-for="color in colorOptions"
                :key="color"
                class="color-option"
                :class="{ active: tagForm.color === color }"
                :style="{ backgroundColor: color }"
                @click="tagForm.color = color"
              ></view>
            </view>
          </view>

          <!-- 预览 -->
          <view class="input-group">
            <view class="input-label">预览效果</view>
            <view class="tag-preview">
              <view
                class="tag tag-primary"
                :style="{
                  backgroundColor: `rgba(${hexToRgb(tagForm.color)}, 0.1)`,
                  color: tagForm.color
                }"
              >
                {{ tagForm.name || '标签名称' }}
              </view>
            </view>
          </view>
        </view>

        <view class="dialog-actions">
          <button class="btn btn-secondary" @click="closeDialog">取消</button>
          <button
            class="btn btn-primary"
            :disabled="!tagForm.name.trim()"
            @click="saveTag"
          >
            {{ editingTag ? '保存' : '添加' }}
          </button>
        </view>
      </view>
    </u-popup>

    <!-- 删除确认弹窗 -->
    <u-popup v-model="showDeleteDialog" mode="center">
      <view class="delete-dialog">
        <view class="dialog-header">
          <view class="dialog-title">删除标签</view>
        </view>

        <view class="dialog-body">
          <view class="delete-warning">
            <view class="warning-icon">⚠️</view>
            <view class="warning-text">
              确定要删除标签"{{ deletingTag && deletingTag.name }}"吗？
            </view>
            <view class="warning-desc">
              删除后将从所有相关记录中移除该标签，此操作不可恢复。
            </view>
          </view>
        </view>

        <view class="dialog-actions">
          <button class="btn btn-secondary" @click="showDeleteDialog = false">取消</button>
          <button class="btn btn-danger" @click="confirmDelete">删除</button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { ACCOUNT_TYPES, ACCOUNT_TYPE_CONFIG } from '../data/categories.js'
import { formatDate, generateRandomColor } from '../data/utils.js'

export default {
  name: 'TagManage',
  data() {
    return {
      currentType: ACCOUNT_TYPES.EXPENSE,
      currentTags: [],
      showAddDialog: false,
      showDeleteDialog: false,
      editingTag: null,
      deletingTag: null,
      tagForm: {
        name: '',
        color: '#1890ff'
      },
      colorOptions: [
        '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
        '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb',
        '#ff7875', '#40a9ff', '#b37feb', '#ff9c6e', '#36cfc9',
        '#73d13d', '#ffb347', '#95de64', '#ffd666', '#87e8de'
      ]
    }
  },

  computed: {
    accountTypeConfigs() {
      return ACCOUNT_TYPE_CONFIG
    },

    currentTypeConfig() {
      return ACCOUNT_TYPE_CONFIG[this.currentType]
    }
  },

  async onLoad() {
    await this.loadTags()
  },

  async onShow() {
    await this.loadTags()
  },

  methods: {
    // 加载标签
    async loadTags() {
      try {
        const _res = await this.$ajax.get('/accounting/tags', { type: this.getTypeNumber(this.currentType) })
        if (_res?.code == 200) {
          this.currentTags = _res.data || []
        } else {
          this.currentTags = []
        }
      } catch (error) {
        console.error('加载标签失败:', error)
        this.currentTags = []
        uni.showToast({
          title: '加载标签失败',
          icon: 'error'
        })
      }
    },

    // 切换类型
    async switchType(type) {
      this.currentType = type
      await this.loadTags()
    },

    // 类型转换方法
    getTypeNumber(typeString) {
      const typeMap = {
        'expense': 1,   // 支出
        'income': 2,    // 入账
        'transfer': 3   // 不计入收入
      }
      return typeMap[typeString] || 1
    },

    // 编辑标签
    editTag(tag) {
      this.editingTag = tag
      this.tagForm = {
        name: tag.name,
        color: tag.color
      }
      this.showAddDialog = true
    },

    // 删除标签
    deleteTag(tag) {
      this.deletingTag = tag
      this.showDeleteDialog = true
    },

    // 确认删除
    async confirmDelete() {
      if (!this.deletingTag) return

      try {
        const _res = await this.$ajax.post('/accounting/tag/delete', { id: this.deletingTag.id })
        if (_res?.code == 200) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })

          this.showDeleteDialog = false
          this.deletingTag = null
          await this.loadTags()
        } else {
          throw new Error(_res?.msg || '删除失败')
        }

      } catch (error) {
        console.error('删除标签失败:', error)
        uni.showToast({
          title: error.message || '删除失败',
          icon: 'error'
        })
      }
    },

    // 保存标签
    async saveTag() {
      if (!this.tagForm.name.trim()) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        })
        return
      }

      try {
        const tagData = {
          name: this.tagForm.name.trim(),
          color: this.tagForm.color,
          type: this.getTypeNumber(this.currentType)
        }

        if (this.editingTag) {
          tagData.id = this.editingTag.id
        }

        const _res = await this.$ajax.post('/accounting/tag/save', tagData)
        if (_res?.code == 200) {
          uni.showToast({
            title: this.editingTag ? '保存成功' : '添加成功',
            icon: 'success'
          })

          this.closeDialog()
          await this.loadTags()
        } else {
          throw new Error(_res?.msg || '保存失败')
        }

      } catch (error) {
        console.error('保存标签失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'error'
        })
      }
    },

    // 关闭弹窗
    closeDialog() {
      this.showAddDialog = false
      this.editingTag = null
      this.tagForm = {
        name: '',
        color: '#1890ff'
      }
    },

    // 十六进制颜色转RGB
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      if (result) {
        const r = parseInt(result[1], 16)
        const g = parseInt(result[2], 16)
        const b = parseInt(result[3], 16)
        return `${r}, ${g}, ${b}`
      }
      return '24, 144, 255' // 默认蓝色
    },

    formatDate
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.tag-manage-container {
  background-color: $background-color;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.type-tabs {
  background-color: $card-background;
  display: flex;
  box-shadow: $shadow-nav;

  .type-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-md;
    transition: all 0.2s ease;

    .tab-icon {
      font-size: 36rpx;
      margin-bottom: $spacing-xs;
    }

    .tab-text {
      font-size: $font-size-sm;
      color: $text-secondary;
    }

    &.active {
      background-color: rgba($primary-color, 0.1);

      .tab-icon,
      .tab-text {
        color: $primary-color;
      }
    }
  }
}

.tag-list {
  padding: $spacing-md;

  .tag-item {
    background-color: $card-background;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-sm;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tag-info {
      display: flex;
      align-items: center;
      flex: 1;

      .tag-color {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: $spacing-md;
      }

      .tag-details {
        .tag-name {
          font-size: $font-size-md;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4rpx;
        }

        .tag-meta {
          font-size: $font-size-xs;
          color: $text-light;
        }
      }
    }

    .tag-actions {
      display: flex;
      gap: $spacing-sm;

      .action-btn {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        .action-icon {
          font-size: 32rpx;
        }

        &.edit-btn {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;

          &:active {
            background-color: rgba($primary-color, 0.2);
          }
        }

        &.delete-btn {
          background-color: rgba($error-color, 0.1);
          color: $error-color;

          &:active {
            background-color: rgba($error-color, 0.2);
          }
        }
      }
    }
  }
}

// 弹窗样式
.tag-dialog,
.delete-dialog {
  background-color: $card-background;
  border-radius: $border-radius-lg;
  width: 600rpx;
  max-width: 90vw;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .dialog-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }

    .dialog-close {
      font-size: $font-size-xl;
      color: $text-light;
      cursor: pointer;
    }
  }

  .dialog-body {
    padding: $spacing-lg;
  }

  .dialog-actions {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;

    .btn {
      flex: 1;
    }
  }
}

.color-picker {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: $spacing-sm;
  margin-top: $spacing-sm;

  .color-option {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 4rpx solid transparent;
    transition: all 0.2s ease;

    &.active {
      border-color: $text-primary;
      transform: scale(1.1);
    }
  }
}

.tag-preview {
  margin-top: $spacing-sm;
}

.delete-warning {
  text-align: center;

  .warning-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-md;
  }

  .warning-text {
    font-size: $font-size-md;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  .warning-desc {
    font-size: $font-size-sm;
    color: $text-light;
    line-height: 1.5;
  }
}
</style>
