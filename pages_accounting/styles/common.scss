/**
 * @Description: 记账系统通用样式 - 简约大气设计
 * @Author: 徐静(parkhansung)
 * @Date: 2025-07-16
 */

// 继承记忆系统的基础样式变量
@import "../../pages_memory/styles/common.scss";

// 记账系统专用色彩变量
$income-color: #52c41a;        // 收入绿色
$expense-color: #ff4d4f;       // 支出红色
$transfer-color: #faad14;      // 转账/不计入收入橙色
$balance-color: #1890ff;       // 结余蓝色
$error-color: #ff4d4f;         // 错误/删除红色

// 记账分类图标颜色
$category-food: #ff7875;       // 餐饮
$category-transport: #40a9ff;  // 交通
$category-shopping: #b37feb;   // 购物
$category-service: #36cfc9;    // 服务
$category-education: #73d13d;  // 教育
$category-entertainment: #ffb347; // 娱乐
$category-medical: #ff85c0;    // 医疗
$category-salary: #52c41a;     // 工资
$category-business: #1890ff;   // 生意
$category-bonus: #faad14;      // 奖金
$category-investment: #722ed1; // 理财

$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 记账专用组件样式
.accounting-container {
  background-color: $background-color;
  min-height: 100vh;
}

// 顶部切换标签样式
.mode-tabs {
  background-color: $card-background;
  box-shadow: $shadow-nav;
  padding: $spacing-md;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $spacing-xl;

  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-sm;
    border-radius: $border-radius-lg;
    transition: all 0.2s ease;
    cursor: pointer;

    .tab-icon {
      font-size: 48rpx;
      margin-bottom: $spacing-xs;
      transition: all 0.2s ease;
    }

    .tab-text {
      font-size: $font-size-sm;
      color: $text-secondary;
      transition: all 0.2s ease;
    }

    &.active {
      background-color: rgba($primary-color, 0.1);

      .tab-icon {
        color: $primary-color;
        transform: scale(1.1);
      }

      .tab-text {
        color: $primary-color;
        font-weight: 600;
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 记账记录卡片样式
.record-card {
  @extend .card;
  margin: $spacing-md;

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-md;
    border-bottom: 1rpx solid $divider-color;

    .record-category {
      display: flex;
      align-items: center;

      .category-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: $spacing-md;
        font-size: 36rpx;
        color: white;
      }

      .category-info {
        .category-name {
          font-size: $font-size-md;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 4rpx;
        }

        .category-sub {
          font-size: $font-size-xs;
          color: $text-light;
        }
      }
    }

    .record-amount {
      text-align: right;

      .amount-value {
        font-size: $font-size-lg;
        font-weight: 700;
        margin-bottom: 4rpx;

        &.income {
          color: $income-color;
        }

        &.expense {
          color: $expense-color;
        }

        &.transfer {
          color: $transfer-color;
        }
      }

      .amount-time {
        font-size: $font-size-xs;
        color: $text-light;
      }
    }
  }

  .record-body {
    padding: $spacing-md;

    .record-note {
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.5;
      margin-bottom: $spacing-sm;
    }

    .record-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
    }
  }
}

// 统计卡片样式
.stats-card {
  @extend .card;
  margin: $spacing-md;

  .stats-header {
    padding: $spacing-md;
    border-bottom: 1rpx solid $divider-color;
    text-align: center;

    .stats-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }

    .stats-period {
      font-size: $font-size-sm;
      color: $text-light;
    }
  }

  .stats-body {
    padding: $spacing-lg;

    .stats-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;

      &:last-child {
        margin-bottom: 0;
      }

      .stats-label {
        font-size: $font-size-md;
        color: $text-secondary;
      }

      .stats-value {
        font-size: $font-size-lg;
        font-weight: 700;

        &.income {
          color: $income-color;
        }

        &.expense {
          color: $expense-color;
        }

        &.balance {
          color: $balance-color;
        }
      }
    }
  }
}

// 分类选择器样式
.category-selector {
  .category-tabs {
    display: flex;
    background-color: $card-background;
    border-radius: $border-radius-lg;
    padding: 8rpx;
    margin: $spacing-md;
    box-shadow: $shadow-sm;

    .category-tab {
      flex: 1;
      text-align: center;
      padding: $spacing-sm;
      border-radius: $border-radius-md;
      font-size: $font-size-sm;
      color: $text-secondary;
      transition: all 0.2s ease;

      &.active {
        background-color: $primary-color;
        color: white;
        font-weight: 600;
      }
    }
  }

  .category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-md;
    padding: $spacing-md;

    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: $spacing-sm;
      border-radius: $border-radius-lg;
      background-color: $card-background;
      box-shadow: $shadow-sm;
      transition: all 0.2s ease;
      min-height: 140rpx; // 增加高度适应更大的图标

      .category-icon {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: $spacing-xs;
        font-size: 36rpx;
        color: white;
        opacity: 0.9;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;
      }

      .category-name {
        font-size: $font-size-xs;
        color: $text-primary;
        text-align: center;
        line-height: 1.2;
      }

      &.active {
        transform: scale(1.05);
        background-color: rgba($primary-color, 0.1);
        border: 3rpx solid $primary-color;
        box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.3);

        .category-icon {
          transform: scale(1.1);
          opacity: 1;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
        }

        .category-name {
          color: $primary-color;
          font-weight: 600;
        }
      }

      &:active {
        transform: scale(0.9);
      }
    }
  }
}

// 浮动添加按钮样式
.fab-add {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: $primary-color;
  box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: all 0.2s ease;

  .fab-icon {
    font-size: 48rpx;
    color: white;
  }

  &:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.4);
  }
}

// 日历组件样式增强
.calendar-container {
  background-color: $card-background;
  border-radius: $border-radius-lg;
  margin: $spacing-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

// 金额输入样式
.amount-input {
  text-align: center;
  font-size: 72rpx;
  font-weight: 700;
  color: $text-primary;
  border: none;
  background: transparent;
  padding: $spacing-lg;

  &.income {
    color: $income-color;
  }

  &.expense {
    color: $expense-color;
  }

  &.transfer {
    color: $transfer-color;
  }
}

// 时间范围选择器样式
.time-range-selector {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
  padding: $spacing-md;
  background-color: $card-background;
  border-radius: $border-radius-lg;
  margin: $spacing-md;
  box-shadow: $shadow-sm;

  .time-range-item {
    padding: $spacing-xs $spacing-md;
    border-radius: $border-radius-xl;
    font-size: $font-size-sm;
    color: $text-secondary;
    background-color: $background-color;
    border: 1rpx solid $border-color;
    transition: all 0.2s ease;

    &.active {
      background-color: $primary-color;
      color: white;
      border-color: $primary-color;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}
