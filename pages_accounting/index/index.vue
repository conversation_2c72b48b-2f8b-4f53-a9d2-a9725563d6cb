<template>
  <view class="accounting-content">
    <!-- 日历组件 -->
    <view class="calendar-container">
      <xj-calendar
        @on-click="onDateChange"
        @month-change="onMonthChange"
        :open="false"
        enableDragSelect
        :signeddates="getSignedDates()"
      />
    </view>

    <!-- 最近记录列表 -->
    <view class="recent-records">
      <view class="section-header">
        <view class="section-title">最近记录</view>
        <view class="header-actions">
          <!-- 筛选按钮 -->
          <view class="filter-tabs">
            <view
              class="filter-tab"
              :class="{ active: currentFilter === 'all' }"
              @click="setFilter('all')"
            >
              <text class="filter-text">全部</text>
            </view>
            <view
              class="filter-tab"
              :class="{ active: currentFilter === 'income' }"
              @click="setFilter('income')"
            >
              <text class="filter-text">收入</text>
            </view>
            <view
              class="filter-tab"
              :class="{ active: currentFilter === 'expense' }"
              @click="setFilter('expense')"
            >
              <text class="filter-text">支出</text>
            </view>
            <view class="filter-more" @click="goToStatistics">
              <text class="more-icon">⋯</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间线记录列表 -->
      <view v-if="groupedRecords.length > 0" class="timeline-list">
        <view v-for="(group, groupIndex) in groupedRecords" :key="group.date" class="timeline-group">
          <!-- 日期标题 -->
          <view class="timeline-date">
            <view class="date-content">
              <text class="date-text">{{ group.dateText }}</text>
              <text class="date-label" :class="getDateLabelClass(group.dateLabel)">{{ group.dateLabel }}</text>
            </view>
            <view class="date-line"></view>
            <view class="date-summary">
              <text v-if="group.totalExpense > 0" class="expense-text">💸￥{{ group.totalExpense.toFixed(2) }}</text>
              <text v-if="group.totalIncome > 0" class="income-text">💰￥{{ group.totalIncome.toFixed(2) }}</text>
            </view>
          </view>

          <!-- 该日期的记录 -->
          <view class="timeline-records">
            <view
              v-for="(record, recordIndex) in group.records"
              :key="record.id"
              class="timeline-item"
              :class="{ 'last-item': recordIndex === group.records.length - 1 && groupIndex === groupedRecords.length - 1 }"
              @click="goToRecordDetail(record)"
            >
              <!-- 时间线节点 -->
              <view class="timeline-node">
                <view class="node-dot" :class="record.type" :style="{ backgroundColor: record.categoryColor }">
                  <text class="dot-emoji">{{ record.categoryEmoji }}</text>
                </view>
                <view class="node-line" v-if="!(recordIndex === group.records.length - 1 && groupIndex === groupedRecords.length - 1)"></view>
              </view>

              <!-- 记录内容 -->
              <view class="timeline-content">
                <view class="record-main">
                  <view class="record-info">
                    <view class="record-title-row">
                      <text class="record-title">{{ record.categoryName }}</text>
                      <view class="record-tags" v-if="record.tags && record.tags.length > 0">
                        <view
                          v-for="tag in record.tags"
                          :key="tag.id"
                          class="tag-item"
                          :style="{ backgroundColor: tag.color + '20', borderColor: tag.color }"
                        >
                          <text class="tag-text" :style="{ color: tag.color }">{{ tag.name }}</text>
                        </view>
                      </view>
                    </view>
                    <view class="record-note" v-if="record.note">{{ record.note }}</view>
                  </view>
                  <view class="record-amount" :class="record.type">
                    <text class="amount-sign">{{ record.type === 'expense' ? '-' : record.type === 'income' ? '+' : '' }}</text>
                    <text class="currency-symbol">￥</text>{{ formatAmountNumber(record.amount, record.type) }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载中提示 -->
        <view v-if="isLoading" class="loading-tip">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多数据提示 -->
        <view v-if="!hasMoreData && groupedRecords.length > 0" class="no-more-tip">
          <text class="no-more-text">没有更多记录了</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <view class="empty-icon">📝</view>
        <view class="empty-text">还没有记账记录</view>
        <view class="empty-desc">点击上方按钮快速记账</view>
      </view>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab-add" @click="goToAdd">
      <view class="fab-icon">+</view>
    </view>
  </view>
</template>

<script>

import { formatAmount, formatAmountWithSign, formatDate } from '../data/utils.js'
import xjCalendar from '@/components/xj-calendar/xj-calendar.vue'

export default {
  name: 'AccountingIndex',
  components: {
    xjCalendar
  },
  data() {
    return {
      selectedDate: formatDate(Date.now()),
      recentRecords: [],
      mockRecords: [],
      currentFilter: 'all', // 当前筛选类型：all, income, expense
      currentMonth: formatDate(Date.now()).substring(0, 7), // 当前月份 YYYY-MM
      signedDatesData: [], // 有记录的日期列表
      isLoading: false, // 加载状态
      currentPage: 1, // 当前页码
      pageSize: 20, // 每页条数
      total: 0,
      hasMoreData: true // 是否还有更多数据
    }
  },

  computed: {
    // 筛选后的记录
    filteredRecords() {
      const records = this.recentRecords.length > 0 ? this.recentRecords : []
      if (this.currentFilter === 'all') {
        return records
      }
      return records.filter(record => record.type === this.currentFilter)
    },

    // 按日期分组的记录
    groupedRecords() {
      const groups = new Map()
      const today = new Date()
      const todayStr = formatDate(today)
      const yesterdayStr = formatDate(new Date(today.getTime() - 86400000))

      // 按日期分组
      this.filteredRecords.forEach(record => {
        const date = record.date
        if (!groups.has(date)) {
          groups.set(date, {
            date: date,
            dateText: date,
            dateLabel: this.getDateLabel(date, todayStr, yesterdayStr),
            records: [],
            totalIncome: 0,
            totalExpense: 0
          })
        }

        const group = groups.get(date)
        group.records.push(record)

        // 计算当日收支
        const amount = record.amount / 100
        if (record.type === 'income') {
          group.totalIncome += amount
        } else if (record.type === 'expense') {
          group.totalExpense += amount
        }
      })

      // 转换为数组并按日期排序（最新的在前）
      return Array.from(groups.values()).sort((a, b) => {
        return new Date(b.date) - new Date(a.date)
      })
    }
  },

  watch: {
    // 监听筛选类型变化
    currentFilter: {
      handler(newVal, oldVal) {
        // 避免初始化时触发，只在真正变化时重新加载
        if (oldVal !== undefined && newVal !== oldVal) {
          this.currentPage = 1
          this.hasMoreData = true
          this.loadRecentRecords()
        }
      }
    }
  },

  onShow() {
    this.loadRecentRecords()
    this.loadMonthDates()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  // 上拉加载
  onReachBottom() {
    console.log('onReachBottom 被调用')
    this.loadMoreRecords()
  },

  methods: {
    // 日历日期变化处理
    async onDateChange(date) {
      console.log("🚀 -> onDateChange -> date:", date)
      this.selectedDate = date

      // 检查是否切换了月份
      const newMonth = date.substring(0, 7)
      if (newMonth !== this.currentMonth) {
        this.currentMonth = newMonth
        await this.loadMonthDates()
      }

      // 根据选择的日期筛选记录
      await this.filterRecordsByDate(date)
    },

    // 获取有记录的日期标记
    getSignedDates() {
      const signedDates = []

      this.signedDatesData.forEach(date => {
        signedDates.push({
          date: date,
          color: '#007AFF' // 使用主题色标记有记录的日期
        })
      })

      return signedDates
    },

    // 加载指定月份有记录的日期列表
    async loadMonthDates(monthParam) {
      try {
        const month = monthParam || this.currentMonth
        const _res = await this.$ajax.get('/accounting/month/dates', {
          month: month
        })

        if (_res?.code == 200) {
          this.signedDatesData = _res.data || []
          if (monthParam) this.currentMonth = monthParam
        } else {
          console.error('获取月份日期失败:', _res?.msg)
          this.signedDatesData = []
        }
      } catch (error) {
        console.error('加载月份日期失败:', error)
        this.signedDatesData = []
      }
    },

    // 月份切换事件处理
    onMonthChange(monthParam) {
      this.loadMonthDates(monthParam)
    },

    // 加载最近记录
    async loadRecentRecords() {
      if (this.isLoading) return

      try {
        this.isLoading = true

        const _res = await this.$ajax.get('/accounting/record/list', {
          type: this.currentFilter, // 筛选类型：income, expense, all
          page: this.currentPage,
          pageSize: this.pageSize
        })

        if (_res?.code == 200) {
          this.total = _res?.count || 0;
          const records = _res.data || []
          console.log('loadRecentRecords 响应', {
            total: this.total,
            currentRecords: records.length,
            currentPage: this.currentPage,
            pageSize: this.pageSize
          })

          // 转换数据格式以适配现有组件
          const formattedRecords = records.map(record => ({
            id: record.id,
            type: this.getTypeString(record.type), // 转换为字符串类型
            categoryId: record.categoryId,
            categoryName: record.categoryName,
            categoryEmoji: record.categoryEmoji,
            categoryColor: record.categoryColor,
            amount: Math.round(parseFloat(record.amount) * 100), // 转换为分
            note: record.description || '',
            date: formatDate(record.recordTime * 1000), // 时间戳转日期
            timestamp: record.recordTime * 1000,
            updateTime: record.createTime * 1000,
            tags: record.tags || [] // 保留完整的tags数据
          }))

          if (this.currentPage === 1) {
            this.recentRecords = formattedRecords
          } else {
            this.recentRecords = [...this.recentRecords, ...formattedRecords]
          }

          // 检查是否还有更多数据
          this.hasMoreData = formattedRecords.length === this.pageSize
          console.log('hasMoreData 计算', {
            hasMoreData: this.hasMoreData,
            formattedRecordsLength: formattedRecords.length,
            pageSize: this.pageSize,
            totalRecordsLoaded: this.recentRecords.length
          })

        } else {
          console.error('获取记录列表失败:', _res?.msg)
          if (this.currentPage === 1) {
            this.recentRecords = []
          }
        }

      } catch (error) {
        console.error('加载记录失败:', error)
        if (this.currentPage === 1) {
          this.recentRecords = []
        }
      } finally {
        this.isLoading = false
      }
    },

    // 根据日期筛选记录
    async filterRecordsByDate(dateStr) {
      console.log('选择日期:', dateStr)

      try {
        this.isLoading = true

        const _res = await this.$ajax.get('/accounting/record/list', {
          type: this.currentFilter,
          dates: dateStr, // 传入选中的日期
          page: 1,
          pageSize: this.pageSize
        })

        if (_res?.code == 200) {
          this.total = _res?.count || 0;
          const records = _res.data || []

          // 转换数据格式
          const formattedRecords = records.map(record => ({
            id: record.id,
            type: this.getTypeString(record.type),
            categoryId: record.categoryId,
            categoryName: record.categoryName,
            categoryEmoji: record.categoryEmoji,
            categoryColor: record.categoryColor,
            amount: Math.round(parseFloat(record.amount) * 100),
            note: record.description || '',
            date: formatDate(record.recordTime * 1000),
            timestamp: record.recordTime * 1000,
            updateTime: record.createTime * 1000,
            tags: record.tags || [] // 保留完整的tags数据
          }))

          this.recentRecords = formattedRecords
          this.currentPage = 1
          this.hasMoreData = formattedRecords.length === this.pageSize

        } else {
          console.error('筛选记录失败:', _res?.msg)
          this.recentRecords = []
        }

      } catch (error) {
        console.error('筛选记录失败:', error)
        this.recentRecords = []
      } finally {
        this.isLoading = false
      }
    },

    // 设置筛选类型
    async setFilter(type) {
      this.currentFilter = type
      this.currentPage = 1
      this.hasMoreData = true
      await this.loadRecentRecords()
    },

    // 快捷添加记账
    quickAdd(type) {
      uni.navigateTo({
        url: `/pages_accounting/add/index?type=${type}`
      })
    },

    // 跳转到新建记账
    goToAdd() {
      uni.navigateTo({
        url: '/pages_accounting/add/index'
      })
    },

    // 跳转到记账列表页面
    goToStatistics() {
      uni.navigateTo({
        url: '/pages_accounting/list/index'
      })
    },

    // 跳转到记录详情
    goToRecordDetail(record) {
      uni.navigateTo({
        url: `/pages_accounting/record-detail/index?id=${record.id}`
      })
    },

    // 获取日期标签
    getDateLabel(date, todayStr, yesterdayStr) {
      if (date === todayStr) {
        return '今天'
      } else if (date === yesterdayStr) {
        return '昨天'
      } else {
        // 计算是星期几
        const dateObj = new Date(date)
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        return dayNames[dateObj.getDay()]
      }
    },

    // 获取日期标签的样式class
    getDateLabelClass(label) {
      if (label === '今天') {
        return 'today-label'
      } else if (label === '昨天') {
        return 'yesterday-label'
      } else {
        return 'weekday-label'
      }
    },

    // 格式化金额数字（不带符号）
    formatAmountNumber(amount, type) {
      const value = (amount / 100).toFixed(2)
      return type === 'expense' ? value : value
    },

    // 类型转换方法：数字转字符串
    getTypeString(typeNumber) {
      const typeMap = {
        1: 'expense',   // 支出
        2: 'income',    // 收入
        3: 'transfer'   // 不计入收入
      }
      return typeMap[typeNumber] || 'expense'
    },

    // 加载更多数据
    async loadMoreRecords() {
      console.log('loadMoreRecords 调用', {
        hasMoreData: this.hasMoreData,
        isLoading: this.isLoading,
        currentPage: this.currentPage,
        totalRecords: this.recentRecords.length,
        total: this.total
      })

      if (!this.hasMoreData || this.isLoading) return

      this.currentPage += 1
      await this.loadRecentRecords()
    },

    // 刷新数据
    async refreshData() {
      this.currentPage = 1
      this.hasMoreData = true
      await Promise.all([
        this.loadRecentRecords(),
        this.loadMonthDates()
      ])
    },

    // 工具方法
    formatAmount,
    formatAmountWithSign,
    formatDate
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/common.scss";

.accounting-content {
  background-color: #ffffff; // 纯白背景
  min-height: 100vh;
  padding-bottom: 200rpx; // 为浮动按钮留出空间
  margin-top: 10rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg 0 $spacing-md;

  .section-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-primary;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: $spacing-md;
  }

  .filter-tabs {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .filter-tab {
      padding: $spacing-xs $spacing-md;
      border-radius: $border-radius-xl;
      font-size: $font-size-sm;
      color: $text-secondary;
      background-color: #f8f9fa;
      border: 1rpx solid #e9ecef;
      transition: all 0.2s ease;
      min-width: 60rpx;
      text-align: center;

      .filter-text {
        font-size: $font-size-sm;
        font-weight: 500;
      }

      &.active {
        background-color: $primary-color;
        color: white;
        border-color: $primary-color;

        .filter-text {
          color: white;
          font-weight: 600;
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .filter-more {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: #f8f8f8;
      transition: all 0.2s ease;

      .more-icon {
        font-size: 32rpx;
        color: $text-secondary;
        font-weight: bold;
        line-height: 1;
      }

      &:active {
        background-color: #e8e8e8;
        transform: scale(0.95);
      }
    }
  }
}

// 日历容器样式
.calendar-container {
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid $border-color;
}

// 记录列表样式
.recent-records {
  margin: $spacing-md;
  background-color: #ffffff;
}

// 时间线样式
.timeline-list {
  padding: $spacing-md 0;

  .timeline-group {
    margin-bottom: $spacing-lg;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timeline-date {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-md;
    position: relative;

    .date-content {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      background-color: $background-color;
      padding: 0 $spacing-sm;

      .date-text {
        font-size: $font-size-md;
        font-weight: 600;
        color: $text-primary;
      }

      .date-label {
        font-size: $font-size-xs;
        padding: 4rpx 8rpx;
        border-radius: $border-radius-sm;
        font-weight: 500;

        &.today-label {
          color: $primary-color;
          background-color: rgba($primary-color, 0.1);
        }

        &.yesterday-label {
          color: $warning-color;
          background-color: rgba($warning-color, 0.1);
        }

        &.weekday-label {
          color: $text-light;
          background-color: #f0f0f0;
        }
      }
    }

    .date-line {
      flex: 1;
      height: 1rpx;
      background-color: $divider-color;
      margin: 0 $spacing-md;
    }

    .date-summary {
      display: flex;
      gap: $spacing-sm;
      font-size: $font-size-xs;

      .expense-text {
        color: $expense-color;
        font-weight: 600;
      }

      .income-text {
        color: $income-color;
        font-weight: 600;
      }
    }
  }

  .timeline-records {
    .timeline-item {
      display: flex;
      align-items: flex-start; // 确保顶部对齐
      margin-bottom: $spacing-sm;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;

        .timeline-content {
          border-bottom: none;
        }
      }

      &:active {
        .timeline-content {
          background-color: #f8f8f8;
        }
      }

      .timeline-node {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: $spacing-md;
        position: relative;
        padding-top: 8rpx; // 微调顶部间距

        .node-dot {
          width: 56rpx;
          height: 56rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          flex-shrink: 0;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

          .dot-emoji {
            font-size: 32rpx;
            line-height: 1;
          }
        }

        .node-line {
          width: 2rpx;
          flex: 1;
          background-color: $divider-color;
          margin-top: 8rpx;
          min-height: 60rpx;
        }
      }

      .timeline-content {
        flex: 1;
        background-color: transparent;
        padding: $spacing-sm 0;
        border-bottom: 1rpx solid #f8f8f8;

        .record-main {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .record-info {
            flex: 1;
            margin-right: $spacing-md;

            .record-title-row {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              gap: 8rpx;
              margin-bottom: 4rpx;

              .record-title {
                font-size: $font-size-md;
                font-weight: 500;
                color: $text-primary;
                flex-shrink: 0;
              }

              .record-tags {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 6rpx;

                .tag-item {
                  padding: 2rpx 8rpx;
                  border-radius: 12rpx;
                  border: 1rpx solid;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .tag-text {
                    font-size: 20rpx;
                    font-weight: 500;
                    line-height: 1.2;
                  }
                }
              }
            }

            .record-note {
              font-size: $font-size-sm;
              color: $text-secondary;
              line-height: 1.4;
            }
          }

          .record-amount {
            font-size: $font-size-lg;
            font-weight: 700;
            display: flex;
            align-items: baseline;

            .amount-sign {
              font-size: $font-size-md;
              margin-right: 2rpx;
            }

            .currency-symbol {
              font-size: $font-size-sm;
              font-weight: 400;
              margin-right: 2rpx;
            }

            &.income {
              color: $income-color;
            }

            &.expense {
              color: $expense-color;
            }

            &.transfer {
              color: $transfer-color;
            }
          }
        }
      }

      &.last-item {
        .timeline-node .node-line {
          display: none;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-xxl;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: $spacing-lg;
  }

  .empty-text {
    font-size: $font-size-lg;
    color: $text-secondary;
    margin-bottom: $spacing-sm;
  }

  .empty-desc {
    font-size: $font-size-sm;
    color: $text-light;
  }
}

// 加载提示样式
.loading-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-lg;
  margin-top: $spacing-md;

  .loading-text {
    font-size: $font-size-sm;
    color: $text-secondary;
  }
}

.no-more-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-lg;
  margin-top: $spacing-md;

  .no-more-text {
    font-size: $font-size-xs;
    color: $text-light;
  }
}

.fab-add {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.3);
  z-index: 100;
  transition: all 0.2s ease;

  .fab-icon {
    font-size: 48rpx;
    color: white;
    font-weight: 300;
  }

  &:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.4);
  }
}
</style>
