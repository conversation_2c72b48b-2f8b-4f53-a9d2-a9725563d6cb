# 首页统计接口实现说明

## 实现概述
根据提供的接口文档，我已经在首页(`pages/index/index.vue`)中集成了记账模块和待办事项模块的统计接口调用，实现了数据的实时获取和显示。

## 实现的功能

### 1. 记账模块统计接口集成
- **接口地址**: `GET /accounting/home/<USER>
- **实现方法**: `loadAccountingStats()`
- **获取数据**:
  - `monthExpense`: 本月支出金额
  - `monthIncome`: 本月收入金额
  - `totalRecords`: 记账总数

### 2. 待办事项模块统计接口集成
- **接口地址**: `GET /task/home/<USER>
- **实现方法**: `loadTaskStats()`
- **获取数据**:
  - `totalTasks`: 待办总数
  - `unfinishedTasks`: 未完成数量
  - `completedTasks`: 已完成数量
  - `highUrgentTasks`: 高和紧急级别数量
  - `expiringSoonTasks`: 2天内到期数量

## 核心方法说明

### loadStats()
主方法，并行调用两个模块的统计接口，提高页面加载性能：
```javascript
async loadStats() {
  try {
    // 并行请求记账模块和待办事项模块的统计数据
    const [accountingRes, taskRes] = await Promise.all([
      this.loadAccountingStats(),
      this.loadTaskStats()
    ]);

    // 更新首页统计显示
    this.updateStatsDisplay(accountingRes, taskRes);
  } catch (error) {
    console.error('加载统计数据失败:', error);
    // 使用默认数据
    this.setDefaultStats();
  }
}
```

### updateStatsDisplay()
更新页面显示数据，将接口返回的数据映射到页面显示字段：
```javascript
updateStatsDisplay(accountingData, taskData) {
  // 更新今日统计显示
  this.todayStats = {
    memories: 3, // 记忆模块暂时保持默认值
    tasks: taskData?.unfinishedTasks || 0,
    accounts: accountingData?.totalRecords || 0
  };

  // 更新各模块计数
  this.taskCount = taskData?.totalTasks || 0;
  this.accountingAmount = accountingData?.monthExpense || 0;
}
```

### formatAmount()
格式化金额显示，确保显示格式的一致性：
```javascript
formatAmount(amount) {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00';
  }

  // 确保是数字类型
  const num = Number(amount);

  // 格式化为两位小数
  return num.toFixed(2);
}
```

## 数据结构更新

### taskStats 对象
```javascript
taskStats: {
  pending: 8,    // 未完成 (unfinishedTasks)
  completed: 12, // 已完成 (completedTasks)
  priority: 3,   // 高和紧急级别 (highUrgentTasks)
  urgent: 2,     // 2天内到期 (expiringSoonTasks)
  total: 20      // 总数 (totalTasks)
}
```

### accountingStats 对象
```javascript
accountingStats: {
  monthExpense: 2580,     // 本月支出 (monthExpense)
  monthIncome: 8200,      // 本月收入 (monthIncome)
  totalRecords: 156       // 记账总数 (totalRecords)
}
```

## 错误处理机制

1. **网络请求失败处理**: 当接口调用失败时，自动使用默认数据，保证页面正常显示
2. **数据格式容错**: 对接口返回的数据进行空值检查和类型转换
3. **用户反馈**: 可选的错误提示机制（已注释，可根据需要启用）

## 性能优化

1. **并行请求**: 使用 `Promise.all()` 同时调用两个接口，减少加载时间
2. **数据缓存**: 在页面显示期间缓存数据，避免重复请求
3. **自动刷新**: 在 `onShow()` 生命周期中自动刷新数据，保证数据实时性

## 手动刷新功能

添加了 `refreshStats()` 方法，支持用户手动刷新统计数据：
```javascript
async refreshStats() {
  try {
    uni.showLoading({ title: '正在刷新...' });
    await this.loadStats();
    uni.hideLoading();
    uni.showToast({ title: '刷新成功', icon: 'success' });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({ title: '刷新失败', icon: 'error' });
  }
}
```

## 页面显示更新

1. **待办统计标签更新**:
   - "优先" → "高紧急"
   - "临期" → "2天内到期"

2. **金额显示优化**:
   - 使用 `formatAmount()` 方法统一格式化
   - 支持小数点显示
   - 自动处理空值和异常值

## 使用说明

1. 页面加载时会自动调用统计接口获取最新数据
2. 每次返回页面时会重新刷新数据
3. 如果接口调用失败，会显示默认数据确保页面可用
4. 金额显示自动格式化为两位小数

## 注意事项

1. 确保后端接口按照文档规范实现
2. 接口需要支持用户鉴权
3. 返回的数据格式必须与文档一致
4. 建议在生产环境中启用错误提示机制